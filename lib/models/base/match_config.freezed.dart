// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchConfig {
  GameTypeId get gameId;
  @Deprecated('determined based on PlayerTypes?')
  GameMode get selectedGameMode;
  PlayerId get hostId;
  @GenerateIdIfNeededConverter()
  GameMatchId get matchId;
  List<Player> get players;
  String? get name;

  /// Create a copy of MatchConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchConfigCopyWith<MatchConfig> get copyWith =>
      _$MatchConfigCopyWithImpl<MatchConfig>(this as MatchConfig, _$identity);

  /// Serializes this MatchConfig to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchConfig &&
            (identical(other.gameId, gameId) || other.gameId == gameId) &&
            (identical(other.selectedGameMode, selectedGameMode) ||
                other.selectedGameMode == selectedGameMode) &&
            (identical(other.hostId, hostId) || other.hostId == hostId) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality().equals(other.players, players) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameId, selectedGameMode, hostId,
      matchId, const DeepCollectionEquality().hash(players), name);

  @override
  String toString() {
    return 'MatchConfig(gameId: $gameId, selectedGameMode: $selectedGameMode, hostId: $hostId, matchId: $matchId, players: $players, name: $name)';
  }
}

/// @nodoc
abstract mixin class $MatchConfigCopyWith<$Res> {
  factory $MatchConfigCopyWith(
          MatchConfig value, $Res Function(MatchConfig) _then) =
      _$MatchConfigCopyWithImpl;
  @useResult
  $Res call(
      {GameTypeId gameId,
      @Deprecated('determined based on PlayerTypes?') GameMode selectedGameMode,
      PlayerId hostId,
      @GenerateIdIfNeededConverter() GameMatchId matchId,
      List<Player> players,
      String? name});
}

/// @nodoc
class _$MatchConfigCopyWithImpl<$Res> implements $MatchConfigCopyWith<$Res> {
  _$MatchConfigCopyWithImpl(this._self, this._then);

  final MatchConfig _self;
  final $Res Function(MatchConfig) _then;

  /// Create a copy of MatchConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameId = null,
    Object? selectedGameMode = null,
    Object? hostId = null,
    Object? matchId = null,
    Object? players = null,
    Object? name = freezed,
  }) {
    return _then(_self.copyWith(
      gameId: null == gameId
          ? _self.gameId
          : gameId // ignore: cast_nullable_to_non_nullable
              as GameTypeId,
      selectedGameMode: null == selectedGameMode
          ? _self.selectedGameMode
          : selectedGameMode // ignore: cast_nullable_to_non_nullable
              as GameMode,
      hostId: null == hostId
          ? _self.hostId
          : hostId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as GameMatchId,
      players: null == players
          ? _self.players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchConfig implements MatchConfig {
  const _MatchConfig(
      {required this.gameId,
      @Deprecated('determined based on PlayerTypes?')
      required this.selectedGameMode,
      required this.hostId,
      @GenerateIdIfNeededConverter() required this.matchId,
      final List<Player> players = const [],
      this.name})
      : _players = players;
  factory _MatchConfig.fromJson(Map<String, dynamic> json) =>
      _$MatchConfigFromJson(json);

  @override
  final GameTypeId gameId;
  @override
  @Deprecated('determined based on PlayerTypes?')
  final GameMode selectedGameMode;
  @override
  final PlayerId hostId;
  @override
  @GenerateIdIfNeededConverter()
  final GameMatchId matchId;
  final List<Player> _players;
  @override
  @JsonKey()
  List<Player> get players {
    if (_players is EqualUnmodifiableListView) return _players;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_players);
  }

  @override
  final String? name;

  /// Create a copy of MatchConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchConfigCopyWith<_MatchConfig> get copyWith =>
      __$MatchConfigCopyWithImpl<_MatchConfig>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchConfigToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchConfig &&
            (identical(other.gameId, gameId) || other.gameId == gameId) &&
            (identical(other.selectedGameMode, selectedGameMode) ||
                other.selectedGameMode == selectedGameMode) &&
            (identical(other.hostId, hostId) || other.hostId == hostId) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality().equals(other._players, _players) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameId, selectedGameMode, hostId,
      matchId, const DeepCollectionEquality().hash(_players), name);

  @override
  String toString() {
    return 'MatchConfig(gameId: $gameId, selectedGameMode: $selectedGameMode, hostId: $hostId, matchId: $matchId, players: $players, name: $name)';
  }
}

/// @nodoc
abstract mixin class _$MatchConfigCopyWith<$Res>
    implements $MatchConfigCopyWith<$Res> {
  factory _$MatchConfigCopyWith(
          _MatchConfig value, $Res Function(_MatchConfig) _then) =
      __$MatchConfigCopyWithImpl;
  @override
  @useResult
  $Res call(
      {GameTypeId gameId,
      @Deprecated('determined based on PlayerTypes?') GameMode selectedGameMode,
      PlayerId hostId,
      @GenerateIdIfNeededConverter() GameMatchId matchId,
      List<Player> players,
      String? name});
}

/// @nodoc
class __$MatchConfigCopyWithImpl<$Res> implements _$MatchConfigCopyWith<$Res> {
  __$MatchConfigCopyWithImpl(this._self, this._then);

  final _MatchConfig _self;
  final $Res Function(_MatchConfig) _then;

  /// Create a copy of MatchConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameId = null,
    Object? selectedGameMode = null,
    Object? hostId = null,
    Object? matchId = null,
    Object? players = null,
    Object? name = freezed,
  }) {
    return _then(_MatchConfig(
      gameId: null == gameId
          ? _self.gameId
          : gameId // ignore: cast_nullable_to_non_nullable
              as GameTypeId,
      selectedGameMode: null == selectedGameMode
          ? _self.selectedGameMode
          : selectedGameMode // ignore: cast_nullable_to_non_nullable
              as GameMode,
      hostId: null == hostId
          ? _self.hostId
          : hostId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as GameMatchId,
      players: null == players
          ? _self._players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
