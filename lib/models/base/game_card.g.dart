// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_card.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GameCard _$GameCardFromJson(Map<String, dynamic> json) => _GameCard(
      id: const GenerateIdIfNeededConverter().fromJson(json['id']),
      name: json['name'] as String,
      type: $enumDecode(_$CardTypeEnumMap, json['type']),
      classId: json['classId'] as String,
      locationId: json['locationId'] as String?,
      imgRefs: (json['imgRefs'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const <String, String>{},
      activeActions: (json['activeActions'] as List<dynamic>?)
              ?.map((e) => TargetedAction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$GameCardToJson(_GameCard instance) => <String, dynamic>{
      'id': const GenerateIdIfNeededConverter().toJson(instance.id),
      'name': instance.name,
      'type': _$CardTypeEnumMap[instance.type]!,
      'classId': instance.classId,
      'locationId': instance.locationId,
      'imgRefs': instance.imgRefs,
      'activeActions': instance.activeActions,
    };

const _$CardTypeEnumMap = {
  CardType.character: 'character',
  CardType.location: 'location',
  CardType.building: 'building',
  CardType.vehicle: 'vehicle',
  CardType.grouping: 'grouping',
};
