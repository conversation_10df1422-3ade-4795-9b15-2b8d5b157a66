import 'package:common/models/game.dart';
import 'package:common/models/player_class.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'game_config.freezed.dart';
part 'game_config.g.dart';

// enum GameMode { hotSeat }

@freezed
abstract class GameConfig with _$GameConfig {
  const GameConfig._();

  PlayerClass? getPlayerClass(String id) {
    return playerClasses.firstWhere((element) => element.id == id);
  }

  const factory GameConfig({
    required GameTypeId id,
    required String name,
    @Default([]) List<PlayerClass> playerClasses,
    // @Default([GameMode.hotSeat]) List<GameMode> supportedModes,
  }) = _GameConfig;

  factory GameConfig.fromJson(Map<String, dynamic> json) =>
      _$GameConfigFromJson(json);
}