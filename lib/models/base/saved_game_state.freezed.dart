// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'saved_game_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SavedGameState {
  GameMatchState get matchState;

  /// Create a copy of SavedGameState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SavedGameStateCopyWith<SavedGameState> get copyWith =>
      _$SavedGameStateCopyWithImpl<SavedGameState>(
          this as SavedGameState, _$identity);

  /// Serializes this SavedGameState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SavedGameState &&
            (identical(other.matchState, matchState) ||
                other.matchState == matchState));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchState);

  @override
  String toString() {
    return 'SavedGameState(matchState: $matchState)';
  }
}

/// @nodoc
abstract mixin class $SavedGameStateCopyWith<$Res> {
  factory $SavedGameStateCopyWith(
          SavedGameState value, $Res Function(SavedGameState) _then) =
      _$SavedGameStateCopyWithImpl;
  @useResult
  $Res call({GameMatchState matchState});

  $GameMatchStateCopyWith<$Res> get matchState;
}

/// @nodoc
class _$SavedGameStateCopyWithImpl<$Res>
    implements $SavedGameStateCopyWith<$Res> {
  _$SavedGameStateCopyWithImpl(this._self, this._then);

  final SavedGameState _self;
  final $Res Function(SavedGameState) _then;

  /// Create a copy of SavedGameState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchState = null,
  }) {
    return _then(_self.copyWith(
      matchState: null == matchState
          ? _self.matchState
          : matchState // ignore: cast_nullable_to_non_nullable
              as GameMatchState,
    ));
  }

  /// Create a copy of SavedGameState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchStateCopyWith<$Res> get matchState {
    return $GameMatchStateCopyWith<$Res>(_self.matchState, (value) {
      return _then(_self.copyWith(matchState: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _SavedGameState implements SavedGameState {
  const _SavedGameState({required this.matchState});
  factory _SavedGameState.fromJson(Map<String, dynamic> json) =>
      _$SavedGameStateFromJson(json);

  @override
  final GameMatchState matchState;

  /// Create a copy of SavedGameState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SavedGameStateCopyWith<_SavedGameState> get copyWith =>
      __$SavedGameStateCopyWithImpl<_SavedGameState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SavedGameStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SavedGameState &&
            (identical(other.matchState, matchState) ||
                other.matchState == matchState));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchState);

  @override
  String toString() {
    return 'SavedGameState(matchState: $matchState)';
  }
}

/// @nodoc
abstract mixin class _$SavedGameStateCopyWith<$Res>
    implements $SavedGameStateCopyWith<$Res> {
  factory _$SavedGameStateCopyWith(
          _SavedGameState value, $Res Function(_SavedGameState) _then) =
      __$SavedGameStateCopyWithImpl;
  @override
  @useResult
  $Res call({GameMatchState matchState});

  @override
  $GameMatchStateCopyWith<$Res> get matchState;
}

/// @nodoc
class __$SavedGameStateCopyWithImpl<$Res>
    implements _$SavedGameStateCopyWith<$Res> {
  __$SavedGameStateCopyWithImpl(this._self, this._then);

  final _SavedGameState _self;
  final $Res Function(_SavedGameState) _then;

  /// Create a copy of SavedGameState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchState = null,
  }) {
    return _then(_SavedGameState(
      matchState: null == matchState
          ? _self.matchState
          : matchState // ignore: cast_nullable_to_non_nullable
              as GameMatchState,
    ));
  }

  /// Create a copy of SavedGameState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchStateCopyWith<$Res> get matchState {
    return $GameMatchStateCopyWith<$Res>(_self.matchState, (value) {
      return _then(_self.copyWith(matchState: value));
    });
  }
}

// dart format on
