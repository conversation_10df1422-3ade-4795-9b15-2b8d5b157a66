// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'targeted_action.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TargetedAction {
  @GenerateIdIfNeededConverter()
  BaseId get id;
  CardAction get action;
  GameCardId get subjectCardId;
  Map<String, dynamic> get reconciledAttributes;
  int get remainingTurns;
  LocationId? get subjectLocationId;
  GameCardId? get objectCardId;
  CardClassId? get objectCardClassId;

  /// Create a copy of TargetedAction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TargetedActionCopyWith<TargetedAction> get copyWith =>
      _$TargetedActionCopyWithImpl<TargetedAction>(
          this as TargetedAction, _$identity);

  /// Serializes this TargetedAction to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TargetedAction &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.subjectCardId, subjectCardId) ||
                other.subjectCardId == subjectCardId) &&
            const DeepCollectionEquality()
                .equals(other.reconciledAttributes, reconciledAttributes) &&
            (identical(other.remainingTurns, remainingTurns) ||
                other.remainingTurns == remainingTurns) &&
            (identical(other.subjectLocationId, subjectLocationId) ||
                other.subjectLocationId == subjectLocationId) &&
            (identical(other.objectCardId, objectCardId) ||
                other.objectCardId == objectCardId) &&
            (identical(other.objectCardClassId, objectCardClassId) ||
                other.objectCardClassId == objectCardClassId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      action,
      subjectCardId,
      const DeepCollectionEquality().hash(reconciledAttributes),
      remainingTurns,
      subjectLocationId,
      objectCardId,
      objectCardClassId);

  @override
  String toString() {
    return 'TargetedAction(id: $id, action: $action, subjectCardId: $subjectCardId, reconciledAttributes: $reconciledAttributes, remainingTurns: $remainingTurns, subjectLocationId: $subjectLocationId, objectCardId: $objectCardId, objectCardClassId: $objectCardClassId)';
  }
}

/// @nodoc
abstract mixin class $TargetedActionCopyWith<$Res> {
  factory $TargetedActionCopyWith(
          TargetedAction value, $Res Function(TargetedAction) _then) =
      _$TargetedActionCopyWithImpl;
  @useResult
  $Res call(
      {@GenerateIdIfNeededConverter() BaseId id,
      CardAction action,
      GameCardId subjectCardId,
      Map<String, dynamic> reconciledAttributes,
      int remainingTurns,
      LocationId? subjectLocationId,
      GameCardId? objectCardId,
      CardClassId? objectCardClassId});

  $CardActionCopyWith<$Res> get action;
}

/// @nodoc
class _$TargetedActionCopyWithImpl<$Res>
    implements $TargetedActionCopyWith<$Res> {
  _$TargetedActionCopyWithImpl(this._self, this._then);

  final TargetedAction _self;
  final $Res Function(TargetedAction) _then;

  /// Create a copy of TargetedAction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? action = null,
    Object? subjectCardId = null,
    Object? reconciledAttributes = null,
    Object? remainingTurns = null,
    Object? subjectLocationId = freezed,
    Object? objectCardId = freezed,
    Object? objectCardClassId = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as BaseId,
      action: null == action
          ? _self.action
          : action // ignore: cast_nullable_to_non_nullable
              as CardAction,
      subjectCardId: null == subjectCardId
          ? _self.subjectCardId
          : subjectCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      reconciledAttributes: null == reconciledAttributes
          ? _self.reconciledAttributes
          : reconciledAttributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      remainingTurns: null == remainingTurns
          ? _self.remainingTurns
          : remainingTurns // ignore: cast_nullable_to_non_nullable
              as int,
      subjectLocationId: freezed == subjectLocationId
          ? _self.subjectLocationId
          : subjectLocationId // ignore: cast_nullable_to_non_nullable
              as LocationId?,
      objectCardId: freezed == objectCardId
          ? _self.objectCardId
          : objectCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      objectCardClassId: freezed == objectCardClassId
          ? _self.objectCardClassId
          : objectCardClassId // ignore: cast_nullable_to_non_nullable
              as CardClassId?,
    ));
  }

  /// Create a copy of TargetedAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CardActionCopyWith<$Res> get action {
    return $CardActionCopyWith<$Res>(_self.action, (value) {
      return _then(_self.copyWith(action: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
@Deprecated('you probably want to use the genId constructor')
class _TargetedAction implements TargetedAction {
  _TargetedAction(
      {@GenerateIdIfNeededConverter() required this.id,
      required this.action,
      required this.subjectCardId,
      final Map<String, dynamic> reconciledAttributes = const {},
      this.remainingTurns = 0,
      this.subjectLocationId,
      this.objectCardId,
      this.objectCardClassId})
      : _reconciledAttributes = reconciledAttributes;
  factory _TargetedAction.fromJson(Map<String, dynamic> json) =>
      _$TargetedActionFromJson(json);

  @override
  @GenerateIdIfNeededConverter()
  final BaseId id;
  @override
  final CardAction action;
  @override
  final GameCardId subjectCardId;
  final Map<String, dynamic> _reconciledAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get reconciledAttributes {
    if (_reconciledAttributes is EqualUnmodifiableMapView)
      return _reconciledAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_reconciledAttributes);
  }

  @override
  @JsonKey()
  final int remainingTurns;
  @override
  final LocationId? subjectLocationId;
  @override
  final GameCardId? objectCardId;
  @override
  final CardClassId? objectCardClassId;

  /// Create a copy of TargetedAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TargetedActionCopyWith<_TargetedAction> get copyWith =>
      __$TargetedActionCopyWithImpl<_TargetedAction>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TargetedActionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TargetedAction &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.subjectCardId, subjectCardId) ||
                other.subjectCardId == subjectCardId) &&
            const DeepCollectionEquality()
                .equals(other._reconciledAttributes, _reconciledAttributes) &&
            (identical(other.remainingTurns, remainingTurns) ||
                other.remainingTurns == remainingTurns) &&
            (identical(other.subjectLocationId, subjectLocationId) ||
                other.subjectLocationId == subjectLocationId) &&
            (identical(other.objectCardId, objectCardId) ||
                other.objectCardId == objectCardId) &&
            (identical(other.objectCardClassId, objectCardClassId) ||
                other.objectCardClassId == objectCardClassId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      action,
      subjectCardId,
      const DeepCollectionEquality().hash(_reconciledAttributes),
      remainingTurns,
      subjectLocationId,
      objectCardId,
      objectCardClassId);

  @override
  String toString() {
    return 'TargetedAction(id: $id, action: $action, subjectCardId: $subjectCardId, reconciledAttributes: $reconciledAttributes, remainingTurns: $remainingTurns, subjectLocationId: $subjectLocationId, objectCardId: $objectCardId, objectCardClassId: $objectCardClassId)';
  }
}

/// @nodoc
abstract mixin class _$TargetedActionCopyWith<$Res>
    implements $TargetedActionCopyWith<$Res> {
  factory _$TargetedActionCopyWith(
          _TargetedAction value, $Res Function(_TargetedAction) _then) =
      __$TargetedActionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@GenerateIdIfNeededConverter() BaseId id,
      CardAction action,
      GameCardId subjectCardId,
      Map<String, dynamic> reconciledAttributes,
      int remainingTurns,
      LocationId? subjectLocationId,
      GameCardId? objectCardId,
      CardClassId? objectCardClassId});

  @override
  $CardActionCopyWith<$Res> get action;
}

/// @nodoc
class __$TargetedActionCopyWithImpl<$Res>
    implements _$TargetedActionCopyWith<$Res> {
  __$TargetedActionCopyWithImpl(this._self, this._then);

  final _TargetedAction _self;
  final $Res Function(_TargetedAction) _then;

  /// Create a copy of TargetedAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? action = null,
    Object? subjectCardId = null,
    Object? reconciledAttributes = null,
    Object? remainingTurns = null,
    Object? subjectLocationId = freezed,
    Object? objectCardId = freezed,
    Object? objectCardClassId = freezed,
  }) {
    return _then(_TargetedAction(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as BaseId,
      action: null == action
          ? _self.action
          : action // ignore: cast_nullable_to_non_nullable
              as CardAction,
      subjectCardId: null == subjectCardId
          ? _self.subjectCardId
          : subjectCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      reconciledAttributes: null == reconciledAttributes
          ? _self._reconciledAttributes
          : reconciledAttributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      remainingTurns: null == remainingTurns
          ? _self.remainingTurns
          : remainingTurns // ignore: cast_nullable_to_non_nullable
              as int,
      subjectLocationId: freezed == subjectLocationId
          ? _self.subjectLocationId
          : subjectLocationId // ignore: cast_nullable_to_non_nullable
              as LocationId?,
      objectCardId: freezed == objectCardId
          ? _self.objectCardId
          : objectCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      objectCardClassId: freezed == objectCardClassId
          ? _self.objectCardClassId
          : objectCardClassId // ignore: cast_nullable_to_non_nullable
              as CardClassId?,
    ));
  }

  /// Create a copy of TargetedAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CardActionCopyWith<$Res> get action {
    return $CardActionCopyWith<$Res>(_self.action, (value) {
      return _then(_self.copyWith(action: value));
    });
  }
}

// dart format on
