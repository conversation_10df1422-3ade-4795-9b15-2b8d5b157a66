import 'dart:math';

import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vector_math/vector_math_64.dart';

import 'game_card_class.dart';
import 'mapping/positioned_card_2d.dart';

part 'game_card.freezed.dart';

part 'game_card.g.dart';

typedef BaseId = String;

typedef GameCardId = BaseId;
typedef CardClassId = BaseId;

const CardClassId groupClassId = 'group';

typedef LocationId = BaseId;

extension MaxMinGridExtents on Matrix2 {
  double get minX => getColumn(0).x;

  double get minY => getColumn(0).y;

  double get maxX => getColumn(1).x;

  double get maxY => getColumn(1).y;

  double get xExtent => maxX - minX;

  double get yExtent => maxY - minY;

  static GridExtents create(
          double minX, double minY, double maxX, double maxY) =>
      GridExtents.columns(Vector2(minX, minY), Vector2(maxX, maxY));

  double getScaleFactor(GridExtents screenExtents) {
    final (xFactor, yFactor) = getScaleFactors(screenExtents);
    return min(xFactor, yFactor);
  }

  (double, double) getScaleFactors(GridExtents screenExtents) {
    // final double xScaleFactor = screenExtents.maxX / maxX;
    // final double yScaleFactor = screenExtents.maxY / maxY;
    final double xScaleFactor = screenExtents.xExtent / xExtent;
    final double yScaleFactor = screenExtents.yExtent / yExtent;
    return (xScaleFactor, yScaleFactor);
  }
}

const LocationId inTransitLocationId = 'inTransit';

enum CardType {
  character,
  location,
  building,
  vehicle,
  grouping;

  bool get isUnique => this == CardType.location;

  String get groupingName {
    switch (this) {
      case CardType.character:
        return 'Crew';
      case CardType.location:
        return 'Sector';
      case CardType.building:
        return 'Buildings';
      case CardType.vehicle:
        return 'Fleet';
      case CardType.grouping:
        return 'Groupings';
    }
  }

  static CardType fromJson(String json) {
    switch (json) {
      case 'character':
        return CardType.character;
      case 'location':
        return CardType.location;
      case 'vehicle':
        return CardType.vehicle;
      case 'building':
        return CardType.building;
      case 'grouping':
        return CardType.grouping;
      default:
        throw Exception('Invalid type');
    }
  }
}

@freezed
abstract class GameCard with _$GameCard {
  const GameCard._();

  bool get isSingleton => id == classId;

  const factory GameCard({
    @GenerateIdIfNeededConverter() required GameCardId id,
    required String name,

    // TODO: this & classId will always match -- we could get this from the class ... or should the class be a parameter here? a getter on MatchState or something?
    required CardType type,

    /// = 'groupClassId' for groupings
    required CardClassId classId,
    LocationId? locationId,

    // TODO: clearly define class-based attributes vs variable instance attributes
    // @Deprecated(
    //     'handled at the class level ... but maybe this is where we store something like currrent health')
    // @Default({})
    // Map<String, dynamic> attributes,

    // TODO: handle these entirely in the AssetsManager/UseCase/Ref ... diff model
    @Deprecated('handled at the class level')
    @Default(<String, String>{})
    Map<String, String> imgRefs,

    // TODO: should these be on the card or in an Actions with relationships
    @Default([]) List<TargetedAction> activeActions,
  }) = _GameCard;

  factory GameCard.fromJson(Map<String, dynamic> json) =>
      _$GameCardFromJson(json);

  static GameCard? fromGrouping(CardGrouping grouping) => GameCard(
        id: grouping.groupingId,
        name: grouping.name,
        type: CardType.location,
        classId: grouping.groupingId,
        locationId: grouping.groupingId,
      );

  static GameCard fromGameCardClass(
    GameCardClass cardClass, {
    GameCardId? id,
    String? name,
    LocationId? locationId,
    Map<String, dynamic>? attributes,
    Map<String, String>? imgRefs,
    List<TargetedAction>? activeActions,
  }) =>
      GameCard(
        id: id ?? const GenerateIdIfNeededConverter().fromJson(),
        name: name ?? cardClass.name,
        type: cardClass.type,
        classId: cardClass.id,
        locationId: locationId,
        // attributes: attributes ?? cardClass.attributes,
        imgRefs: imgRefs ?? cardClass.imgRefs,
      );
}
