// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card_action.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CardAction {
  String get id;
  String get name;
  ActionType get type;
  List<CardType> get targets;
  List<ActionTargetFilter> get targetFilters;
  List<ActionSubjectFilter> get subjectFilters;
  List<ActionAttribute> get actionAttributes;
  List<String> get targetRelevantAttributes;

  /// Create a copy of CardAction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardActionCopyWith<CardAction> get copyWith =>
      _$CardActionCopyWithImpl<CardAction>(this as CardAction, _$identity);

  /// Serializes this CardAction to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardAction &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other.targets, targets) &&
            const DeepCollectionEquality()
                .equals(other.targetFilters, targetFilters) &&
            const DeepCollectionEquality()
                .equals(other.subjectFilters, subjectFilters) &&
            const DeepCollectionEquality()
                .equals(other.actionAttributes, actionAttributes) &&
            const DeepCollectionEquality().equals(
                other.targetRelevantAttributes, targetRelevantAttributes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      const DeepCollectionEquality().hash(targets),
      const DeepCollectionEquality().hash(targetFilters),
      const DeepCollectionEquality().hash(subjectFilters),
      const DeepCollectionEquality().hash(actionAttributes),
      const DeepCollectionEquality().hash(targetRelevantAttributes));

  @override
  String toString() {
    return 'CardAction(id: $id, name: $name, type: $type, targets: $targets, targetFilters: $targetFilters, subjectFilters: $subjectFilters, actionAttributes: $actionAttributes, targetRelevantAttributes: $targetRelevantAttributes)';
  }
}

/// @nodoc
abstract mixin class $CardActionCopyWith<$Res> {
  factory $CardActionCopyWith(
          CardAction value, $Res Function(CardAction) _then) =
      _$CardActionCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String name,
      ActionType type,
      List<CardType> targets,
      List<ActionTargetFilter> targetFilters,
      List<ActionSubjectFilter> subjectFilters,
      List<ActionAttribute> actionAttributes,
      List<String> targetRelevantAttributes});
}

/// @nodoc
class _$CardActionCopyWithImpl<$Res> implements $CardActionCopyWith<$Res> {
  _$CardActionCopyWithImpl(this._self, this._then);

  final CardAction _self;
  final $Res Function(CardAction) _then;

  /// Create a copy of CardAction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? targets = null,
    Object? targetFilters = null,
    Object? subjectFilters = null,
    Object? actionAttributes = null,
    Object? targetRelevantAttributes = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as ActionType,
      targets: null == targets
          ? _self.targets
          : targets // ignore: cast_nullable_to_non_nullable
              as List<CardType>,
      targetFilters: null == targetFilters
          ? _self.targetFilters
          : targetFilters // ignore: cast_nullable_to_non_nullable
              as List<ActionTargetFilter>,
      subjectFilters: null == subjectFilters
          ? _self.subjectFilters
          : subjectFilters // ignore: cast_nullable_to_non_nullable
              as List<ActionSubjectFilter>,
      actionAttributes: null == actionAttributes
          ? _self.actionAttributes
          : actionAttributes // ignore: cast_nullable_to_non_nullable
              as List<ActionAttribute>,
      targetRelevantAttributes: null == targetRelevantAttributes
          ? _self.targetRelevantAttributes
          : targetRelevantAttributes // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CardAction implements CardAction {
  const _CardAction(
      {required this.id,
      required this.name,
      required this.type,
      final List<CardType> targets = const [],
      final List<ActionTargetFilter> targetFilters = const [],
      final List<ActionSubjectFilter> subjectFilters = const [],
      final List<ActionAttribute> actionAttributes = const [],
      final List<String> targetRelevantAttributes = const []})
      : _targets = targets,
        _targetFilters = targetFilters,
        _subjectFilters = subjectFilters,
        _actionAttributes = actionAttributes,
        _targetRelevantAttributes = targetRelevantAttributes;
  factory _CardAction.fromJson(Map<String, dynamic> json) =>
      _$CardActionFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final ActionType type;
  final List<CardType> _targets;
  @override
  @JsonKey()
  List<CardType> get targets {
    if (_targets is EqualUnmodifiableListView) return _targets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_targets);
  }

  final List<ActionTargetFilter> _targetFilters;
  @override
  @JsonKey()
  List<ActionTargetFilter> get targetFilters {
    if (_targetFilters is EqualUnmodifiableListView) return _targetFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_targetFilters);
  }

  final List<ActionSubjectFilter> _subjectFilters;
  @override
  @JsonKey()
  List<ActionSubjectFilter> get subjectFilters {
    if (_subjectFilters is EqualUnmodifiableListView) return _subjectFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subjectFilters);
  }

  final List<ActionAttribute> _actionAttributes;
  @override
  @JsonKey()
  List<ActionAttribute> get actionAttributes {
    if (_actionAttributes is EqualUnmodifiableListView)
      return _actionAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_actionAttributes);
  }

  final List<String> _targetRelevantAttributes;
  @override
  @JsonKey()
  List<String> get targetRelevantAttributes {
    if (_targetRelevantAttributes is EqualUnmodifiableListView)
      return _targetRelevantAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_targetRelevantAttributes);
  }

  /// Create a copy of CardAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CardActionCopyWith<_CardAction> get copyWith =>
      __$CardActionCopyWithImpl<_CardAction>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CardActionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CardAction &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._targets, _targets) &&
            const DeepCollectionEquality()
                .equals(other._targetFilters, _targetFilters) &&
            const DeepCollectionEquality()
                .equals(other._subjectFilters, _subjectFilters) &&
            const DeepCollectionEquality()
                .equals(other._actionAttributes, _actionAttributes) &&
            const DeepCollectionEquality().equals(
                other._targetRelevantAttributes, _targetRelevantAttributes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      const DeepCollectionEquality().hash(_targets),
      const DeepCollectionEquality().hash(_targetFilters),
      const DeepCollectionEquality().hash(_subjectFilters),
      const DeepCollectionEquality().hash(_actionAttributes),
      const DeepCollectionEquality().hash(_targetRelevantAttributes));

  @override
  String toString() {
    return 'CardAction(id: $id, name: $name, type: $type, targets: $targets, targetFilters: $targetFilters, subjectFilters: $subjectFilters, actionAttributes: $actionAttributes, targetRelevantAttributes: $targetRelevantAttributes)';
  }
}

/// @nodoc
abstract mixin class _$CardActionCopyWith<$Res>
    implements $CardActionCopyWith<$Res> {
  factory _$CardActionCopyWith(
          _CardAction value, $Res Function(_CardAction) _then) =
      __$CardActionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      ActionType type,
      List<CardType> targets,
      List<ActionTargetFilter> targetFilters,
      List<ActionSubjectFilter> subjectFilters,
      List<ActionAttribute> actionAttributes,
      List<String> targetRelevantAttributes});
}

/// @nodoc
class __$CardActionCopyWithImpl<$Res> implements _$CardActionCopyWith<$Res> {
  __$CardActionCopyWithImpl(this._self, this._then);

  final _CardAction _self;
  final $Res Function(_CardAction) _then;

  /// Create a copy of CardAction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? targets = null,
    Object? targetFilters = null,
    Object? subjectFilters = null,
    Object? actionAttributes = null,
    Object? targetRelevantAttributes = null,
  }) {
    return _then(_CardAction(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as ActionType,
      targets: null == targets
          ? _self._targets
          : targets // ignore: cast_nullable_to_non_nullable
              as List<CardType>,
      targetFilters: null == targetFilters
          ? _self._targetFilters
          : targetFilters // ignore: cast_nullable_to_non_nullable
              as List<ActionTargetFilter>,
      subjectFilters: null == subjectFilters
          ? _self._subjectFilters
          : subjectFilters // ignore: cast_nullable_to_non_nullable
              as List<ActionSubjectFilter>,
      actionAttributes: null == actionAttributes
          ? _self._actionAttributes
          : actionAttributes // ignore: cast_nullable_to_non_nullable
              as List<ActionAttribute>,
      targetRelevantAttributes: null == targetRelevantAttributes
          ? _self._targetRelevantAttributes
          : targetRelevantAttributes // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on
