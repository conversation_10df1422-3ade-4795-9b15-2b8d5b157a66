import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uuid/uuid.dart';

import 'card_action.dart';
import 'game_card.dart';

part 'targeted_action.freezed.dart';
part 'targeted_action.g.dart';

BaseId generateBaseId() => const Uuid().v4();

@freezed
abstract class TargetedAction with _$TargetedAction {
  @Deprecated('you probably want to use the genId constructor')
  factory TargetedAction({
    @GenerateIdIfNeededConverter() required BaseId id,
    required CardAction action,
    required GameCardId subjectCardId,
    @Default({}) Map<String, dynamic> reconciledAttributes,
    @Default(0) int remainingTurns,
    LocationId? subjectLocationId,
    GameCardId? objectCardId,
    CardClassId? objectCardClassId,
  }) = _TargetedAction;

  factory TargetedAction.genId({
    required CardAction action,
    required GameCardId subjectCardId,
    Map<String, dynamic> reconciledAttributes = const {},
    int remainingTurns = 0,
    LocationId? subjectLocationId,
    GameCardId? objectCardId,
    CardClassId? objectCardClassId,
  }) => TargetedAction(
    id: generateBaseId(),
    action: action,
    subjectCardId: subjectCardId,
    reconciledAttributes: reconciledAttributes,
    remainingTurns: remainingTurns,
    subjectLocationId: subjectLocationId,
    objectCardId: objectCardId,
    objectCardClassId: objectCardClassId,
  );

  factory TargetedAction.fromJson(Map<String, dynamic> json) =>
      _$TargetedActionFromJson(json);
}