// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'resource_value.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ResourceValue {
  ResourceId get id;
  double get value;

  /// Create a copy of ResourceValue
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ResourceValueCopyWith<ResourceValue> get copyWith =>
      _$ResourceValueCopyWithImpl<ResourceValue>(
          this as ResourceValue, _$identity);

  /// Serializes this ResourceValue to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ResourceValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, value);

  @override
  String toString() {
    return 'ResourceValue(id: $id, value: $value)';
  }
}

/// @nodoc
abstract mixin class $ResourceValueCopyWith<$Res> {
  factory $ResourceValueCopyWith(
          ResourceValue value, $Res Function(ResourceValue) _then) =
      _$ResourceValueCopyWithImpl;
  @useResult
  $Res call({ResourceId id, double value});
}

/// @nodoc
class _$ResourceValueCopyWithImpl<$Res>
    implements $ResourceValueCopyWith<$Res> {
  _$ResourceValueCopyWithImpl(this._self, this._then);

  final ResourceValue _self;
  final $Res Function(ResourceValue) _then;

  /// Create a copy of ResourceValue
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? value = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as ResourceId,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ResourceValue implements ResourceValue {
  const _ResourceValue({required this.id, this.value = 0.0});
  factory _ResourceValue.fromJson(Map<String, dynamic> json) =>
      _$ResourceValueFromJson(json);

  @override
  final ResourceId id;
  @override
  @JsonKey()
  final double value;

  /// Create a copy of ResourceValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ResourceValueCopyWith<_ResourceValue> get copyWith =>
      __$ResourceValueCopyWithImpl<_ResourceValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ResourceValueToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ResourceValue &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, value);

  @override
  String toString() {
    return 'ResourceValue(id: $id, value: $value)';
  }
}

/// @nodoc
abstract mixin class _$ResourceValueCopyWith<$Res>
    implements $ResourceValueCopyWith<$Res> {
  factory _$ResourceValueCopyWith(
          _ResourceValue value, $Res Function(_ResourceValue) _then) =
      __$ResourceValueCopyWithImpl;
  @override
  @useResult
  $Res call({ResourceId id, double value});
}

/// @nodoc
class __$ResourceValueCopyWithImpl<$Res>
    implements _$ResourceValueCopyWith<$Res> {
  __$ResourceValueCopyWithImpl(this._self, this._then);

  final _ResourceValue _self;
  final $Res Function(_ResourceValue) _then;

  /// Create a copy of ResourceValue
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? value = null,
  }) {
    return _then(_ResourceValue(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as ResourceId,
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

// dart format on
