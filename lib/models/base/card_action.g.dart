// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card_action.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CardAction _$CardActionFromJson(Map<String, dynamic> json) => _CardAction(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$ActionTypeEnumMap, json['type']),
      targets: (json['targets'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$CardTypeEnumMap, e))
              .toList() ??
          const [],
      targetFilters: (json['targetFilters'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$ActionTargetFilterEnumMap, e))
              .toList() ??
          const [],
      subjectFilters: (json['subjectFilters'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$ActionSubjectFilterEnumMap, e))
              .toList() ??
          const [],
      actionAttributes: (json['actionAttributes'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$ActionAttributeEnumMap, e))
              .toList() ??
          const [],
      targetRelevantAttributes:
          (json['targetRelevantAttributes'] as List<dynamic>?)
                  ?.map((e) => e as String)
                  .toList() ??
              const [],
    );

Map<String, dynamic> _$CardActionToJson(_CardAction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$ActionTypeEnumMap[instance.type]!,
      'targets': instance.targets.map((e) => _$CardTypeEnumMap[e]!).toList(),
      'targetFilters': instance.targetFilters
          .map((e) => _$ActionTargetFilterEnumMap[e]!)
          .toList(),
      'subjectFilters': instance.subjectFilters
          .map((e) => _$ActionSubjectFilterEnumMap[e]!)
          .toList(),
      'actionAttributes': instance.actionAttributes
          .map((e) => _$ActionAttributeEnumMap[e]!)
          .toList(),
      'targetRelevantAttributes': instance.targetRelevantAttributes,
    };

const _$ActionTypeEnumMap = {
  ActionType.move: 'move',
  ActionType.construct: 'construct',
  ActionType.changeGroup: 'changeGroup',
};

const _$CardTypeEnumMap = {
  CardType.character: 'character',
  CardType.location: 'location',
  CardType.building: 'building',
  CardType.vehicle: 'vehicle',
  CardType.grouping: 'grouping',
};

const _$ActionTargetFilterEnumMap = {
  ActionTargetFilter.currentLocation: 'currentLocation',
  ActionTargetFilter.sameType: 'sameType',
  ActionTargetFilter.notCurrentLocation: 'notCurrentLocation',
  ActionTargetFilter.notCurrentGrouping: 'notCurrentGrouping',
};

const _$ActionSubjectFilterEnumMap = {
  ActionSubjectFilter.notGrouping: 'notGrouping',
};

const _$ActionAttributeEnumMap = {
  ActionAttribute.immediateEffect: 'immediateEffect',
};
