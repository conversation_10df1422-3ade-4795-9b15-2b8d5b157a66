import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:common/models/game.dart';
import 'package:common/models/player.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_config.freezed.dart';
part 'match_config.g.dart';

enum GameMode { hotSeat, server }

@freezed
abstract class MatchConfig with _$MatchConfig {
  const factory MatchConfig({
    required GameTypeId gameId,
    @Deprecated('determined based on PlayerTypes?')
    required GameMode selectedGameMode,
    required PlayerId hostId,
    @GenerateIdIfNeededConverter() required GameMatchId matchId,
    @Default([]) List<Player> players,
    String? name,
  }) = _MatchConfig;

  factory MatchConfig.fromJson(Map<String, dynamic> json) =>
      _$MatchConfigFromJson(json);
}