// import 'dart:math';
//
// import '../deck.dart';
// import '../game_card.dart';
// import '../playerHand.dart';
//
// extension DeckExtensions on Deck {
//   // TODO: add parameters for only n number of cards, other things? handling unique/non-unique cards? methods of distribution?
//   // Map<Player, List<Card>> distributeCardsToPlayers(
//   Iterable<PlayerHand> distributeCardsToHands(
//       {required final Iterable<PlayerHand> hands}) {
//     Iterable<PlayerHand> handsForDistribution = hands;
//     while (cards.isNotEmpty) {
//       for (var hand in handsForDistribution) {
//         if (cards.isNotEmpty) {
//           // hand.cards.add(popTopCard());
//         }
//       }
//     }
//     return handsForDistribution;
//   }
//
//   // void distributeCardsToPlayers({required List<Player> players}) {
//   //   // Map<Player, List<Card>> cardsByPlayer = { for (var player in players) player : [] };
//   //   /// transfer cards into numDistributions of lists
//   //   // var random = Random();
//   //   // for (var i = cards.length - 1; i > 0; i--) {
//   //   //   var n = random.nextInt(i + 1);
//   //   //   var temp = cards[i];
//   //   //   cards[i] = cards[n];
//   //   //   cards[n] = temp;
//   //   // }
//   //
//   //   while (cards.isNotEmpty) {
//   //     for (var player in players) {
//   //       if (cards.isNotEmpty) {
//   //         // player.hand
//   //         player.hand.cards.add(cards.removeLast());
//   //       }
//   //     }
//   //   }
//   // }
//   //
//   // List<List<Card>> distributeCards({required int numDistributions}) {
//   //   /// transfer cards into numDistributions of lists
//   //   var random = Random();
//   //   for (var i = cards.length - 1; i > 0; i--) {
//   //     var n = random.nextInt(i + 1);
//   //     var temp = cards[i];
//   //     cards[i] = cards[n];
//   //     cards[n] = temp;
//   //   }
//   // }
//   //
//   void shuffle() {
//     var random = Random();
//     for (var i = cards.length - 1; i > 0; i--) {
//       var n = random.nextInt(i + 1);
//       var temp = cards[i];
//       cards[i] = cards[n];
//       cards[n] = temp;
//     }
//   }
// }
//
// // TODO: keep this?
// extension ShuffleCards on List<GameCard> {
//   shuffle() {
//     var random = Random();
//     for (var i = length - 1; i > 0; i--) {
//       var n = random.nextInt(i + 1);
//       var temp = this[i];
//       this[i] = this[n];
//       this[n] = temp;
//     }
//   }
// }
