import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vector_math/vector_math_64.dart';

import 'game_card.dart';

part 'game_card_class.freezed.dart';
part 'game_card_class.g.dart';

@freezed
abstract class GameCardClass with _$GameCardClass {
  const GameCardClass._();

  Vector2? get gridCoordinates => attributes['location']?['type'] == 'cartesian'
      ? Vector2(attributes['location']['x'].toDouble(),
      attributes['location']['y'].toDouble())
      : null;

  double? get moveSpeed => attributes['move_speed']?.toDouble();

  double getGridDistance(Vector2 other) {
    final gridDistanceVector = _getGridDistanceVector(other);
    if (gridDistanceVector == null) {
      return double.infinity;
    }
    return gridDistanceVector.length;
  }

  Vector2? _getGridDistanceVector(Vector2 other) {
    final gridCoordinates = this.gridCoordinates;
    if (gridCoordinates == null) {
      return null;
    }
    return other - gridCoordinates;
  }

  const factory GameCardClass({
    required CardClassId id,
    required String name,
    required CardType type,
    @Default({}) Map<String, dynamic> attributes,
    @Default(<String, String>{}) Map<String, String> imgRefs,
  }) = _GameCardClass;

  factory GameCardClass.fromJson(Map<String, dynamic> json) =>
      _$GameCardClassFromJson(json);
}
