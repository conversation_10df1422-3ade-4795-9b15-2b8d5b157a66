import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'saved_game_state.freezed.dart';
part 'saved_game_state.g.dart';

@freezed
abstract class SavedGameState with _$SavedGameState {
  const factory SavedGameState({
    required GameMatchState matchState,
  }) = _SavedGameState;

  factory SavedGameState.fromJson(Map<String, dynamic> json) =>
      _$SavedGameStateFromJson(json);
}