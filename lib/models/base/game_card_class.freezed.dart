// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_card_class.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GameCardClass {
  CardClassId get id;
  String get name;
  CardType get type;
  Map<String, dynamic> get attributes;
  Map<String, String> get imgRefs;

  /// Create a copy of GameCardClass
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GameCardClassCopyWith<GameCardClass> get copyWith =>
      _$GameCardClassCopyWithImpl<GameCardClass>(
          this as GameCardClass, _$identity);

  /// Serializes this GameCardClass to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GameCardClass &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality()
                .equals(other.attributes, attributes) &&
            const DeepCollectionEquality().equals(other.imgRefs, imgRefs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      const DeepCollectionEquality().hash(attributes),
      const DeepCollectionEquality().hash(imgRefs));

  @override
  String toString() {
    return 'GameCardClass(id: $id, name: $name, type: $type, attributes: $attributes, imgRefs: $imgRefs)';
  }
}

/// @nodoc
abstract mixin class $GameCardClassCopyWith<$Res> {
  factory $GameCardClassCopyWith(
          GameCardClass value, $Res Function(GameCardClass) _then) =
      _$GameCardClassCopyWithImpl;
  @useResult
  $Res call(
      {CardClassId id,
      String name,
      CardType type,
      Map<String, dynamic> attributes,
      Map<String, String> imgRefs});
}

/// @nodoc
class _$GameCardClassCopyWithImpl<$Res>
    implements $GameCardClassCopyWith<$Res> {
  _$GameCardClassCopyWithImpl(this._self, this._then);

  final GameCardClass _self;
  final $Res Function(GameCardClass) _then;

  /// Create a copy of GameCardClass
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? attributes = null,
    Object? imgRefs = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as CardClassId,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as CardType,
      attributes: null == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      imgRefs: null == imgRefs
          ? _self.imgRefs
          : imgRefs // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _GameCardClass extends GameCardClass {
  const _GameCardClass(
      {required this.id,
      required this.name,
      required this.type,
      final Map<String, dynamic> attributes = const {},
      final Map<String, String> imgRefs = const <String, String>{}})
      : _attributes = attributes,
        _imgRefs = imgRefs,
        super._();
  factory _GameCardClass.fromJson(Map<String, dynamic> json) =>
      _$GameCardClassFromJson(json);

  @override
  final CardClassId id;
  @override
  final String name;
  @override
  final CardType type;
  final Map<String, dynamic> _attributes;
  @override
  @JsonKey()
  Map<String, dynamic> get attributes {
    if (_attributes is EqualUnmodifiableMapView) return _attributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_attributes);
  }

  final Map<String, String> _imgRefs;
  @override
  @JsonKey()
  Map<String, String> get imgRefs {
    if (_imgRefs is EqualUnmodifiableMapView) return _imgRefs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_imgRefs);
  }

  /// Create a copy of GameCardClass
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GameCardClassCopyWith<_GameCardClass> get copyWith =>
      __$GameCardClassCopyWithImpl<_GameCardClass>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GameCardClassToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GameCardClass &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality()
                .equals(other._attributes, _attributes) &&
            const DeepCollectionEquality().equals(other._imgRefs, _imgRefs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      const DeepCollectionEquality().hash(_attributes),
      const DeepCollectionEquality().hash(_imgRefs));

  @override
  String toString() {
    return 'GameCardClass(id: $id, name: $name, type: $type, attributes: $attributes, imgRefs: $imgRefs)';
  }
}

/// @nodoc
abstract mixin class _$GameCardClassCopyWith<$Res>
    implements $GameCardClassCopyWith<$Res> {
  factory _$GameCardClassCopyWith(
          _GameCardClass value, $Res Function(_GameCardClass) _then) =
      __$GameCardClassCopyWithImpl;
  @override
  @useResult
  $Res call(
      {CardClassId id,
      String name,
      CardType type,
      Map<String, dynamic> attributes,
      Map<String, String> imgRefs});
}

/// @nodoc
class __$GameCardClassCopyWithImpl<$Res>
    implements _$GameCardClassCopyWith<$Res> {
  __$GameCardClassCopyWithImpl(this._self, this._then);

  final _GameCardClass _self;
  final $Res Function(_GameCardClass) _then;

  /// Create a copy of GameCardClass
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? attributes = null,
    Object? imgRefs = null,
  }) {
    return _then(_GameCardClass(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as CardClassId,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as CardType,
      attributes: null == attributes
          ? _self._attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      imgRefs: null == imgRefs
          ? _self._imgRefs
          : imgRefs // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
    ));
  }
}

// dart format on
