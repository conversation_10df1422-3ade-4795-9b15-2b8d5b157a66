import 'package:freezed_annotation/freezed_annotation.dart';

import 'game_card.dart';

part 'card_action.freezed.dart';

part 'card_action.g.dart';

enum ActionType {
  move,
  construct,
  changeGroup
}

enum ActionTargetFilter {
  currentLocation,
  // currentLocationGrouping,
  sameType,
  notCurrentLocation,
  notCurrentGrouping,
}

enum ActionSubjectFilter {
  notGrouping
}

enum ActionAttribute {
  immediateEffect
}

// enum ResourceType {
//   time,
//   money
// }

@freezed
abstract class CardAction with _$CardAction {
  const factory CardAction({
    required String id,
    required String name,
    required ActionType type,
    @Default([]) List<CardType> targets,
    @Default([]) List<ActionTargetFilter> targetFilters,
    @Default([]) List<ActionSubjectFilter> subjectFilters,
    @Default([]) List<ActionAttribute> actionAttributes,
    @Default([]) List<String> targetRelevantAttributes,
    // CardClassId? objectCardClassId,
    // @Default({}) Map<String, dynamic> targetedActionAttributes,
    // @Default(0) int turnsRequired
    // @Default({}) Map<ResourceType, int> costs,
  }) = _CardAction;

  factory CardAction.fromJson(Map<String, dynamic> json) =>
      _$CardActionFromJson(json);
}
