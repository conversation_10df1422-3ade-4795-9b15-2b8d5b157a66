// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_card.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GameCard {
  @GenerateIdIfNeededConverter()
  GameCardId get id;
  String
      get name; // TODO: this & classId will always match -- we could get this from the class ... or should the class be a parameter here? a getter on MatchState or something?
  CardType get type;

  /// = 'groupClassId' for groupings
  CardClassId get classId;
  LocationId?
      get locationId; // TODO: clearly define class-based attributes vs variable instance attributes
// @Deprecated(
//     'handled at the class level ... but maybe this is where we store something like currrent health')
// @Default({})
// Map<String, dynamic> attributes,
// TODO: handle these entirely in the AssetsManager/UseCase/Ref ... diff model
  @Deprecated('handled at the class level')
  Map<String, String>
      get imgRefs; // TODO: should these be on the card or in an Actions with relationships
  List<TargetedAction> get activeActions;

  /// Create a copy of GameCard
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GameCardCopyWith<GameCard> get copyWith =>
      _$GameCardCopyWithImpl<GameCard>(this as GameCard, _$identity);

  /// Serializes this GameCard to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GameCard &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            const DeepCollectionEquality().equals(other.imgRefs, imgRefs) &&
            const DeepCollectionEquality()
                .equals(other.activeActions, activeActions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      classId,
      locationId,
      const DeepCollectionEquality().hash(imgRefs),
      const DeepCollectionEquality().hash(activeActions));

  @override
  String toString() {
    return 'GameCard(id: $id, name: $name, type: $type, classId: $classId, locationId: $locationId, imgRefs: $imgRefs, activeActions: $activeActions)';
  }
}

/// @nodoc
abstract mixin class $GameCardCopyWith<$Res> {
  factory $GameCardCopyWith(GameCard value, $Res Function(GameCard) _then) =
      _$GameCardCopyWithImpl;
  @useResult
  $Res call(
      {@GenerateIdIfNeededConverter() GameCardId id,
      String name,
      CardType type,
      CardClassId classId,
      LocationId? locationId,
      @Deprecated('handled at the class level') Map<String, String> imgRefs,
      List<TargetedAction> activeActions});
}

/// @nodoc
class _$GameCardCopyWithImpl<$Res> implements $GameCardCopyWith<$Res> {
  _$GameCardCopyWithImpl(this._self, this._then);

  final GameCard _self;
  final $Res Function(GameCard) _then;

  /// Create a copy of GameCard
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? classId = null,
    Object? locationId = freezed,
    Object? imgRefs = null,
    Object? activeActions = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as CardType,
      classId: null == classId
          ? _self.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as CardClassId,
      locationId: freezed == locationId
          ? _self.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as LocationId?,
      imgRefs: null == imgRefs
          ? _self.imgRefs
          : imgRefs // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      activeActions: null == activeActions
          ? _self.activeActions
          : activeActions // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _GameCard extends GameCard {
  const _GameCard(
      {@GenerateIdIfNeededConverter() required this.id,
      required this.name,
      required this.type,
      required this.classId,
      this.locationId,
      @Deprecated('handled at the class level')
      final Map<String, String> imgRefs = const <String, String>{},
      final List<TargetedAction> activeActions = const []})
      : _imgRefs = imgRefs,
        _activeActions = activeActions,
        super._();
  factory _GameCard.fromJson(Map<String, dynamic> json) =>
      _$GameCardFromJson(json);

  @override
  @GenerateIdIfNeededConverter()
  final GameCardId id;
  @override
  final String name;
// TODO: this & classId will always match -- we could get this from the class ... or should the class be a parameter here? a getter on MatchState or something?
  @override
  final CardType type;

  /// = 'groupClassId' for groupings
  @override
  final CardClassId classId;
  @override
  final LocationId? locationId;
// TODO: clearly define class-based attributes vs variable instance attributes
// @Deprecated(
//     'handled at the class level ... but maybe this is where we store something like currrent health')
// @Default({})
// Map<String, dynamic> attributes,
// TODO: handle these entirely in the AssetsManager/UseCase/Ref ... diff model
  final Map<String, String> _imgRefs;
// TODO: clearly define class-based attributes vs variable instance attributes
// @Deprecated(
//     'handled at the class level ... but maybe this is where we store something like currrent health')
// @Default({})
// Map<String, dynamic> attributes,
// TODO: handle these entirely in the AssetsManager/UseCase/Ref ... diff model
  @override
  @JsonKey()
  @Deprecated('handled at the class level')
  Map<String, String> get imgRefs {
    if (_imgRefs is EqualUnmodifiableMapView) return _imgRefs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_imgRefs);
  }

// TODO: should these be on the card or in an Actions with relationships
  final List<TargetedAction> _activeActions;
// TODO: should these be on the card or in an Actions with relationships
  @override
  @JsonKey()
  List<TargetedAction> get activeActions {
    if (_activeActions is EqualUnmodifiableListView) return _activeActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_activeActions);
  }

  /// Create a copy of GameCard
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GameCardCopyWith<_GameCard> get copyWith =>
      __$GameCardCopyWithImpl<_GameCard>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GameCardToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GameCard &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            const DeepCollectionEquality().equals(other._imgRefs, _imgRefs) &&
            const DeepCollectionEquality()
                .equals(other._activeActions, _activeActions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      type,
      classId,
      locationId,
      const DeepCollectionEquality().hash(_imgRefs),
      const DeepCollectionEquality().hash(_activeActions));

  @override
  String toString() {
    return 'GameCard(id: $id, name: $name, type: $type, classId: $classId, locationId: $locationId, imgRefs: $imgRefs, activeActions: $activeActions)';
  }
}

/// @nodoc
abstract mixin class _$GameCardCopyWith<$Res>
    implements $GameCardCopyWith<$Res> {
  factory _$GameCardCopyWith(_GameCard value, $Res Function(_GameCard) _then) =
      __$GameCardCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@GenerateIdIfNeededConverter() GameCardId id,
      String name,
      CardType type,
      CardClassId classId,
      LocationId? locationId,
      @Deprecated('handled at the class level') Map<String, String> imgRefs,
      List<TargetedAction> activeActions});
}

/// @nodoc
class __$GameCardCopyWithImpl<$Res> implements _$GameCardCopyWith<$Res> {
  __$GameCardCopyWithImpl(this._self, this._then);

  final _GameCard _self;
  final $Res Function(_GameCard) _then;

  /// Create a copy of GameCard
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? classId = null,
    Object? locationId = freezed,
    Object? imgRefs = null,
    Object? activeActions = null,
  }) {
    return _then(_GameCard(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as CardType,
      classId: null == classId
          ? _self.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as CardClassId,
      locationId: freezed == locationId
          ? _self.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as LocationId?,
      imgRefs: null == imgRefs
          ? _self._imgRefs
          : imgRefs // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      activeActions: null == activeActions
          ? _self._activeActions
          : activeActions // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
    ));
  }
}

// dart format on
