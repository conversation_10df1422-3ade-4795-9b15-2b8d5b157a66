// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'targeted_action.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TargetedAction _$TargetedActionFromJson(Map<String, dynamic> json) =>
    _TargetedAction(
      id: const GenerateIdIfNeededConverter().fromJson(json['id']),
      action: CardAction.fromJson(json['action'] as Map<String, dynamic>),
      subjectCardId: json['subjectCardId'] as String,
      reconciledAttributes:
          json['reconciledAttributes'] as Map<String, dynamic>? ?? const {},
      remainingTurns: (json['remainingTurns'] as num?)?.toInt() ?? 0,
      subjectLocationId: json['subjectLocationId'] as String?,
      objectCardId: json['objectCardId'] as String?,
      objectCardClassId: json['objectCardClassId'] as String?,
    );

Map<String, dynamic> _$TargetedActionToJson(_TargetedAction instance) =>
    <String, dynamic>{
      'id': const GenerateIdIfNeededConverter().toJson(instance.id),
      'action': instance.action,
      'subjectCardId': instance.subjectCardId,
      'reconciledAttributes': instance.reconciledAttributes,
      'remainingTurns': instance.remainingTurns,
      'subjectLocationId': instance.subjectLocationId,
      'objectCardId': instance.objectCardId,
      'objectCardClassId': instance.objectCardClassId,
    };
