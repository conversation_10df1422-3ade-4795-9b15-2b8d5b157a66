// import 'package:dauntless/models/base/player.dart';
// import 'package:flutter/material.dart';
//
// import 'deck.dart';
// import 'game_card.dart';
//
// @immutable
// class PlayerHand extends Deck {
//   const PlayerHand(
//       {required super.id,
//       required super.name,
//       List<GameCard>? startingCards,
//       List<PlayerType>? playerTypesAllowed})
//       : super(cards: startingCards, playerTypesAllowed: playerTypesAllowed);
//
//   // const PlayerHand._({
//   //   required String id,
//   //   String? name,
//   //   List<GameCard> startingCards = const [],
//   // })
//   // // : Deck.get(id: id, name: name);
//   // : super.get(
//   //         id: id,
//   //         name: name ?? id,
//   //         // cards: startingCards,
//   //       );
//
//   // const PlayerHand({
//   //   required String id,
//   //   String? name,
//   //   List<GameCard>? startingCards,
//   // }) = _(
//   //   id: id,
//   //   name: name,
//   //   // cards: startingCards,
//   //     );
//   // const factory PlayerHand({
//   //   required String id,
//   //   String? name,
//   //   List<GameCard> startingCards,
//   // }) = PlayerHand._;
// }
