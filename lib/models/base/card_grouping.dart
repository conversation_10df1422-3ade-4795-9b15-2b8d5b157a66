import 'package:freezed_annotation/freezed_annotation.dart';

import 'game_card.dart';

part 'card_grouping.freezed.dart';
part 'card_grouping.g.dart';


// TODO: rename -- these are relationship pairs: id:groupingId; can be named ... "related"? is this an anti-pattern? How should these best be handled?
@freezed
abstract class CardGrouping with _$CardGrouping {
  const CardGrouping._();

  String get name => name_ ?? groupingId;

  const factory CardGrouping({
    String? name_,
    required GameCardId id,
    required GameCardId groupingId,
    @Default({}) Map<String, dynamic> attributes
  }) = _CardGrouping;

  factory CardGrouping.fromJson(Map<String, dynamic> json) =>
      _$CardGroupingFromJson(json);
}
