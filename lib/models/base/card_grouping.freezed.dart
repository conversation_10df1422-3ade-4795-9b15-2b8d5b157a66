// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card_grouping.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CardGrouping {
  String? get name_;
  GameCardId get id;
  GameCardId get groupingId;
  Map<String, dynamic> get attributes;

  /// Create a copy of CardGrouping
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardGroupingCopyWith<CardGrouping> get copyWith =>
      _$CardGroupingCopyWithImpl<CardGrouping>(
          this as CardGrouping, _$identity);

  /// Serializes this CardGrouping to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardGrouping &&
            (identical(other.name_, name_) || other.name_ == name_) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.groupingId, groupingId) ||
                other.groupingId == groupingId) &&
            const DeepCollectionEquality()
                .equals(other.attributes, attributes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name_, id, groupingId,
      const DeepCollectionEquality().hash(attributes));

  @override
  String toString() {
    return 'CardGrouping(name_: $name_, id: $id, groupingId: $groupingId, attributes: $attributes)';
  }
}

/// @nodoc
abstract mixin class $CardGroupingCopyWith<$Res> {
  factory $CardGroupingCopyWith(
          CardGrouping value, $Res Function(CardGrouping) _then) =
      _$CardGroupingCopyWithImpl;
  @useResult
  $Res call(
      {String? name_,
      GameCardId id,
      GameCardId groupingId,
      Map<String, dynamic> attributes});
}

/// @nodoc
class _$CardGroupingCopyWithImpl<$Res> implements $CardGroupingCopyWith<$Res> {
  _$CardGroupingCopyWithImpl(this._self, this._then);

  final CardGrouping _self;
  final $Res Function(CardGrouping) _then;

  /// Create a copy of CardGrouping
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name_ = freezed,
    Object? id = null,
    Object? groupingId = null,
    Object? attributes = null,
  }) {
    return _then(_self.copyWith(
      name_: freezed == name_
          ? _self.name_
          : name_ // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      groupingId: null == groupingId
          ? _self.groupingId
          : groupingId // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      attributes: null == attributes
          ? _self.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CardGrouping extends CardGrouping {
  const _CardGrouping(
      {this.name_,
      required this.id,
      required this.groupingId,
      final Map<String, dynamic> attributes = const {}})
      : _attributes = attributes,
        super._();
  factory _CardGrouping.fromJson(Map<String, dynamic> json) =>
      _$CardGroupingFromJson(json);

  @override
  final String? name_;
  @override
  final GameCardId id;
  @override
  final GameCardId groupingId;
  final Map<String, dynamic> _attributes;
  @override
  @JsonKey()
  Map<String, dynamic> get attributes {
    if (_attributes is EqualUnmodifiableMapView) return _attributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_attributes);
  }

  /// Create a copy of CardGrouping
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CardGroupingCopyWith<_CardGrouping> get copyWith =>
      __$CardGroupingCopyWithImpl<_CardGrouping>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CardGroupingToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CardGrouping &&
            (identical(other.name_, name_) || other.name_ == name_) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.groupingId, groupingId) ||
                other.groupingId == groupingId) &&
            const DeepCollectionEquality()
                .equals(other._attributes, _attributes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name_, id, groupingId,
      const DeepCollectionEquality().hash(_attributes));

  @override
  String toString() {
    return 'CardGrouping(name_: $name_, id: $id, groupingId: $groupingId, attributes: $attributes)';
  }
}

/// @nodoc
abstract mixin class _$CardGroupingCopyWith<$Res>
    implements $CardGroupingCopyWith<$Res> {
  factory _$CardGroupingCopyWith(
          _CardGrouping value, $Res Function(_CardGrouping) _then) =
      __$CardGroupingCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? name_,
      GameCardId id,
      GameCardId groupingId,
      Map<String, dynamic> attributes});
}

/// @nodoc
class __$CardGroupingCopyWithImpl<$Res>
    implements _$CardGroupingCopyWith<$Res> {
  __$CardGroupingCopyWithImpl(this._self, this._then);

  final _CardGrouping _self;
  final $Res Function(_CardGrouping) _then;

  /// Create a copy of CardGrouping
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name_ = freezed,
    Object? id = null,
    Object? groupingId = null,
    Object? attributes = null,
  }) {
    return _then(_CardGrouping(
      name_: freezed == name_
          ? _self.name_
          : name_ // ignore: cast_nullable_to_non_nullable
              as String?,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      groupingId: null == groupingId
          ? _self.groupingId
          : groupingId // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      attributes: null == attributes
          ? _self._attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on
