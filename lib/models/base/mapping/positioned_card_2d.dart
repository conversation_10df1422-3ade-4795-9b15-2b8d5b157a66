import 'package:dauntless/models/base/game_card_class.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:vector_math/vector_math_64.dart';

part 'positioned_card_2d.freezed.dart';

typedef GridExtents = Matrix2;

@Freezed(fromJson: false, toJson: false)
abstract class PositionedCard2d with _$PositionedCard2d {

  const PositionedCard2d._();

  const factory PositionedCard2d({
    required GameCard card,
    required GameCardClass cardClass,
    // TODO: figure out these defaults
    Vector2? mapCoordinates,
    Vector2? screenCoordinates,
    // @Default(const Vector2.zero()) Vector2 mapCoordinates,
    // @Default(const Vector2.zero()) Vector2 screenCoordinates,
  }) = _PositionedCard2d;

  static PositionedCard2d _withGetScreenCoordinates({
    required GameCard card,
    required GameCardClass cardClass,
    required Vector2 gridCoordinates,
    final double scaleFactor = 1.0,
    Vector2? screenOffset,
  }) {
    final Vector2 screenCoordinates = gridCoordinates.scaled(scaleFactor)..add(screenOffset ?? Vector2.zero());
    return PositionedCard2d(
        card: card,
        cardClass: cardClass,
        mapCoordinates: gridCoordinates,
        screenCoordinates: screenCoordinates);
  }

  // PositionedCard2d updateScreenCoordinates(
  //     final Vector2 newscreenCoordinates,
  //     {final double scaleFactor = 1.0}) {
  //   return _withGetScreenCoordinates(
  //       card: card,
  //       gridCoordinates: mapCoordinates!, // TODO: fix null check
  //       scaleFactor: scaleFactor,
  //       screenOffset: newscreenCoordinates);
  // }

  static PositionedCard2d? fromGameCard(
      final GameCard card,
    final GameCardClass cardClass, {
    final double scaleFactor = 1.0,
    Vector2? screenOffset,
  }) {
    final Vector2? coordinates = cardClass.gridCoordinates;
    if (coordinates == null) {
      return null;
    }
    return _withGetScreenCoordinates(
        card: card,
        cardClass: cardClass,
        gridCoordinates: coordinates,
        scaleFactor: scaleFactor,
        screenOffset: screenOffset);
  }
}
