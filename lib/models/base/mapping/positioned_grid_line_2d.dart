import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'positioned_grid_line_2d.freezed.dart';

enum GridLineType {
  major,
  minor,
}

@freezed
abstract class PositionedGridLine2d with _$PositionedGridLine2d {
  const PositionedGridLine2d._();

  const factory PositionedGridLine2d({
    // @Default(2.0) double width,
    @Default(0.0) double position,
    @Default(Axis.vertical) Axis axis,
    @Default(GridLineType.minor) GridLineType type,
    // @Default(Colors.black) Color color,
  }) = _PositionedGridLine2d;
}