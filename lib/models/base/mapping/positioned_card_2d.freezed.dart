// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'positioned_card_2d.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PositionedCard2d {
  GameCard get card;
  GameCardClass get cardClass; // TODO: figure out these defaults
  Vector2? get mapCoordinates;
  Vector2? get screenCoordinates;

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PositionedCard2dCopyWith<PositionedCard2d> get copyWith =>
      _$PositionedCard2dCopyWithImpl<PositionedCard2d>(
          this as PositionedCard2d, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PositionedCard2d &&
            (identical(other.card, card) || other.card == card) &&
            (identical(other.cardClass, cardClass) ||
                other.cardClass == cardClass) &&
            (identical(other.mapCoordinates, mapCoordinates) ||
                other.mapCoordinates == mapCoordinates) &&
            (identical(other.screenCoordinates, screenCoordinates) ||
                other.screenCoordinates == screenCoordinates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, card, cardClass, mapCoordinates, screenCoordinates);

  @override
  String toString() {
    return 'PositionedCard2d(card: $card, cardClass: $cardClass, mapCoordinates: $mapCoordinates, screenCoordinates: $screenCoordinates)';
  }
}

/// @nodoc
abstract mixin class $PositionedCard2dCopyWith<$Res> {
  factory $PositionedCard2dCopyWith(
          PositionedCard2d value, $Res Function(PositionedCard2d) _then) =
      _$PositionedCard2dCopyWithImpl;
  @useResult
  $Res call(
      {GameCard card,
      GameCardClass cardClass,
      Vector2? mapCoordinates,
      Vector2? screenCoordinates});

  $GameCardCopyWith<$Res> get card;
  $GameCardClassCopyWith<$Res> get cardClass;
}

/// @nodoc
class _$PositionedCard2dCopyWithImpl<$Res>
    implements $PositionedCard2dCopyWith<$Res> {
  _$PositionedCard2dCopyWithImpl(this._self, this._then);

  final PositionedCard2d _self;
  final $Res Function(PositionedCard2d) _then;

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? card = null,
    Object? cardClass = null,
    Object? mapCoordinates = freezed,
    Object? screenCoordinates = freezed,
  }) {
    return _then(_self.copyWith(
      card: null == card
          ? _self.card
          : card // ignore: cast_nullable_to_non_nullable
              as GameCard,
      cardClass: null == cardClass
          ? _self.cardClass
          : cardClass // ignore: cast_nullable_to_non_nullable
              as GameCardClass,
      mapCoordinates: freezed == mapCoordinates
          ? _self.mapCoordinates
          : mapCoordinates // ignore: cast_nullable_to_non_nullable
              as Vector2?,
      screenCoordinates: freezed == screenCoordinates
          ? _self.screenCoordinates
          : screenCoordinates // ignore: cast_nullable_to_non_nullable
              as Vector2?,
    ));
  }

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardCopyWith<$Res> get card {
    return $GameCardCopyWith<$Res>(_self.card, (value) {
      return _then(_self.copyWith(card: value));
    });
  }

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardClassCopyWith<$Res> get cardClass {
    return $GameCardClassCopyWith<$Res>(_self.cardClass, (value) {
      return _then(_self.copyWith(cardClass: value));
    });
  }
}

/// @nodoc

class _PositionedCard2d extends PositionedCard2d {
  const _PositionedCard2d(
      {required this.card,
      required this.cardClass,
      this.mapCoordinates,
      this.screenCoordinates})
      : super._();

  @override
  final GameCard card;
  @override
  final GameCardClass cardClass;
// TODO: figure out these defaults
  @override
  final Vector2? mapCoordinates;
  @override
  final Vector2? screenCoordinates;

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PositionedCard2dCopyWith<_PositionedCard2d> get copyWith =>
      __$PositionedCard2dCopyWithImpl<_PositionedCard2d>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PositionedCard2d &&
            (identical(other.card, card) || other.card == card) &&
            (identical(other.cardClass, cardClass) ||
                other.cardClass == cardClass) &&
            (identical(other.mapCoordinates, mapCoordinates) ||
                other.mapCoordinates == mapCoordinates) &&
            (identical(other.screenCoordinates, screenCoordinates) ||
                other.screenCoordinates == screenCoordinates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, card, cardClass, mapCoordinates, screenCoordinates);

  @override
  String toString() {
    return 'PositionedCard2d(card: $card, cardClass: $cardClass, mapCoordinates: $mapCoordinates, screenCoordinates: $screenCoordinates)';
  }
}

/// @nodoc
abstract mixin class _$PositionedCard2dCopyWith<$Res>
    implements $PositionedCard2dCopyWith<$Res> {
  factory _$PositionedCard2dCopyWith(
          _PositionedCard2d value, $Res Function(_PositionedCard2d) _then) =
      __$PositionedCard2dCopyWithImpl;
  @override
  @useResult
  $Res call(
      {GameCard card,
      GameCardClass cardClass,
      Vector2? mapCoordinates,
      Vector2? screenCoordinates});

  @override
  $GameCardCopyWith<$Res> get card;
  @override
  $GameCardClassCopyWith<$Res> get cardClass;
}

/// @nodoc
class __$PositionedCard2dCopyWithImpl<$Res>
    implements _$PositionedCard2dCopyWith<$Res> {
  __$PositionedCard2dCopyWithImpl(this._self, this._then);

  final _PositionedCard2d _self;
  final $Res Function(_PositionedCard2d) _then;

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? card = null,
    Object? cardClass = null,
    Object? mapCoordinates = freezed,
    Object? screenCoordinates = freezed,
  }) {
    return _then(_PositionedCard2d(
      card: null == card
          ? _self.card
          : card // ignore: cast_nullable_to_non_nullable
              as GameCard,
      cardClass: null == cardClass
          ? _self.cardClass
          : cardClass // ignore: cast_nullable_to_non_nullable
              as GameCardClass,
      mapCoordinates: freezed == mapCoordinates
          ? _self.mapCoordinates
          : mapCoordinates // ignore: cast_nullable_to_non_nullable
              as Vector2?,
      screenCoordinates: freezed == screenCoordinates
          ? _self.screenCoordinates
          : screenCoordinates // ignore: cast_nullable_to_non_nullable
              as Vector2?,
    ));
  }

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardCopyWith<$Res> get card {
    return $GameCardCopyWith<$Res>(_self.card, (value) {
      return _then(_self.copyWith(card: value));
    });
  }

  /// Create a copy of PositionedCard2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardClassCopyWith<$Res> get cardClass {
    return $GameCardClassCopyWith<$Res>(_self.cardClass, (value) {
      return _then(_self.copyWith(cardClass: value));
    });
  }
}

// dart format on
