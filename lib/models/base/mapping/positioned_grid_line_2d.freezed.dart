// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'positioned_grid_line_2d.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PositionedGridLine2d {
// @Default(2.0) double width,
  double get position;
  Axis get axis;
  GridLineType get type;

  /// Create a copy of PositionedGridLine2d
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PositionedGridLine2dCopyWith<PositionedGridLine2d> get copyWith =>
      _$PositionedGridLine2dCopyWithImpl<PositionedGridLine2d>(
          this as PositionedGridLine2d, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PositionedGridLine2d &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.axis, axis) || other.axis == axis) &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position, axis, type);

  @override
  String toString() {
    return 'PositionedGridLine2d(position: $position, axis: $axis, type: $type)';
  }
}

/// @nodoc
abstract mixin class $PositionedGridLine2dCopyWith<$Res> {
  factory $PositionedGridLine2dCopyWith(PositionedGridLine2d value,
          $Res Function(PositionedGridLine2d) _then) =
      _$PositionedGridLine2dCopyWithImpl;
  @useResult
  $Res call({double position, Axis axis, GridLineType type});
}

/// @nodoc
class _$PositionedGridLine2dCopyWithImpl<$Res>
    implements $PositionedGridLine2dCopyWith<$Res> {
  _$PositionedGridLine2dCopyWithImpl(this._self, this._then);

  final PositionedGridLine2d _self;
  final $Res Function(PositionedGridLine2d) _then;

  /// Create a copy of PositionedGridLine2d
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? position = null,
    Object? axis = null,
    Object? type = null,
  }) {
    return _then(_self.copyWith(
      position: null == position
          ? _self.position
          : position // ignore: cast_nullable_to_non_nullable
              as double,
      axis: null == axis
          ? _self.axis
          : axis // ignore: cast_nullable_to_non_nullable
              as Axis,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as GridLineType,
    ));
  }
}

/// @nodoc

class _PositionedGridLine2d extends PositionedGridLine2d {
  const _PositionedGridLine2d(
      {this.position = 0.0,
      this.axis = Axis.vertical,
      this.type = GridLineType.minor})
      : super._();

// @Default(2.0) double width,
  @override
  @JsonKey()
  final double position;
  @override
  @JsonKey()
  final Axis axis;
  @override
  @JsonKey()
  final GridLineType type;

  /// Create a copy of PositionedGridLine2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PositionedGridLine2dCopyWith<_PositionedGridLine2d> get copyWith =>
      __$PositionedGridLine2dCopyWithImpl<_PositionedGridLine2d>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PositionedGridLine2d &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.axis, axis) || other.axis == axis) &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position, axis, type);

  @override
  String toString() {
    return 'PositionedGridLine2d(position: $position, axis: $axis, type: $type)';
  }
}

/// @nodoc
abstract mixin class _$PositionedGridLine2dCopyWith<$Res>
    implements $PositionedGridLine2dCopyWith<$Res> {
  factory _$PositionedGridLine2dCopyWith(_PositionedGridLine2d value,
          $Res Function(_PositionedGridLine2d) _then) =
      __$PositionedGridLine2dCopyWithImpl;
  @override
  @useResult
  $Res call({double position, Axis axis, GridLineType type});
}

/// @nodoc
class __$PositionedGridLine2dCopyWithImpl<$Res>
    implements _$PositionedGridLine2dCopyWith<$Res> {
  __$PositionedGridLine2dCopyWithImpl(this._self, this._then);

  final _PositionedGridLine2d _self;
  final $Res Function(_PositionedGridLine2d) _then;

  /// Create a copy of PositionedGridLine2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? position = null,
    Object? axis = null,
    Object? type = null,
  }) {
    return _then(_PositionedGridLine2d(
      position: null == position
          ? _self.position
          : position // ignore: cast_nullable_to_non_nullable
              as double,
      axis: null == axis
          ? _self.axis
          : axis // ignore: cast_nullable_to_non_nullable
              as Axis,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as GridLineType,
    ));
  }
}

// dart format on
