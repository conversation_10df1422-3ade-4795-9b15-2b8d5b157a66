// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'positioned_label_2d.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PositionedLabel2d {
  Vector2 get coordinates;
  String get label;

  /// Create a copy of PositionedLabel2d
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PositionedLabel2dCopyWith<PositionedLabel2d> get copyWith =>
      _$PositionedLabel2dCopyWithImpl<PositionedLabel2d>(
          this as PositionedLabel2d, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PositionedLabel2d &&
            (identical(other.coordinates, coordinates) ||
                other.coordinates == coordinates) &&
            (identical(other.label, label) || other.label == label));
  }

  @override
  int get hashCode => Object.hash(runtimeType, coordinates, label);

  @override
  String toString() {
    return 'PositionedLabel2d(coordinates: $coordinates, label: $label)';
  }
}

/// @nodoc
abstract mixin class $PositionedLabel2dCopyWith<$Res> {
  factory $PositionedLabel2dCopyWith(
          PositionedLabel2d value, $Res Function(PositionedLabel2d) _then) =
      _$PositionedLabel2dCopyWithImpl;
  @useResult
  $Res call({Vector2 coordinates, String label});
}

/// @nodoc
class _$PositionedLabel2dCopyWithImpl<$Res>
    implements $PositionedLabel2dCopyWith<$Res> {
  _$PositionedLabel2dCopyWithImpl(this._self, this._then);

  final PositionedLabel2d _self;
  final $Res Function(PositionedLabel2d) _then;

  /// Create a copy of PositionedLabel2d
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coordinates = null,
    Object? label = null,
  }) {
    return _then(_self.copyWith(
      coordinates: null == coordinates
          ? _self.coordinates
          : coordinates // ignore: cast_nullable_to_non_nullable
              as Vector2,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _PositionedLabel2d implements PositionedLabel2d {
  const _PositionedLabel2d({required this.coordinates, required this.label});

  @override
  final Vector2 coordinates;
  @override
  final String label;

  /// Create a copy of PositionedLabel2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PositionedLabel2dCopyWith<_PositionedLabel2d> get copyWith =>
      __$PositionedLabel2dCopyWithImpl<_PositionedLabel2d>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PositionedLabel2d &&
            (identical(other.coordinates, coordinates) ||
                other.coordinates == coordinates) &&
            (identical(other.label, label) || other.label == label));
  }

  @override
  int get hashCode => Object.hash(runtimeType, coordinates, label);

  @override
  String toString() {
    return 'PositionedLabel2d(coordinates: $coordinates, label: $label)';
  }
}

/// @nodoc
abstract mixin class _$PositionedLabel2dCopyWith<$Res>
    implements $PositionedLabel2dCopyWith<$Res> {
  factory _$PositionedLabel2dCopyWith(
          _PositionedLabel2d value, $Res Function(_PositionedLabel2d) _then) =
      __$PositionedLabel2dCopyWithImpl;
  @override
  @useResult
  $Res call({Vector2 coordinates, String label});
}

/// @nodoc
class __$PositionedLabel2dCopyWithImpl<$Res>
    implements _$PositionedLabel2dCopyWith<$Res> {
  __$PositionedLabel2dCopyWithImpl(this._self, this._then);

  final _PositionedLabel2d _self;
  final $Res Function(_PositionedLabel2d) _then;

  /// Create a copy of PositionedLabel2d
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? coordinates = null,
    Object? label = null,
  }) {
    return _then(_PositionedLabel2d(
      coordinates: null == coordinates
          ? _self.coordinates
          : coordinates // ignore: cast_nullable_to_non_nullable
              as Vector2,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
