// import 'package:freezed_annotation/freezed_annotation.dart';
//
// import 'game_card.dart';
// import 'resource_value.dart';
//
// typedef ResourceId = String;
//
// @freezed
// class ResourceClass with _$ResourceClass {
//   const factory ResourceClass({
//     required ResourceId id,
//     required String name,
//   }) = _ResourceClass;
//
//   factory ResourceClass.fromJson(Map<String, dynamic> json) =>
//       _$ResourceClassFromJson(json);
// }