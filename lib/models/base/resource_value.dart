import 'package:freezed_annotation/freezed_annotation.dart';

part 'resource_value.freezed.dart';
part 'resource_value.g.dart';

typedef ResourceId = String;

@freezed
abstract class ResourceValue with _$ResourceValue {
  const factory ResourceValue({
    required ResourceId id,
    @Default(0.0) double value,
  }) = _ResourceValue;

  factory ResourceValue.fromJson(Map<String, dynamic> json) =>
      _$ResourceValueFromJson(json);
}