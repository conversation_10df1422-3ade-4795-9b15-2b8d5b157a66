// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'asset_ref.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AssetRef {
  String get id;
  String get path;

  /// Create a copy of AssetRef
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AssetRefCopyWith<AssetRef> get copyWith =>
      _$AssetRefCopyWithImpl<AssetRef>(this as AssetRef, _$identity);

  /// Serializes this AssetRef to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AssetRef &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.path, path) || other.path == path));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, path);

  @override
  String toString() {
    return 'AssetRef(id: $id, path: $path)';
  }
}

/// @nodoc
abstract mixin class $AssetRefCopyWith<$Res> {
  factory $AssetRefCopyWith(AssetRef value, $Res Function(AssetRef) _then) =
      _$AssetRefCopyWithImpl;
  @useResult
  $Res call({String id, String path});
}

/// @nodoc
class _$AssetRefCopyWithImpl<$Res> implements $AssetRefCopyWith<$Res> {
  _$AssetRefCopyWithImpl(this._self, this._then);

  final AssetRef _self;
  final $Res Function(AssetRef) _then;

  /// Create a copy of AssetRef
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? path = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      path: null == path
          ? _self.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AssetRef implements AssetRef {
  const _AssetRef({required this.id, required this.path});
  factory _AssetRef.fromJson(Map<String, dynamic> json) =>
      _$AssetRefFromJson(json);

  @override
  final String id;
  @override
  final String path;

  /// Create a copy of AssetRef
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AssetRefCopyWith<_AssetRef> get copyWith =>
      __$AssetRefCopyWithImpl<_AssetRef>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AssetRefToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AssetRef &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.path, path) || other.path == path));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, path);

  @override
  String toString() {
    return 'AssetRef(id: $id, path: $path)';
  }
}

/// @nodoc
abstract mixin class _$AssetRefCopyWith<$Res>
    implements $AssetRefCopyWith<$Res> {
  factory _$AssetRefCopyWith(_AssetRef value, $Res Function(_AssetRef) _then) =
      __$AssetRefCopyWithImpl;
  @override
  @useResult
  $Res call({String id, String path});
}

/// @nodoc
class __$AssetRefCopyWithImpl<$Res> implements _$AssetRefCopyWith<$Res> {
  __$AssetRefCopyWithImpl(this._self, this._then);

  final _AssetRef _self;
  final $Res Function(_AssetRef) _then;

  /// Create a copy of AssetRef
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? path = null,
  }) {
    return _then(_AssetRef(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      path: null == path
          ? _self.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
