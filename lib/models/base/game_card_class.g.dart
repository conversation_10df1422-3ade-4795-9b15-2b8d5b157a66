// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_card_class.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GameCardClass _$GameCardClassFromJson(Map<String, dynamic> json) =>
    _GameCardClass(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$CardTypeEnumMap, json['type']),
      attributes: json['attributes'] as Map<String, dynamic>? ?? const {},
      imgRefs: (json['imgRefs'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const <String, String>{},
    );

Map<String, dynamic> _$GameCardClassToJson(_GameCardClass instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$CardTypeEnumMap[instance.type]!,
      'attributes': instance.attributes,
      'imgRefs': instance.imgRefs,
    };

const _$CardTypeEnumMap = {
  CardType.character: 'character',
  CardType.location: 'location',
  CardType.building: 'building',
  CardType.vehicle: 'vehicle',
  CardType.grouping: 'grouping',
};
