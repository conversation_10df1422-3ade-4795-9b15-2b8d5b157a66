// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchConfig _$MatchConfigFromJson(Map<String, dynamic> json) => _MatchConfig(
      gameId: json['gameId'] as String,
      selectedGameMode:
          $enumDecode(_$GameModeEnumMap, json['selectedGameMode']),
      hostId: json['hostId'] as String,
      matchId: const GenerateIdIfNeededConverter().from<PERSON>son(json['matchId']),
      players: (json['players'] as List<dynamic>?)
              ?.map((e) => Player.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      name: json['name'] as String?,
    );

Map<String, dynamic> _$MatchConfigToJson(_MatchConfig instance) =>
    <String, dynamic>{
      'gameId': instance.gameId,
      'selectedGameMode': _$GameModeEnumMap[instance.selectedGameMode]!,
      'hostId': instance.hostId,
      'matchId': const GenerateIdIfNeededConverter().toJson(instance.matchId),
      'players': instance.players,
      'name': instance.name,
    };

const _$GameModeEnumMap = {
  GameMode.hotSeat: 'hotSeat',
  GameMode.server: 'server',
};
