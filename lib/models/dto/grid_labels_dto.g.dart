// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'grid_labels_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GridLabelsDto _$GridLabelsDtoFromJson(Map<String, dynamic> json) =>
    _GridLabelsDto(
      rowLabels: (json['rowLabels'] as List<dynamic>?)
              ?.map((e) =>
                  GridLabelDtoComponent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      columnLabels: (json['columnLabels'] as List<dynamic>?)
              ?.map((e) =>
                  GridLabelDtoComponent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$GridLabelsDtoToJson(_GridLabelsDto instance) =>
    <String, dynamic>{
      'rowLabels': instance.rowLabels,
      'columnLabels': instance.columnLabels,
    };
