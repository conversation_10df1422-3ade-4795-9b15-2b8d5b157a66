// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'grid_label_dto_component.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GridLabelDtoComponent {
  String get name;
  double get position;

  /// Create a copy of GridLabelDtoComponent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GridLabelDtoComponentCopyWith<GridLabelDtoComponent> get copyWith =>
      _$GridLabelDtoComponentCopyWithImpl<GridLabelDtoComponent>(
          this as GridLabelDtoComponent, _$identity);

  /// Serializes this GridLabelDtoComponent to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GridLabelDtoComponent &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, position);

  @override
  String toString() {
    return 'GridLabelDtoComponent(name: $name, position: $position)';
  }
}

/// @nodoc
abstract mixin class $GridLabelDtoComponentCopyWith<$Res> {
  factory $GridLabelDtoComponentCopyWith(GridLabelDtoComponent value,
          $Res Function(GridLabelDtoComponent) _then) =
      _$GridLabelDtoComponentCopyWithImpl;
  @useResult
  $Res call({String name, double position});
}

/// @nodoc
class _$GridLabelDtoComponentCopyWithImpl<$Res>
    implements $GridLabelDtoComponentCopyWith<$Res> {
  _$GridLabelDtoComponentCopyWithImpl(this._self, this._then);

  final GridLabelDtoComponent _self;
  final $Res Function(GridLabelDtoComponent) _then;

  /// Create a copy of GridLabelDtoComponent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? position = null,
  }) {
    return _then(_self.copyWith(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      position: null == position
          ? _self.position
          : position // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _GridLabelDtoComponent implements GridLabelDtoComponent {
  const _GridLabelDtoComponent({required this.name, required this.position});
  factory _GridLabelDtoComponent.fromJson(Map<String, dynamic> json) =>
      _$GridLabelDtoComponentFromJson(json);

  @override
  final String name;
  @override
  final double position;

  /// Create a copy of GridLabelDtoComponent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GridLabelDtoComponentCopyWith<_GridLabelDtoComponent> get copyWith =>
      __$GridLabelDtoComponentCopyWithImpl<_GridLabelDtoComponent>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GridLabelDtoComponentToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GridLabelDtoComponent &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, position);

  @override
  String toString() {
    return 'GridLabelDtoComponent(name: $name, position: $position)';
  }
}

/// @nodoc
abstract mixin class _$GridLabelDtoComponentCopyWith<$Res>
    implements $GridLabelDtoComponentCopyWith<$Res> {
  factory _$GridLabelDtoComponentCopyWith(_GridLabelDtoComponent value,
          $Res Function(_GridLabelDtoComponent) _then) =
      __$GridLabelDtoComponentCopyWithImpl;
  @override
  @useResult
  $Res call({String name, double position});
}

/// @nodoc
class __$GridLabelDtoComponentCopyWithImpl<$Res>
    implements _$GridLabelDtoComponentCopyWith<$Res> {
  __$GridLabelDtoComponentCopyWithImpl(this._self, this._then);

  final _GridLabelDtoComponent _self;
  final $Res Function(_GridLabelDtoComponent) _then;

  /// Create a copy of GridLabelDtoComponent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = null,
    Object? position = null,
  }) {
    return _then(_GridLabelDtoComponent(
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      position: null == position
          ? _self.position
          : position // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

// dart format on
