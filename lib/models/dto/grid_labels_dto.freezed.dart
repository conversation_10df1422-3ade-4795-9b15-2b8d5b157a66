// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'grid_labels_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GridLabelsDto {
  List<GridLabelDtoComponent> get rowLabels;
  List<GridLabelDtoComponent> get columnLabels;

  /// Create a copy of GridLabelsDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GridLabelsDtoCopyWith<GridLabelsDto> get copyWith =>
      _$GridLabelsDtoCopyWithImpl<GridLabelsDto>(
          this as GridLabelsDto, _$identity);

  /// Serializes this GridLabelsDto to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GridLabelsDto &&
            const DeepCollectionEquality().equals(other.rowLabels, rowLabels) &&
            const DeepCollectionEquality()
                .equals(other.columnLabels, columnLabels));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(rowLabels),
      const DeepCollectionEquality().hash(columnLabels));

  @override
  String toString() {
    return 'GridLabelsDto(rowLabels: $rowLabels, columnLabels: $columnLabels)';
  }
}

/// @nodoc
abstract mixin class $GridLabelsDtoCopyWith<$Res> {
  factory $GridLabelsDtoCopyWith(
          GridLabelsDto value, $Res Function(GridLabelsDto) _then) =
      _$GridLabelsDtoCopyWithImpl;
  @useResult
  $Res call(
      {List<GridLabelDtoComponent> rowLabels,
      List<GridLabelDtoComponent> columnLabels});
}

/// @nodoc
class _$GridLabelsDtoCopyWithImpl<$Res>
    implements $GridLabelsDtoCopyWith<$Res> {
  _$GridLabelsDtoCopyWithImpl(this._self, this._then);

  final GridLabelsDto _self;
  final $Res Function(GridLabelsDto) _then;

  /// Create a copy of GridLabelsDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rowLabels = null,
    Object? columnLabels = null,
  }) {
    return _then(_self.copyWith(
      rowLabels: null == rowLabels
          ? _self.rowLabels
          : rowLabels // ignore: cast_nullable_to_non_nullable
              as List<GridLabelDtoComponent>,
      columnLabels: null == columnLabels
          ? _self.columnLabels
          : columnLabels // ignore: cast_nullable_to_non_nullable
              as List<GridLabelDtoComponent>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _GridLabelsDto extends GridLabelsDto {
  const _GridLabelsDto(
      {final List<GridLabelDtoComponent> rowLabels = const [],
      final List<GridLabelDtoComponent> columnLabels = const []})
      : _rowLabels = rowLabels,
        _columnLabels = columnLabels,
        super._();
  factory _GridLabelsDto.fromJson(Map<String, dynamic> json) =>
      _$GridLabelsDtoFromJson(json);

  final List<GridLabelDtoComponent> _rowLabels;
  @override
  @JsonKey()
  List<GridLabelDtoComponent> get rowLabels {
    if (_rowLabels is EqualUnmodifiableListView) return _rowLabels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rowLabels);
  }

  final List<GridLabelDtoComponent> _columnLabels;
  @override
  @JsonKey()
  List<GridLabelDtoComponent> get columnLabels {
    if (_columnLabels is EqualUnmodifiableListView) return _columnLabels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_columnLabels);
  }

  /// Create a copy of GridLabelsDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GridLabelsDtoCopyWith<_GridLabelsDto> get copyWith =>
      __$GridLabelsDtoCopyWithImpl<_GridLabelsDto>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GridLabelsDtoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GridLabelsDto &&
            const DeepCollectionEquality()
                .equals(other._rowLabels, _rowLabels) &&
            const DeepCollectionEquality()
                .equals(other._columnLabels, _columnLabels));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_rowLabels),
      const DeepCollectionEquality().hash(_columnLabels));

  @override
  String toString() {
    return 'GridLabelsDto(rowLabels: $rowLabels, columnLabels: $columnLabels)';
  }
}

/// @nodoc
abstract mixin class _$GridLabelsDtoCopyWith<$Res>
    implements $GridLabelsDtoCopyWith<$Res> {
  factory _$GridLabelsDtoCopyWith(
          _GridLabelsDto value, $Res Function(_GridLabelsDto) _then) =
      __$GridLabelsDtoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<GridLabelDtoComponent> rowLabels,
      List<GridLabelDtoComponent> columnLabels});
}

/// @nodoc
class __$GridLabelsDtoCopyWithImpl<$Res>
    implements _$GridLabelsDtoCopyWith<$Res> {
  __$GridLabelsDtoCopyWithImpl(this._self, this._then);

  final _GridLabelsDto _self;
  final $Res Function(_GridLabelsDto) _then;

  /// Create a copy of GridLabelsDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? rowLabels = null,
    Object? columnLabels = null,
  }) {
    return _then(_GridLabelsDto(
      rowLabels: null == rowLabels
          ? _self._rowLabels
          : rowLabels // ignore: cast_nullable_to_non_nullable
              as List<GridLabelDtoComponent>,
      columnLabels: null == columnLabels
          ? _self._columnLabels
          : columnLabels // ignore: cast_nullable_to_non_nullable
              as List<GridLabelDtoComponent>,
    ));
  }
}

// dart format on
