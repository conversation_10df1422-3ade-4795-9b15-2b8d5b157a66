import 'package:freezed_annotation/freezed_annotation.dart';

part 'grid_label_dto_component.freezed.dart';

part 'grid_label_dto_component.g.dart';

@freezed
abstract class GridLabelDtoComponent with _$GridLabelDtoComponent {
  const factory GridLabelDtoComponent({
    required String name,
    required double position
  }) =
      _GridLabelDtoComponent;

  factory GridLabelDtoComponent.fromJson(Map<String, dynamic> json) =>
      _$GridLabelDtoComponentFromJson(json);
}
