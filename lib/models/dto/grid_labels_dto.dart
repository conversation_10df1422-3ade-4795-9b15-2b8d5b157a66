import 'package:freezed_annotation/freezed_annotation.dart';

import 'grid_label_dto_component.dart';

part 'grid_labels_dto.freezed.dart';

part 'grid_labels_dto.g.dart';

typedef GridLabelComponent = (double position, String name);
typedef GridLabel = (GridLabelComponent x, GridLabelComponent y);

// typedef GridLabelDtoComponent = MapEntry<String, double>;
// typedef GridLabelDtoComponent = (String name, double position);

@freezed
abstract class GridLabelsDto with _$GridLabelsDto {
  const GridLabelsDto._();

  (List<GridLabelComponent>, List<GridLabelComponent>) toGridLabelComponents() {
    final List<GridLabelComponent> _rowLabels =
        rowLabels.map((e) => (e.position, e.name)).toList();
    final List<GridLabelComponent> _columnLabels =
        columnLabels.map((e) => (e.position, e.name)).toList();
    return (_rowLabels, _columnLabels);
  }

  const factory GridLabelsDto({
    @Default([]) List<GridLabelDtoComponent> rowLabels,
    @Default([]) List<GridLabelDtoComponent> columnLabels,
    // @Default([]) List<GridLabelDtoComponent> rowLabels,
    // @Default([]) List<GridLabelDtoComponent> columnLabels,
  }) = _GridLabelsDto;

  factory GridLabelsDto.fromJson(Map<String, dynamic> json) =>
      _$GridLabelsDtoFromJson(json);
}
