import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/repositories/generic_card_class_repository.dart';

class GenericCardClassUseCase {
  final GenericCardClassRepository _repository;

  GenericCardClassUseCase(this._repository);

  Future<Map<CardClassId, GameCardClass>> getGameCardClasses() async {
    final classList = await _repository.get();
    return Map.fromEntries(classList.map((e) => MapEntry(e.id, e)));
  }
}