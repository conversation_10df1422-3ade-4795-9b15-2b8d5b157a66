import 'package:dauntless/dev/dev_initial_state.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/repositories/assets_repository.dart';

import 'players_use_case.dart';

class AssetsUseCase {
  final AssetsRepository _repository;

  AssetsUseCase(this._repository);

  Map<String, String> loadAssetRefs(String basePath) {
    return {
      'imperial_fleet_icon': 'icons/imperial_fleet.png',
      'alliance_fleet_icon': 'icons/alliance_fleet.png',
      'neutral_building_icon': 'icons/neutral_building.png',
      'alliance_building_icon': 'icons/alliance_building.png',
      'imperial_building_icon': 'icons/imperial_building.png',
      'neutral_battery_icon': 'icons/neutral_battery.png',
      'alliance_battery_icon': 'icons/alliance_battery.png',
      'imperial_battery_icon': 'icons/imperial_battery.png',
      'imperial_missions_icon': 'icons/imperial_missions.png',
      'alliance_missions_icon': 'icons/alliance_missions.png',
    };
  }

  Map<PlayerId, Map<CardType, String>> loadCardTypeAssetFallbacks(String basePath) {
    return {
      DevInitialState.imperialPlayer.id: {
        CardType.grouping: 'icons/imperial_fleet.png',
        CardType.location: 'icons/imperial_building.png',
        CardType.vehicle: 'icons/imperial_fleet.png',
        CardType.character: 'icons/imperial_missions.png',
      },
      DevInitialState.alliancePlayer.id: {
        CardType.grouping: 'icons/alliance_fleet.png',
        CardType.location: 'icons/alliance_building.png',
        CardType.vehicle: 'icons/alliance_fleet.png',
        CardType.character: 'icons/alliance_missions.png',
      },
      locationsPlayerId: {
        CardType.location: 'planets/rocky43.png',
      },
    };
  }
}