import 'dart:async';

import 'package:dauntless/api/dtos/submit_turn_dto.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/repositories/match_repository.dart';
import 'package:dauntless/repositories/players_repository.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match/match_use_case.dart';

class NetworkMatchUseCase extends MatchUseCase {
  final ServerRepository _serverRepository;
  final RemoteLogger _logger;
  final PlayersRepository _playersRepository;
  final MatchRepository _matchRepository;

  // Stream subscription for server messages
  late final StreamSubscription _serverMessagesSubscription;

  NetworkMatchUseCase(
    this._logger,
    this._playersRepository,
    this._matchRepository,
    this._serverRepository,
  ) : super(_logger, _playersRepository, _matchRepository) {
    // Listen to server messages, especially for other player turn submissions
    _setupServerMessageListener();
  }

  @override
  Future<GameMatchState> submitPlayerTurn(GameMatchState state) async {
    await _submitTurnToServer(state);
    // TODO: handle hot seat w/ more players; player order
    state = state.copyWith(
      userPlayerId: _playersRepository.playablePlayers
          .firstWhere((player) => player.id != state.userPlayerId)
          .id,
    );

    return state;
  }

  /// Submits turn actions to the remote server
  Future<void> _submitTurnToServer(
    GameMatchState state,
    // PlayerId playerId,
    // List<TargetedAction> actions,
    // String matchId
  ) async {
    final playerId = state.userPlayerId;
    final actions = state.currentTurnActions;
    final matchId = state.matchConfig.gameId;
    _logger.info(
        'Submitting turn to server for player: $playerId with ${actions.length} actions for game_match: $matchId');

    // Submit turn actions to the server
    final submitTurnDto = SubmitTurnDto(
      playerId: playerId,
      move: actions,
      gameMatchId: matchId,
      turnNumber: state.turnCount,
    );
    final success = await _serverRepository.submitTurnActions(submitTurnDto);

    if (!success) {
      _logger.error(
          'Failed to submit turn to server, falling back to local processing');
      // Fallback to local repository if server submission fails
      _matchRepository.submitPlayerTurnActions(playerId, actions);
    }
  }

  void _setupServerMessageListener() {
    _serverMessagesSubscription =
        _serverRepository.serverMessages.listen((message) {
      _logger.info('Received server message of type: ${message['type']}');

      switch (message['type']) {
        case 'other_player_turn_submission':
          _handleOtherPlayerTurnSubmission(message);
          break;
        default:
          _logger.info('Unhandled message type: ${message['type']}');
      }
    }, onError: (error) {
      _logger.error('Error in server message stream: $error');
    });
  }

  /// Handles turn submissions from other players
  void _handleOtherPlayerTurnSubmission(Map<String, dynamic> message) {
    final String matchId = message['matchId'];
    _logger.info(
        'Processing turn submission from other player for game_match: $matchId');

    // Check if this is a direct server response with other player information
    if (message['from_server'] == true &&
        message['other_player_turn'] == true) {
      _logger
          .info('Processing server response with other player turn submission');

      // Use the other player ID provided by the server
      final String? otherPlayerId = message['other_player'] as String?;

      if (otherPlayerId != null) {
        _logger.info('Server indicated other player ID: $otherPlayerId');

        // Create empty actions - in a real implementation with more complex server responses,
        // we would parse the actions from the message
        final otherPlayerActions = List<TargetedAction>.empty(growable: true);

        // Submit these as the other player's turn
        _matchRepository.submitDevOtherPlayerTurnActions(
            otherPlayerId, otherPlayerActions);

        _logger
            .info('Processed server-provided turn for player: $otherPlayerId');
      } else {
        _logger.warn('Server response missing other player ID');
        // Fallback to local simulation if server didn't provide player ID
        _simulateOtherPlayerTurn(matchId);
      }
    }
    // For client-side simulated dev purposes
    else if (message['simulated'] == true) {
      _logger
          .info('Simulating turn submission from other players (client-side)');
      _simulateOtherPlayerTurn(matchId);
    }
    // If server provided game_match data without specific other_player_turn flag
    else if (message.containsKey('game_match')) {
      _logger.info('Processing game_match data from server');
      _simulateOtherPlayerTurn(matchId);
    }

    // After processing the other player's turn, check if all players have submitted their turns
    if (allPlayersSubmittedTurn) {
      _logger.info('All players have submitted their turns, processing turn');
      // No need to do anything here as the MatchManager's _onSubmitPlayerTurnEvent will handle this
    }
  }

  /// Helper method to simulate other player turn for development
  void _simulateOtherPlayerTurn(String matchId) {
    // Determine the other player ID - find a player that's not the current user
    // Get the most recently submitted player ID or default to first player if none
    final submittedPlayerId = _matchRepository.submittedActiveActions.isNotEmpty
        ? _matchRepository.submittedActiveActions.keys.last
        : _playersRepository.playablePlayers.first.id;

    final otherPlayerId = _playersRepository.playablePlayers
        .firstWhere((player) => player.id != submittedPlayerId)
        .id;

    // Create simulated actions for the other player - for dev purposes, we're using empty actions
    // which will effectively just register the other player as having submitted their turn
    final simulatedActions = List<TargetedAction>.empty(growable: true);

    // Submit these simulated actions to the game_match repository using the dev method
    // that allows overwriting existing actions if necessary
    _matchRepository.submitDevOtherPlayerTurnActions(
        otherPlayerId, simulatedActions);

    _logger
        .info('Simulated turn submission processed for player: $otherPlayerId');
  }

  /// Disposes of resources when no longer needed
  void dispose() {
    _serverMessagesSubscription.cancel();
  }
}
