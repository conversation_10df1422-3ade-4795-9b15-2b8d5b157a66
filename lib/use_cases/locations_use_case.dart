import 'dart:async';

import 'package:collection/collection.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/dto/grid_labels_dto.dart';
import 'package:dauntless/repositories/locations_repository.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

class LocationsUseCase extends ListObjectUseCase<GameCard> {
  final LocationsRepository _repository;

  LocationsUseCase(this._repository) : super();

  @override
  Future<void> init() => _getLocations();

  ProcessingStatus _status = ProcessingStatus.start;

  @override
  ProcessingStatus get status => _status;

  @override
  List<GameCard> get objects => _locations;

  Map<CardClassId, GameCardClass> get locationClasses => _locationClasses;
  // List<GameCardClass> get locationClasses => _locationClasses;

  final List<GameCard> _locations = [];
  final Map<CardClassId, GameCardClass> _locationClasses = {};

  List<GridLabelComponent> get gridRowLabels => _gridRowLabels;
  List<GridLabelComponent> get gridColumnLabels => _gridColumnLabels;

  final List<GridLabelComponent> _gridRowLabels = [];
  final List<GridLabelComponent> _gridColumnLabels = [];

  GameCardClass? getLocationById(CardClassId id) =>
      _locationClasses[id];//.firstWhereOrNull((element) => element.id == id);

  Future<void> get ready => _initCompleter.future;

  final Completer<void> _initCompleter = Completer<void>();

  Future<void> _getLocations() async {
    if (_status == ProcessingStatus.loading) {
      return _initCompleter.future;
    }
    if (_status == ProcessingStatus.start) {
      _status = ProcessingStatus.loading;
      await Future.wait([
        () async {
          final locationsList = await _repository.get();
          final Map<CardClassId, GameCardClass> locationsClassesMap = Map.fromEntries(locationsList.map((e) => MapEntry(e.id, e)));
          final locations = locationsClassesMap;
          final locationGroupings = await _repository.getGroupings();

          final groupings =
              locationGroupings.map((grouping) => grouping.groupingId).toSet();

          final Set<CardGrouping> groupingsSet = groupings
              .map((groupingId) => locationGroupings
                  .firstWhere((grouping) => grouping.groupingId == groupingId))
              .toSet();

          final locationGroupingCards = groupingsSet
              .map((grouping) => GameCard.fromGrouping(grouping))
              .whereType<GameCardClass>();

          final Map<CardClassId, GameCardClass> locationsGroupingsClassesMap = Map.fromEntries(locationGroupingCards.map((e) => MapEntry(e.id, e)));

          _locationClasses.addAll(locations);
          _locationClasses.addAll(locationsGroupingsClassesMap);

          final locationCards = _locationClasses.values.map((e) => GameCard.fromGameCardClass(e, id: e.id)).toList();
          _locations.addAll(locationCards);
        }(),
        _getGridLabels(),
        // () async {
        //   final groupings = await _repository.getGroupings();
        //   _groupings.addAll(groupings);
        // }()
      ]);
      _status = ProcessingStatus.loaded;
      _initCompleter.complete();
    }
  }

  Future<void> _getGridLabels() async {
    final (gridRowLabels, gridColumnLabels) = await _repository.getGridLabels();
    _gridRowLabels.addAll(gridRowLabels);
    _gridColumnLabels.addAll(gridColumnLabels);
  }

  @override
  void selectObject(object) {
    // TODO: implement selectObject
  }

  GameCardClass? getBaseLocationCard(
      Map<PlayerId, List<GameCard>> hands, CardClassId? locationId) {
    if (locationId == null) {
      return null;
    }
    return _getBaseLocationCard(hands, locationId);
  }

  GameCardClass? _getBaseLocationCard(
      Map<PlayerId, List<GameCard>> hands, GameCardId locationId) {
    final locationCard = getLocationById(locationId);
    if (locationCard != null) {
      return locationCard;
    }
    for (var hand in hands.values) {
      final card = hand.firstWhereOrNull((card) => card.id == locationId);
      if (card?.type == CardType.location && card?.locationId != null) {
        return card as GameCardClass;
      }
      if (card?.locationId == null) {
        continue;
      }
      return _getBaseLocationCard(hands, card!.locationId!);
    }
    return null;
  }
}
