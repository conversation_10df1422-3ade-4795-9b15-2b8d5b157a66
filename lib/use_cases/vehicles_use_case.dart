import 'dart:async';

import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/repositories/vehicles_repository.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';

class VehiclesUseCase extends ListObjectUseCase<GameCard> {
  final VehiclesRepository _repository;

  VehiclesUseCase(this._repository) : super();

  @override
  Future<void> init() => _getVehicles();

  ProcessingStatus _status = ProcessingStatus.start;

  @override
  ProcessingStatus get status => _status;

  @override
  List<GameCard> get objects => _vehicleClasses
      .map((e) => GameCard.fromGameCardClass(e))
      .toList(); // _vehicles;

  // List<GameCardClass> get vehicleClasses => _vehicleClasses;
  Map<CardClassId, GameCardClass> get vehicleClasses => Map.fromEntries(_vehicleClasses.map((e) => MapEntry(e.id, e)));

  final List<GameCardClass> _vehicleClasses = [];

  final Completer<void> _initCompleter = Completer<void>();

  Future<void> _getVehicles() async {
    if (_status == ProcessingStatus.loading) {
      return _initCompleter.future;
    }
    if (_status == ProcessingStatus.start) {
      _status = ProcessingStatus.loading;
      final vehicles = await _repository.get();
      _vehicleClasses.addAll(vehicles);
      // _vehicles.addAll(vehicles as Iterable<GameCard>);
      _status = ProcessingStatus.loaded;
      _initCompleter.complete();
    }
  }

  @override
  void selectObject(object) {
    // TODO: implement selectObject
  }
}
