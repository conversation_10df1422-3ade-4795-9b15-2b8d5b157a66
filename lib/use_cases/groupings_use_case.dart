import 'dart:async';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/repositories/locations_repository.dart';
import 'package:dauntless/repositories/actions_repository.dart';
import 'package:dauntless/repositories/game_card_repository.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';

class LocationGroupingsUseCase extends GroupingsUseCase {
  LocationGroupingsUseCase(LocationsRepository super.repository);
}

class ActionsGroupingsUseCase extends GroupingsUseCase {
  ActionsGroupingsUseCase(ActionsRepository super.repository);
}

class GroupingsUseCase extends ListObjectUseCase<CardGrouping> {
  final BaseListRepository _repository;
  GroupingsUseCase(this._repository);

  @override
  List<CardGrouping> get objects => _groupings;//.where((grouping) => _selectedGroupingIds.contains(grouping.id)).toList();
      // .where((grouping) => grouping.groupingId == _selectedGroupingId).toList();

  List<CardGrouping> getGroupingsByIds(List<GameCardId> ids) => _groupings.where((grouping) => ids.contains(grouping.id)).toList();

  List<CardGrouping> getGroupingsByGroupingIds(List<GameCardId> ids) => _groupings.where((grouping) => ids.contains(grouping.groupingId)).toList();

  Set<GameCardId> get allGroupingIds => _groupings.map((grouping) => grouping.groupingId).toSet();

  Set<GameCardId> getGroupingIdsByIds(List<GameCardId> ids) => getGroupingsByIds(ids).map((grouping) => grouping.groupingId).toSet();

  Set<GameCardId> getIdsByGroupingIds(List<GameCardId> groupingIds) => getGroupingsByGroupingIds(groupingIds).map((grouping) => grouping.id).toSet();

  @override
  Future<void> init() => _getGroupings();

  // List<CardGrouping> get groupings => _groupings;

  final List<CardGrouping> _groupings = [];

  ProcessingStatus _status = ProcessingStatus.start;

  // List<GameCardId> _selectedGroupingIds = [];

  /// overrides any existing selected grouping ids
  // void setSelectedGroupingIds(List<GameCardId> ids) {
  //   _selectedGroupingIds = ids;
  // }

  @override
  ProcessingStatus get status => _status;

  final Completer<void> _initCompleter = Completer<void>();

  _getGroupings() async {
    if (_status == ProcessingStatus.loading) {
      return _initCompleter.future;
    }
    if (_status == ProcessingStatus.start) {
      _status = ProcessingStatus.loading;
      final groupings = await _repository.getGroupings();
      _groupings.addAll(groupings);
      _status = ProcessingStatus.loaded;
      _initCompleter.complete();
    }
  }

  @override
  void selectObject(object) {
    // TODO: implement selectObject
  }
}