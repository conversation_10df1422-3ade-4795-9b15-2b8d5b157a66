import 'dart:async';
import 'package:dauntless/repositories/logging/logging_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:loggy/loggy.dart';

class RemoteLogger {
  final LoggingUseCase _loggingUseCase;
  final String packageName;

  RemoteLogger(this._loggingUseCase, this.packageName);

  void debug(String message) {
    _loggingUseCase._debug(message, packageName);
  }

  void info(String message) {
    _loggingUseCase._info(message, packageName);
  }

  void warn(String message) {
    _loggingUseCase._warn(message, packageName);
  }

  void error(String message) {
    _loggingUseCase._error(message, packageName);
  }

  void critical(String message) {
    _loggingUseCase._critical(message, packageName);
  }
}

// Various log levels
enum LogLevel {
  debug('DEBUG'),
  info('INFO'),
  warn('WARN'),
  error('ERROR'),
  critical('CRITICAL');

  final String level;
  const LogLevel(this.level);

  @override
  String toString() => level;

  // Static method to get AIModel from string
  static LogLevel? fromString(String logLevel) {
    // Find the enum that matches the given name
    for (var level in LogLevel.values) {
      if (level.level == logLevel) {
        return level;
      }
    }
    return null; // Return null if no matching model found
  }
}

const int maxLogs = 10; // The number of logs before
const int minutes = 5; // Log after 5 minutes

class LoggingUseCase {
  final LoggingRepository _loggingRepository;
  // final LocalStorageRepository _localStorageRepository;
  // final AppLifeCycleRepository _appLifeCycleRepository;
  // final AppConfig _appConfig;
  // final EnvironmentInfoRepository _environmentInfoRepository;
  late Loggy _loggy;
  late bool _canRemoteLog;

  Timer? _timer;
  // late Lock logLock;
  final Map<String, LogLevel> _excludedLevels = {};

  LoggingUseCase(
    this._loggingRepository,
    // this._localStorageRepository,
    // this._appLifeCycleRepository,
    // this._appConfig,
    // this._environmentInfoRepository
  ) {
    // logLock = Lock();
    _loggy = Loggy('LoggingUseCase');
    Loggy.initLoggy(logPrinter: const PrettyPrinter());
    const env = String.fromEnvironment('env', defaultValue: 'dev');

    // const canRemoteLogStr =
    //     String.fromEnvironment('remotelog', defaultValue: 'false');
    // _canRemoteLog = canRemoteLogStr == 'true';
    _canRemoteLog = (env == 'prod' || env == 'staging');
    print('LOGGER - Can remote log: $_canRemoteLog');
  }

  Future<void> init() async {
    logAppVersion();
    // Listen to connectivity changes
    // Connectivity().onConnectivityChanged.listen((results) async {
    //   final result = results.last;
    //   if (result != ConnectivityResult.none) {
    //     await _log();
    //   }
    //
    //   for (String logLevel in _appConfig.excludedLogs) {
    //     // Protects against garbage values}
    //     LogLevel? level = LogLevel.fromString(logLevel);
    //     if (level != null) {
    //       _excludedLevels[logLevel] = level;
    //     }
    //   }
    // });

    // Starts the timer, useful for instance the app was restarted but no new logs generated but some pending
    _startTimer();
    // _appLifeCycleRepository.appLifeCycleStream.listen(_onAppLifeCycleUpdate);
  }

  void logAppVersion() {
    // PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
    //   String version = '${packageInfo.version}+${packageInfo.buildNumber}';
    //
    //   String? buildSignature = packageInfo.buildSignature;
    //   buildSignature = buildSignature.isNotEmpty ? '—$buildSignature' : '';
    //   String logMessage =
    //       '1440 Mobile Version: $version-$buildSignature \n${packageInfo.version}';
    //   print(logMessage);
    // });
  }

  void _onAppLifeCycleUpdate(AppLifecycleState state) async {
    _info('AppLifecycleState update: $state', 'AppLifeCycle');
    if (state == AppLifecycleState.resumed) {
      _startTimer();
      await _log();
    }
    if (state == AppLifecycleState.inactive) {
      _stopTimer();
    }
  }

  RemoteLogger getRemoteLogger(String packageName) {
    return RemoteLogger(this, packageName);
  }

  Future<void> _debug(String message, String packageName) async {
    if (_excludedLevels.containsKey(LogLevel.debug.level)) {
      return;
    }
    if (!_canRemoteLog) {
      _loggy.debug("$packageName—$message");
      return;
    }
    return await _storeLogEvent(LogLevel.debug, message, packageName);
  }

  Future<void> _info(String message, String packageName) async {
    if (_excludedLevels.containsKey(LogLevel.info.level)) {
      return;
    }
    if (!_canRemoteLog) {
      _loggy.info("$packageName—$message");
      return;
    }
    return await _storeLogEvent(LogLevel.info, message, packageName);
  }

  Future<void> _warn(String message, String packageName) async {
    if (_excludedLevels.containsKey(LogLevel.warn.level)) {
      return;
    }
    if (!_canRemoteLog) {
      _loggy.warning("$packageName—$message");
      return;
    }
    return await _storeLogEvent(LogLevel.warn, message, packageName);
  }

  Future<void> _error(String message, String packageName) async {
    if (_excludedLevels.containsKey(LogLevel.error.level)) {
      return;
    }
    if (!_canRemoteLog) {
      _loggy.error("$packageName—$message");
      return;
    }
    return await _storeLogEvent(LogLevel.error, message, packageName);
  }

  Future<void> _critical(String message, String packageName) async {
    if (_excludedLevels.containsKey(LogLevel.critical.level)) {
      return;
    }
    if (!_canRemoteLog) {
      _loggy.error("$packageName—$message");
      return;
    }
    return await _storeLogEvent(LogLevel.critical, message, packageName);
  }

  Future<void> _storeLogEvent(
      LogLevel level, String message, String packageName) async {
    // return await logLock.synchronized(() async {
    //   if (kDebugMode) {
    //     print(
    //         'LOGGER - Storing log event from $packageName with level: $level: $message');
    //   }
    //   final Credentials credentials =
    //   await _localStorageRepository.getCredentials();
    //
    //   // Restart the time on new log entry
    //   _startTimer();
    //
    //   if (credentials.isLoggedIn) {
    //     final LogMetadata metadata = await _getMetadata();
    //     final int timestamp = DateTime.now().millisecondsSinceEpoch;
    //     final logEvent = LogEvent(
    //         timestamp: timestamp,
    //         packageName: packageName,
    //         level: level.level,
    //         metadata: metadata,
    //         message: message);
    //
    //     if (kDebugMode) {
    //       print(
    //           '${DateTime.now()}: LOGGER - Generated log entry: ${logEvent.message}');
    //     }
    //
    //     // Insert the new log entry
    //     await _localStorageRepository.addLogEvent(logEvent);
    //     List<LogEvent> logs = await _localStorageRepository.getLogs();
    //
    //     if (kDebugMode) {
    //       print('LOGGER - Nb Stored Logs: ${logs.length}');
    //     }
    //
    //     // If the max log has been reached, flush the logs
    //     if (logs.length == maxLogs) {
    //       if (kDebugMode) {
    //         print('LOGGER - Max logs reached, logging...');
    //       }
    //       await _log();
    //     }
    //     return;
    //   }
    //   if (kDebugMode) {
    //     print('LOGGER - No orgId provided, skipping...');
    //   }
    // });
  }

  void _startTimer() {
    if (kDebugMode) {
      print('LOGGER - Starting timer...');
    }
    if (isTimerActive()) {
      _timer?.cancel();
    }
    _timer = Timer.periodic(const Duration(minutes: minutes), (Timer t) async {
      await _log();
    });
  }

  void _stopTimer() {
    if (isTimerActive()) {
      _timer?.cancel();
    }
  }

  bool isTimerActive() {
    return _timer != null && _timer!.isActive;
  }

  Future<void> _log() async {
    // If the timer is not active, this means the app was placed
    // in foreground and therefore we are not attempting to log anymore
    if (!isTimerActive()) {
      return;
    }

    // If no internet connection, do not attempt to send them to the Shim Service
    // Connectivity().checkConnectivity().then((results) {
    //   final result = results.last;
    //   if (result == ConnectivityResult.none) {
    //     if (kDebugMode) {
    //       print('LOGGER - No internet connection, skipping...');
    //     }
    //     return;
    //   }
    // });

    // final Credentials credentials =
    // await _localStorageRepository.getCredentials();

    // if (credentials.isLoggedIn) {
    //   List<LogEvent> logs = await _localStorageRepository.getLogs();
    //   if (logs.isNotEmpty) {
    //     if (kDebugMode) {
    //       print('LOGGER - Logging ${logs.length} messages...');
    //     }
    //     final response = await _loggingRepository.putLogEvents(
    //         credentials.orgId!, LogEventBody(events: logs));
    //     if (response is! Error) {
    //       await _localStorageRepository.clearLogs();
    //     }
    //     return;
    //   }
    //   if (kDebugMode) {
    //     print('LOGGER - Logs are empty, skipping...');
    //   }
    //   return;
    // }
  }

  // Generate the metadata for the log entry
  // Future<LogMetadata> _getMetadata() =>
  //     _environmentInfoRepository.getLogMetadata();
}
