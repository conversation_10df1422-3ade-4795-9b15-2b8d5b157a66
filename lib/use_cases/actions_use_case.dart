import 'dart:async';

import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/repositories/actions_repository.dart';

import 'list_object_use_case.dart';

class ActionsUseCase extends ListObjectUseCase<CardAction> {
  final ActionsRepository _repository;

  ActionsUseCase(this._repository) : super();

  @override
  Future<void> init() {
    return _getActions();
  }

  ProcessingStatus _status = ProcessingStatus.start;

  @override
  ProcessingStatus get status => _status;

  @override
  List<CardAction> get objects => _actions;

  final List<CardAction> _actions = [];

  final Completer<void> _initCompleter = Completer<void>();

  Future<void> _getActions() async {
    if (_status == ProcessingStatus.loading) {
      return _initCompleter.future;
    }
    if (_status == ProcessingStatus.start) {
      _status = ProcessingStatus.loading;
      final actions = await _repository.get();
      _actions.addAll(actions as Iterable<CardAction>);
      _status = ProcessingStatus.loaded;
      _initCompleter.complete();
    }
  }

  @override
  void selectObject(object) {
    // TODO: implement selectObject
  }
}