import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/mapping/positioned_card_2d.dart';
import 'package:dauntless/models/base/mapping/positioned_grid_line_2d.dart';
import 'package:dauntless/models/base/mapping/positioned_label_2d.dart';
import 'package:dauntless/models/dto/grid_labels_dto.dart';
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart';

class MapGridUseCase {
  (double, Vector2) _getScaleAndOffset({
    required int gridPxWidth,
    required int gridPxHeight,
    required double maxX,
    required double maxY,
    required double minX,
    required double minY,
    required EdgeInsets padding,
  }) {
    final GridExtents mapExtents = MaxMinGridExtents.create(
      minX,
      minY,
      maxX,
      maxY,
    );

    final GridExtents screenExtents = MaxMinGridExtents.create(
      0,
      0,
      gridPxWidth.toDouble() -
          padding.left.toDouble() -
          padding.right.toDouble(),
      gridPxHeight.toDouble() -
          padding.top.toDouble() -
          padding.bottom.toDouble(),
    );

    final double scaleFactor = mapExtents.getScaleFactor(
        screenExtents); // screenExtents.getScaleFactor(mapExtents);

    final Vector2 mapCenterPoint = Vector2(
      (maxX + minX) / 2,
      (maxY + minY) / 2,
    );

    final screenCenterPoint = Vector2(
      gridPxWidth / 2,
      gridPxHeight / 2,
    );

    final Vector2 screenOffeset =
        screenCenterPoint - mapCenterPoint * scaleFactor;
    return (scaleFactor, screenOffeset);
  }

  (List<PositionedCard2d>, double, Vector2) getPositionedCards({
    required List<GameCardClass> cards,
    required int gridPxWidth,
    required int gridPxHeight,
    required EdgeInsets padding,
  }) {
    /// Load map extents if provided //TODO: implement
    /// or get from items
    double maxX = 0;
    double maxY = 0;
    double minX = 0;
    double minY = 0;

    for (final GameCardClass card in cards) {
      final Vector2 mapCoordinates = card.gridCoordinates ?? Vector2.zero();
      maxX = mapCoordinates.x > maxX ? mapCoordinates.x : maxX;
      maxY = mapCoordinates.y > maxY ? mapCoordinates.y : maxY;
      minX = mapCoordinates.x < minX ? mapCoordinates.x : minX;
      minY = mapCoordinates.y < minY ? mapCoordinates.y : minY;
    }

    final (scaleFactor, screenOffeset) = _getScaleAndOffset(
      gridPxWidth: gridPxWidth,
      gridPxHeight: gridPxHeight,
      maxX: maxX,
      maxY: maxY,
      minX: minX,
      minY: minY,
      padding: padding,
    );

    List<PositionedCard2d> positionedCards = cards
        .map((e) => PositionedCard2d.fromGameCard(
            GameCard.fromGameCardClass(e, id: e.id), e,
            scaleFactor: scaleFactor, screenOffset: screenOffeset))
        .whereType<PositionedCard2d>()
        .toList();

    /// ************  DEBUG ************
    /// Check Positioned Cards
    maxX = 0;
    maxY = 0;
    minX = 0;
    minY = 0;
    for (final card in positionedCards) {
      final Vector2 screenCoords = card.screenCoordinates ?? Vector2.zero();
      maxX = screenCoords.x > maxX ? screenCoords.x : maxX;
      maxY = screenCoords.y > maxY ? screenCoords.y : maxY;
      minX = screenCoords.x < minX ? screenCoords.x : minX;
      minY = screenCoords.y < minY ? screenCoords.y : minY;
    }
    print('max/min screen coord distro: $minX, $minY, $maxX, $maxY');

    /// ************  DEBUG ************
    return (positionedCards, scaleFactor, screenOffeset);
  }

  List<PositionedLabel2d> getPositionedLabels({
    List<GridLabelComponent> rowLabels = const [],
    List<GridLabelComponent> columnLabels = const [],
    double scaleFactor = 1.0,
    Vector2? offset,
  }) {
    final List<PositionedLabel2d> labels = [];
    for (final label in rowLabels) {
      final Vector2 screenCoordinates = Vector2(
        100,
        label.$1 * scaleFactor,
      )..add(offset ?? Vector2.zero());
      labels.add(PositionedLabel2d(
        label: label.$2,
        coordinates: screenCoordinates,
      ));
    }

    for (final label in columnLabels) {
      final Vector2 screenCoordinates = Vector2(
        label.$1 * scaleFactor,
        100,
      )..add(offset ?? Vector2.zero());
      labels.add(PositionedLabel2d(
        label: label.$2,
        coordinates: screenCoordinates,
      ));
    }

    return labels;
  }

  // TODO: align these to map ... currently set to screen
  @Deprecated('positioning not right')
  List<PositionedGridLine2d> getGeneratedGridLines({
    required int gridPxWidth,
    required int gridPxHeight,
    required double gridSize,
    Vector2? offset,
    int thickLineInterval = 5,
  }) {
    if (gridSize == 0 || gridSize.isInfinite || gridSize.isNaN) {
      return [];
    }
    final List<PositionedGridLine2d> gridLines = [];
    final startingX = -(offset ?? Vector2.zero()).x;
    final int maxXLines = (gridPxWidth - startingX) ~/ gridSize;

    for (int i = 0; i <= maxXLines; i++) {
      final double x = startingX + i * gridSize;
      final GridLineType type =
          i % thickLineInterval == 0 ? GridLineType.major : GridLineType.minor;
      gridLines.add(PositionedGridLine2d(
        position: x,
        axis: Axis.vertical,
        type: type,
      ));
    }

    final startingY = -(offset ?? Vector2.zero()).y;
    final int maxYLines = (gridPxHeight - startingY) ~/ gridSize;

    for (int i = 0; i <= maxYLines; i++) {
      final double y = startingY + i * gridSize;
      final GridLineType type =
          i % thickLineInterval == 0 ? GridLineType.major : GridLineType.minor;
      gridLines.add(PositionedGridLine2d(
        position: y,
        axis: Axis.horizontal,
        type: type,
      ));
    }

    return gridLines;
  }
}
