import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/api/dtos/create_match_dto.dart';
// Unused import removed
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/base/game_match.dart';
import 'package:dauntless/models/base/match_config.dart'; // For GameMode enum, MatchConfig
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:path/path.dart' as path;

// Type aliases to match the codebase conventions using modern Dart syntax
typedef GameMatchId = String;
typedef PlayerId = String;

/// UseCase that handles match selection operations
abstract class MatchSelectionUseCase {
  /// source name
  final String name = 'unknown';

  /// current implementation only supports one server connection at a time; could be refactored to support more -- requires managing multiple websocket connections and dio/api
  final bool isServerSource = false;
  Future<List<GameMatch>> fetchOpenMatches(String gameName);

  Future<GameMatch?> openNewMatch(GameMatch newMatch, GameConfig gameConfig);

  Future<bool> joinMatch(String matchId, {PlayerId? playerId});

  Future<bool> leaveMatch(String matchId, {PlayerId? playerId});

  Future<bool> deleteMatch(String matchId);
}

class LocalMatchSelectionUseCase extends MatchSelectionUseCase {
  @override
  get name => 'Local';

  final RemoteLogger? _logger;

  LocalMatchSelectionUseCase(this._logger);

  /// Load locally saved games from the file system
  @override
  Future<List<GameMatch>> fetchOpenMatches(String gameName) async {
    try {
      _logger?.info('Loading saved games for $gameName');

      // Build the path to the saved games directory
      final savesDir = Directory(path.join('games', gameName, 'savedGames'));

      // Check if the directory exists
      if (!await savesDir.exists()) {
        _logger?.warn('Saved games directory does not exist: ${savesDir.path}');
        return [];
      }

      final savedGames = <GameMatch>[];

      // Iterate through all files in the directory
      await for (final entity in savesDir.list()) {
        if (entity is File && entity.path.endsWith('.json')) {
          try {
            // Read and parse the saved game file
            final jsonContent = await entity.readAsString();
            final jsonData = jsonDecode(jsonContent) as Map<String, dynamic>;

            // Create a GameMatch object from the JSON data
            final gameMatch = GameMatch.fromJson(jsonData);

            // Add metadata about the save file
            final stat = await entity.stat();
            final fileName = path.basename(entity.path);

            // Add file metadata to the match settings
            // final updatedSettings = Map<String, dynamic>.from(gameMatch.settings);
            // updatedSettings['lastModified'] = stat.modified.toIso8601String();
            // updatedSettings['saveFileName'] = fileName;
            // updatedSettings['localSaveGame'] = true;

            // Create updated GameMatch with the new settings
            // final updatedMatch = gameMatch.copyWith(settings: updatedSettings);
            // savedGames.add(updatedMatch);

            _logger?.info('Loaded saved game: $fileName');
          } catch (e) {
            _logger?.error('Error parsing saved game file ${entity.path}: $e');
            // Continue to next file even if this one fails
          }
        }
      }

      _logger?.info('Loaded ${savedGames.length} saved games for $gameName');
      return savedGames;
    } catch (e) {
      _logger?.error('Error loading saved games: $e');
      print('Error loading saved games: $e');
      return [];
    }
  }

  @override
  Future<bool> joinMatch(String matchId, {PlayerId? playerId}) async {
    // TODO: implement joinMatch for local
    return true;
  }
  
  @override
  Future<bool> leaveMatch(String matchId, {PlayerId? playerId}) async {
    // TODO: implement leaveMatch for local
    return true;
  }

  @override
  Future<bool> deleteMatch(String matchId) async {
    return true;
  }

  @override
  Future<GameMatch?> openNewMatch(GameMatch newMatch, GameConfig gameConfig) async {
    return null;
  }
}

class NetworkMatchSelectionUseCase extends MatchSelectionUseCase {
  @override
  get name => _name ?? 'Network';
  @override
  bool get isServerSource => true;
  final ServerRepository _serverRepository;
  final RemoteLogger? _logger;

  final String? _name;

  NetworkMatchSelectionUseCase(this._serverRepository, this._logger,
      [this._name]);

  @disposeMethod // TODO: this "add" is being done in the network_modules module; can we move this there or that here?
  void dispose() {
    GetIt.I<MatchSelectionEnvironmentManager>()
        .add(RemoveMatchSelectionUseCase(name));
  }

  /// Fetch available matches from the server
  @override
  Future<List<GameMatch>> fetchOpenMatches(String gameName) async {
    try {
      _logger?.info('Fetching open matches for $gameName');

      // Use reactive repository if available, otherwise fall back to the regular one
      final matches = await _serverRepository.fetchOpenMatches(gameName);

      _logger?.info('Fetched ${matches.length} open matches');

      return matches;
    } catch (e) {
      _logger?.error('Error fetching open matches: $e');
      print('Error fetching available matches: $e');
      return [];
    }
  }

  /// Create a match from template
  @override
  Future<GameMatch?> openNewMatch(GameMatch newMatch, GameConfig gameConfig) async {
    try {
      _logger?.info(
        'Creating match from game: ${gameConfig.name}, id: ${gameConfig.id}',
      );

      // We need a current user ID for the host
      // TODO: update to use usecase or whatever architecture we end up with
      final currentUserId =
          GetIt.I<UserManager>().state.user?.id ?? 'ERROR-NO-ID-PROVIDED';

      final gameName = newMatch.gameName;
      // Create a MatchConfig from the GameConfig
      final matchConfig = MatchConfig(
        gameId: gameConfig.id,
        // Use the game type ID from the game config
        selectedGameMode: GameMode.server,
        // We're creating a server match
        hostId: currentUserId,
        // Set the current user as host
        matchId: "",
        // Empty string - will be generated by server
        name: gameName?.isNotEmpty ?? false
            ? gameName!
            : gameConfig.name, // Use custom name if provided
      );

      _logger?.info(
        'Creating match: ${matchConfig.name}, mode: ${matchConfig.selectedGameMode}',
      );

      // Convert PlayerSlots to PlayerSlotDto objects for the DTO
      // final playerSlotDtos = playerSlots.map((slot) => PlayerSlotDto(
      //   id: slot.id,
      //   name: slot.name ?? 'Unnamed Player',
      //   playerClassId: slot.playerClassId ?? '',
      //   type: slot.type.toString().split('.').last, // Convert enum to string
      //   isHost: slot.id == state.hostId,
      // )).toList();

      // Create a more comprehensive DTO with all the state information
      // final createMatchDto = CreateMatchDto(
      //   gameTypeId: gameConfig.id,
      //   creatorId: currentUserId,
      //   // settings: {
      //   //   'name': state.gameName.isNotEmpty ? state.gameName : gameConfig.name,
      //   //   'game_mode': GameMode.server.toString().split('.').last,
      //   //   'max_players': state.playerSlots.length,
      //   //   'is_open_for_joining': true,
      //   //   'playerSlotCount': state.playerSlots.length,
      //   // },
      //   // isOpenForJoining: true,
      //   // maxPlayers: state.playerSlots.length,
      //   gameName: state.gameName.isNotEmpty ? state.gameName : gameConfig.name,
      //   playerSlots: playerSlotDtos,
      // );

      // final playerSlots = matchConfig.players.map((playerClass) => PlayerSlotDto(
      //   id: playerClass.id,
      //   name: playerClass.name,
      //   playerClassId: playerClass.id,
      //   type: 'humanLocal', // Default to human local
      //   isHost: playerClass.id == matchConfig.hostId,
      // )).toList();

      // Assemble the DTO for the repository
      final createMatchDto = CreateMatchDto(
        gameTypeId: matchConfig.gameId,
        creatorId: matchConfig.hostId,
        gameName: gameName?.isNotEmpty == true ? gameName : matchConfig.name,
        playerSlots: newMatch.playerSlots,
        // settings: {
        //   'name': matchConfig.name,
        //   'game_mode': matchConfig.selectedGameMode.toString().split('.').last,
        //   'max_players': maxPlayers,
        //   'is_open_for_joining': isOpenForJoining,
        //   // Also add top-level equivalent fields to help server transition
        //   'playerSlotCount': maxPlayers,
        // },
      );

      // Debug: log the DTO content
      _logger?.info('CreateMatchDto content: ${createMatchDto.toString()}');
      try {
        final jsonData = createMatchDto.toJson();
        _logger?.info('CreateMatchDto JSON: $jsonData');
        print('DEBUG: CreateMatchDto JSON: $jsonData');
      } catch (e) {
        _logger?.error('Failed to serialize CreateMatchDto to JSON: $e');
        print('ERROR: Failed to serialize CreateMatchDto to JSON: $e');
      }

      // Call the repository with the assembled DTO
      final match = await _serverRepository.createMatch(createMatchDto);

      if (match != null) {
        _logger?.info('Match created with ID: ${match.id}');
        return match;
      } else {
        _logger?.error('Failed to create match');
        return null;
      }
    } catch (e) {
      _logger?.error('Error creating match from template: $e');
      print('Error creating match from template: $e');
      return null;
    }
  }

  /// Join a match by ID
  @override
  Future<bool> joinMatch(String matchId, {PlayerId? playerId}) async {
    try {
      if (playerId == null) {
        throw Exception('Player ID cannot be null when joining a match');
      }
        
      _logger?.info('Player $playerId joining match: $matchId');
      return await _serverRepository.joinMatch(matchId, playerId);
    } catch (e) {
      _logger?.error('Error joining match: $e');
      print('Error joining match: $e');
      return false;
    }
  }
  
  /// Leave a match by ID
  @override
  Future<bool> leaveMatch(String matchId, {PlayerId? playerId}) async {
    try {
      if (playerId == null) {
        throw Exception('Player ID cannot be null when leaving a match');
      }
        
      _logger?.info('Player $playerId leaving match: $matchId');
      return await _serverRepository.leaveMatch(matchId, playerId);
    } catch (e) {
      _logger?.error('Error leaving match: $e');
      print('Error leaving match: $e');
      return false;
    }
  }

  /// Delete a match by ID
  @override
  Future<bool> deleteMatch(String matchId) async {
    try {
      _logger?.info('Deleting match with ID: $matchId');

      if (matchId.isEmpty) {
        print('ERROR: Attempted to delete match with empty ID');
        _logger?.error('Attempted to delete match with empty ID');
        return false;
      }

      // Use reactive repository if available
      final success = await _serverRepository.deleteMatch(matchId);

      if (success) {
        _logger?.info('Match successfully deleted: $matchId');
      } else {
        _logger?.error('Failed to delete match: $matchId');
      }

      return success;
    } catch (e) {
      _logger?.error('Error deleting match: $e');
      print('Error deleting match: $e');
      return false;
    }
  }
}
