import 'package:dauntless/frameworks/game_config/game_config_state.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/models/base/saved_game_state.dart';
import 'package:dauntless/repositories/save_state_repository.dart';
import 'package:get_it/get_it.dart';

import 'game_config_use_case.dart';

class MatchSaveUseCase {
  final SaveStateRepository _saveStateRepository;

  MatchSaveUseCase(this._saveStateRepository);

  // TODO: refactor GetIt out
  String? getSavePath(GameConfigState gameConfigState) => (gameConfigState
                  .basePath ==
              null ||
          gameConfigState.loadedGameConfig == null)
      ? null
      : "${gameConfigState.basePath!}$gameSavePath${GetIt.I<GameMatchManager>().state.matchConfig.matchId}";

  Future<void> saveSavedGameState(
      GameConfigState gameConfigState, SavedGameState savedGameState) async {
    final savePath = getSavePath(gameConfigState);
    if (savePath == null) {
      // TODO: handle error
      throw Exception('Save path is not set');
    }
    final saveFileName =
        '/${savedGameState.matchState.turnCountString}.json';
    await _saveStateRepository.saveSavedGameState(
        savePath + saveFileName, savedGameState);
  }

  Future<SavedGameState?> getMostRecentSaveForGame(
      GameConfigState gameConfigState) async {
    final savePath = getSavePath(gameConfigState);
    if (savePath == null) {
      // TODO: handle error
      throw Exception('Save path is not set');
    }
    final saveFileEntities = await _saveStateRepository.getGameSaves(savePath);

    saveFileEntities.sort((a, b) => b.path.compareTo(a.path));

    if (saveFileEntities.isEmpty) {
      return null;
    }

    final loadedSave = await _saveStateRepository
        .loadSavedGameState(saveFileEntities.first.path);

    return loadedSave;
  }
}
