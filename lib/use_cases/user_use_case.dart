import 'dart:io';
import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/frameworks/user/user_config.dart';
import 'package:dauntless/frameworks/user/user_profile.dart';

class UserUseCase {
  UserUseCase();

  /// Config file path constants
  static const String _configFilePath = 'users_config.json';

  /// Load user config from JSON file
  Future<UserConfig> loadUserConfig([String path = _configFilePath]) async {
    try {
      // Check if file exists
      final file = File(path);
      if (!await file.exists()) {
        // Return default config with empty profiles
        return const UserConfig();
      }

      // Parse existing config file
      final configFile = await JsonReadWriteDataService.parseUserConfigFromJsonFile(path);
      return configFile;
    } catch (e) {
      print('USER_USE_CASE: Error loading user config: $e');
      // Return default config on error
      return const UserConfig();
    }
  }

  /// Save user config to JSON file
  Future<void> saveUserConfig(UserConfig config, [String path = _configFilePath]) async {
    try {
      await JsonReadWriteDataService.writeJsonToFile(path, config.toJson());
      print('USER_USE_CASE: User config saved to $path');
    } catch (e) {
      print('USER_USE_CASE: Error saving user config: $e');
      throw Exception('Failed to save user config: $e');
    }
  }

  /// Add a new user profile
  Future<UserConfig> addUserProfile(UserConfig currentConfig, UserProfile profile) async {
    // Check if profile with same ID already exists
    if (currentConfig.hasProfile(profile.id)) {
      throw Exception('Profile with ID ${profile.id} already exists');
    }

    final updatedProfiles = [...currentConfig.profiles, profile];
    final updatedConfig = currentConfig.copyWith(profiles: updatedProfiles);
    await saveUserConfig(updatedConfig);
    return updatedConfig;
  }

  /// Update an existing user profile
  Future<UserConfig> updateUserProfile(UserConfig currentConfig, UserProfile updatedProfile) async {
    final profileIndex = currentConfig.profiles.indexWhere((p) => p.id == updatedProfile.id);
    if (profileIndex == -1) {
      throw Exception('Profile with ID ${updatedProfile.id} not found');
    }

    final updatedProfiles = [...currentConfig.profiles];
    updatedProfiles[profileIndex] = updatedProfile;
    final updatedConfig = currentConfig.copyWith(profiles: updatedProfiles);
    await saveUserConfig(updatedConfig);
    return updatedConfig;
  }

  /// Remove a user profile
  Future<UserConfig> removeUserProfile(UserConfig currentConfig, String profileId) async {
    if (!currentConfig.hasProfile(profileId)) {
      throw Exception('Profile with ID $profileId not found');
    }

    final updatedProfiles = currentConfig.profiles.where((p) => p.id != profileId).toList();

    // If we're removing the selected profile, clear the selection
    String? newSelectedProfileId = currentConfig.selectedProfileId;
    if (currentConfig.selectedProfileId == profileId) {
      newSelectedProfileId = updatedProfiles.isNotEmpty ? updatedProfiles.first.id : null;
    }

    final updatedConfig = currentConfig.copyWith(
      profiles: updatedProfiles,
      selectedProfileId: newSelectedProfileId,
    );
    await saveUserConfig(updatedConfig);
    return updatedConfig;
  }

  /// Set the selected profile
  Future<UserConfig> setSelectedProfile(UserConfig currentConfig, String? profileId) async {
    if (profileId != null && !currentConfig.hasProfile(profileId)) {
      throw Exception('Profile with ID $profileId not found');
    }

    final updatedConfig = currentConfig.copyWith(selectedProfileId: profileId);
    await saveUserConfig(updatedConfig);
    return updatedConfig;
  }
}
