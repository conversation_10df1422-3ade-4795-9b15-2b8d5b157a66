import 'dart:io';
import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_config.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:get_it/get_it.dart';

class ServerEnvironmentUseCase {
  ServerEnvironmentUseCase();
  /// Config file path constants
  static const String _assetConfigFilePath = 'assets/config/server_config.json';
  static const String _writableConfigFilePath = 'server_config.json';

  Future<ServerEnvironmentConfig> loadServerEnvironmentConfig(
      [String? path]) async {
    // Try writable config first, then fall back to asset config
    final configPath = path ?? _writableConfigFilePath;
    
    try {
      // Check if writable config exists
      final file = File(configPath);
      if (await file.exists()) {
        final configFile = await JsonReadWriteDataService.parseServerConfigsFromJsonFile(configPath);
        return configFile;
      }
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error loading writable config: $e');
    }
    
    // Fall back to asset config
    try {
      final configFile = await JsonReadWriteDataService.parseServerConfigsFromJsonFile(_assetConfigFilePath);
      return configFile;
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error loading asset config: $e');
      // Return default config with empty profiles
      return const ServerEnvironmentConfig();
    }
  }

  /// Save server environment config to JSON file
  Future<void> saveServerEnvironmentConfig(ServerEnvironmentConfig config, [String? path]) async {
    final configPath = path ?? _writableConfigFilePath;
    try {
      await JsonReadWriteDataService.writeJsonToFile(configPath, config.toJson());
      print('SERVER_ENVIRONMENT_USE_CASE: Server config saved to $configPath');
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error saving server config: $e');
      throw Exception('Failed to save server config: $e');
    }
  }

  Future<bool> testConnection() async {
    try {
      /// TODO: / Important NOTE!!! the serverRepo needs this useCase for config; cannot be imported here -- need to CLEAN this up
      final serverRepository = GetIt.I<ServerRepository>();
      await serverRepository.testConnection();
      return true;
    } catch (e) {
      print('SERVER_ENVIRONMENT_USE_CASE: Error testing connection: $e');
      return false;
    }
  }
}
