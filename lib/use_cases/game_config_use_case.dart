import 'package:collection/collection.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/repositories/game_config_repository.dart';

// TODO: figure out where to handle this?
const String gameConfigPath = '/data/config.json';
const String gameSavePath = '/game_saves/';

class GameConfigUseCase {
  final GameConfigRepository _repository;

  GameConfigUseCase(this._repository);

  Future<GameConfig> loadGameConfig(String basePath) async {
    final loadedConfig = await _repository.loadGameConfig(basePath + gameConfigPath);
    return loadedConfig;
  }

  Future<List<GameConfig>> loadLocalGameConfigs() {
    return _repository.loadAvailableGameConfigs();
  }

  Future<GameConfig?> getGameConfigById(String matchConfigId) async {
    final availableConfigs = await loadLocalGameConfigs();
    if (availableConfigs.firstWhereOrNull((config) => config.id == matchConfigId) == null) throw Exception('need to handle this error');
    return availableConfigs.firstWhereOrNull((config) => config.id == matchConfigId);
  }
}