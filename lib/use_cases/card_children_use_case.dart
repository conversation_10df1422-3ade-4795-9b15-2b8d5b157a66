// import 'package:dauntless/models/base/game_card.dart';
// import 'package:dauntless/models/games/liberator/repositories/locations_repository.dart';
// import 'package:dauntless/models/games/liberator/repositories/vehicles_repository.dart';
// import 'package:dauntless/repositories/game_card_repository.dart';
//
// class CardChildrenUseCase {
//   late final Map<CardType, GameCardRepository<GameCardType>> _repositories;
//
//   CardChildrenUseCase(
//   LocationsRepository locationsRepository,
//   VehiclesRepository vehiclesRepository) {
//     _repositories = {
//       CardType.location: locationsRepository,
//       CardType.vehicle: vehiclesRepository
//     };
//   }
//
//   Future<Map<CardType, List<GameCard>>> getAllCardChildren(GameCardId cardId) async {
//     final children = <CardType, List<GameCard>>{};
//     for (final entry in _repositories.entries) {
//       final cards = await entry.value.get();
//       final groupings = await entry.value.getGroupings();
//
//       // (cards, groupings) = await Future.wait([
//       //   entry.value.get(),
//       //   entry.value.getGroupings()
//       // ]);
//       children[entry.key] = cards.where((card) => card.locationId == cardId).toList();
//     }
//     return children;
// }
//
//   Future<List<GameCard>?> getCardChildren(GameCardId cardId) async {
//     final cardType = CardType.fromType<T>();
//     final repository = _repositories[cardType] as GameCardRepository<T>?;
//     final cards = await repository?.get();
//     return cards?.where((card) => card.locationId == cardId).toList();
//   }
// }