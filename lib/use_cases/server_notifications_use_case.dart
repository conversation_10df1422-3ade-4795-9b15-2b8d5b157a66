import 'dart:async';

import 'package:common/models/game_match.dart';
import 'package:dauntless/models/base/game_match.dart' as base_model;
import 'package:injectable/injectable.dart';
import 'package:dauntless/repositories/websocket_repository.dart';

/// UseCase for handling WebSocket operations
@singleton
class ServerNotificationsUseCase {
  /// WebSocket repository
  final WebSocketRepository _repository;
  // final ServerRepository _serverRepository;

  /// Map of match factories
  final Map<String, GameMatch Function(Map<String, dynamic>)> _matchFactories =
      {};

  /// Constructor
  ServerNotificationsUseCase(this._repository);

  /// Stream of game_match updates
  Stream<GameMatch> get matchUpdates => _repository.matchUpdates;

  /// Stream of open matches updates
  Stream<List<base_model.GameMatch>> get openMatchesUpdates =>
      _repository.getTopicStream('open_matches')
        .map<List<base_model.GameMatch>>((dynamic data) {
          print('ServerNotificationsUseCase: ⚠️ Processing open_matches update: $data');
          print('ServerNotificationsUseCase: 🔍 Stream has active listeners: ${_repository.hasTopicListeners('open_matches')}');
          print('ServerNotificationsUseCase: 🔄 Topic subscription debug info: ${_repository.getTopicDebugInfo()}');
          
          // Add console timestamp to help correlate logs
          final timestamp = DateTime.now().toIso8601String();
          print('ServerNotificationsUseCase: ⏱️ Timestamp: $timestamp');
          
          final matches = <base_model.GameMatch>[];
          try {
            if (data is Map<String, dynamic>) {
              print('ServerNotificationsUseCase: Data is Map with keys: ${data.keys.join(', ')}');
              if (data['data'] != null) {
                print('ServerNotificationsUseCase: data["data"] = ${data['data']}');
                final dataMap = data['data'] as Map<String, dynamic>?;
                if (dataMap != null) {
                  print('ServerNotificationsUseCase: dataMap keys: ${dataMap.keys.join(', ')}');
                  final matchesData = dataMap['matches'] as List<dynamic>?;
                  if (matchesData != null) {
                    print('ServerNotificationsUseCase: Found ${matchesData.length} matches in data');
                    for (final matchData in matchesData) {
                      if (matchData is Map<String, dynamic>) {
                        print('ServerNotificationsUseCase: Match data keys: ${matchData.keys.join(', ')}');
                        final gameTypeId = matchData['gameTypeId'] as String?;
                        print('ServerNotificationsUseCase: Match gameTypeId: $gameTypeId');
                        
                        // Always try to convert the match data, even if we don't have a specific factory
                        try {
                          base_model.GameMatch gameMatch;
                          
                          // Check if we have a specific factory for this game type
                          if (gameTypeId != null && _matchFactories.containsKey(gameTypeId)) {
                            final factory = _matchFactories[gameTypeId]!;
                            print('ServerNotificationsUseCase: Using factory for gameTypeId: $gameTypeId');
                            gameMatch = factory(matchData);
                          } else {
                            // Use default deserialization if no specific factory exists
                            print('ServerNotificationsUseCase: Using default GameMatch deserialization');
                            gameMatch = base_model.GameMatch.fromJson(matchData);
                          }
                          
                          matches.add(gameMatch);
                          print('ServerNotificationsUseCase: Successfully created and added match: ${gameMatch.id}');
                        } catch (e) {
                          print('ServerNotificationsUseCase: Error deserializing match: $e');
                          print('ServerNotificationsUseCase: Raw match data: $matchData');
                        }
                      }
                    }
                  } else {
                    print('ServerNotificationsUseCase: No "matches" field in data');
                  }
                } else {
                  print('ServerNotificationsUseCase: data["data"] is not a Map');
                }
              } else {
                print('ServerNotificationsUseCase: data["data"] is null');
              }
            } else {
              print('ServerNotificationsUseCase: Data is not a Map: ${data.runtimeType}');
            }
          } catch (e) {
            print('ServerNotificationsUseCase: Error processing open matches: $e');
          }
          print('ServerNotificationsUseCase: Returning ${matches.length} matches');
          return matches;
        });

  /// Stream of connection status updates
  Stream<bool> get connectionStatusUpdates =>
      _repository.connectionStatusUpdates;

  /// Get connection status
  bool get isConnected => _repository.isConnected;

  /// Get stream for a specific topic
  Stream<dynamic> getTopicStream(String topic) =>
      _repository.getTopicStream(topic);

  /// Subscribe to a specific topic
  Future<void> subscribeToTopic(String topic) async {
    await _repository.subscribeToTopic(topic);
  }

  /// Unsubscribe from a specific topic
  void unsubscribeFromTopic(String topic) {
    _repository.unsubscribeFromTopic(topic);
  }

  /// Send a custom message to the WebSocket server
  void sendMessage(Map<String, dynamic> message) {
    _repository.sendMessage(message);
  }

  /// Connect to the WebSocket server
  Future<void> connect() async {
    try {
      await _repository.connect();
    } catch (e) {
      // If WebSocket connection fails, try to fetch data via HTTP
      // await refreshDataViaHttp();
      rethrow;
    }
  }

  /// Disconnect from the WebSocket server
  void disconnect() {
    _repository.disconnect();
  }

  /// Subscribe to open matches updates
  Future<void> subscribeToOpenMatches() async {
    await _repository.subscribeToTopic('open_matches');
  }

  /// Unsubscribe from open matches updates
  void unsubscribeFromOpenMatches() {
    _repository.unsubscribeFromTopic('open_matches');
  }

  /// Subscribe to match actions for a specific match
  Future<void> subscribeToMatchActions(String matchId) async {
    await subscribeToTopic('match:${matchId}:actions');
  }

  /// Unsubscribe from match actions for a specific match
  void unsubscribeFromMatchActions(String matchId) {
    unsubscribeFromTopic('match:${matchId}:actions');
  }

  /// Send an action for a match
  void sendMatchAction(String matchId, Map<String, dynamic> action) {
    final message = {
      'type': 'match_action',
      'topic': 'match:$matchId:actions',
      'data': action,
    };

    sendMessage(message);
  }
}
