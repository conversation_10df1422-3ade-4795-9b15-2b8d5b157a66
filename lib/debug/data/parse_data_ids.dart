// import 'package:freezed_annotation/freezed_annotation.dart';
// import 'dart:convert';
// import 'dart:io';
//
// part 'parse_data_ids.freezed.dart';
// part 'parse_data_ids.g.dart';
//
// @freezed
// class Item with _$Item {
//   factory Item({
//     required String name,
//     required String id,
//     @Default({}) Map<String, dynamic> extraFields}) = _Item;
//
//   factory Item.fromJson(Map<String, dynamic> json) => _$ItemFromJson(json);
// }
//
// void parseDataIds() {
//   final jsonData =
//   jsonDecode(File('/Users/<USER>/dev/dauntless//Users/<USER>/dev/dauntless/games/liberator/data/rebellion_alliance_ships.json').readAsStringSync());
//   for (var item in jsonData['items']) {
//     final name = item['name'];
//     final newName = name
//         .split(' ')
//         .map((e) => e[0].toUpperCase() + e.substring(1))
//         .join('');
//     final newId = name.toLowerCase().replaceAll(' ', '_');
//     item['name'] = newName;
//     item['id'] = newId;
//
//     // Preserve all extra fields
//     for (var key in item.keys) {
//       if (key != 'name' && key != 'id') {
//         item.remove(key);
//         item.addAll({key: item[key]});
//       }
//     }
//   }
//
//   final outputFilePath =
//       '/Users/<USER>/dev/dauntless//Users/<USER>/dev/dauntless/games/liberator/data/rebellion_rebel_ships_updated.json';
//   File(outputFilePath).writeAsStringSync(jsonEncode(jsonData));
// }
