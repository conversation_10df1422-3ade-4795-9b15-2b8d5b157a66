import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GlobalBlocObserver extends BlocObserver {
  final RegExp _blocBlacklistPatterns = RegExp(
    r'^(Positions2dBloc|MapInstanceBloc|SingleCardBloc—.*?)$'
  );
  
  final RemoteLogger _logger;

  GlobalBlocObserver(this._logger);
  
  String _getName(BlocBase bloc) {
    if (bloc is BlocName) {
      return bloc.name;
    }
    return bloc.runtimeType.toString();
  }

  bool _isBlacklisted(String name) {
    return _blocBlacklistPatterns.hasMatch(name);
  }

  @override
  void onEvent(Bloc bloc, Object? event) {
    super.onEvent(bloc, event);
    if (_isBlacklisted(_getName(bloc))) return;
    _logger.info('${_getName(bloc)}-event: ${_formatEvent(event)}');
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    if (_isBlacklisted(_getName(bloc))) return;
    // _logger.info('${_getName(bloc)}-change: ${_formatChange(change)}');
  }

  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    if (_isBlacklisted(_getName(bloc))) return;
    // _logger.info('${_getName(bloc)}-transition: $transition');
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    if (_isBlacklisted(_getName(bloc))) return;
    _logger.error('${_getName(bloc)} error:\n$error\n$stackTrace');
  }

  String _formatEvent(Object? event) {
    if (event == null) return 'null';
    return event.runtimeType.toString().split('(').last.split(')').first;
  }
}
