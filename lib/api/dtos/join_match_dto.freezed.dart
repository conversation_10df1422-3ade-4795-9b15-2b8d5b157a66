// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'join_match_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$JoinMatchDto {
  @JsonKey(name: 'matchId')
  GameMatchId get id;
  @JsonKey(name: 'id')
  PlayerId get playerId;

  /// Create a copy of JoinMatchDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JoinMatchDtoCopyWith<JoinMatchDto> get copyWith =>
      _$JoinMatchDtoCopyWithImpl<JoinMatchDto>(
          this as JoinMatchDto, _$identity);

  /// Serializes this JoinMatchDto to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JoinMatchDto &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, playerId);

  @override
  String toString() {
    return 'JoinMatchDto(id: $id, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class $JoinMatchDtoCopyWith<$Res> {
  factory $JoinMatchDtoCopyWith(
          JoinMatchDto value, $Res Function(JoinMatchDto) _then) =
      _$JoinMatchDtoCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'matchId') GameMatchId id,
      @JsonKey(name: 'id') PlayerId playerId});
}

/// @nodoc
class _$JoinMatchDtoCopyWithImpl<$Res> implements $JoinMatchDtoCopyWith<$Res> {
  _$JoinMatchDtoCopyWithImpl(this._self, this._then);

  final JoinMatchDto _self;
  final $Res Function(JoinMatchDto) _then;

  /// Create a copy of JoinMatchDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? playerId = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameMatchId,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _JoinMatchDto implements JoinMatchDto {
  const _JoinMatchDto(
      {@JsonKey(name: 'matchId') required this.id,
      @JsonKey(name: 'id') required this.playerId});
  factory _JoinMatchDto.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchDtoFromJson(json);

  @override
  @JsonKey(name: 'matchId')
  final GameMatchId id;
  @override
  @JsonKey(name: 'id')
  final PlayerId playerId;

  /// Create a copy of JoinMatchDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JoinMatchDtoCopyWith<_JoinMatchDto> get copyWith =>
      __$JoinMatchDtoCopyWithImpl<_JoinMatchDto>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JoinMatchDtoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JoinMatchDto &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, playerId);

  @override
  String toString() {
    return 'JoinMatchDto(id: $id, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class _$JoinMatchDtoCopyWith<$Res>
    implements $JoinMatchDtoCopyWith<$Res> {
  factory _$JoinMatchDtoCopyWith(
          _JoinMatchDto value, $Res Function(_JoinMatchDto) _then) =
      __$JoinMatchDtoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'matchId') GameMatchId id,
      @JsonKey(name: 'id') PlayerId playerId});
}

/// @nodoc
class __$JoinMatchDtoCopyWithImpl<$Res>
    implements _$JoinMatchDtoCopyWith<$Res> {
  __$JoinMatchDtoCopyWithImpl(this._self, this._then);

  final _JoinMatchDto _self;
  final $Res Function(_JoinMatchDto) _then;

  /// Create a copy of JoinMatchDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? playerId = null,
  }) {
    return _then(_JoinMatchDto(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameMatchId,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
    ));
  }
}

// dart format on
