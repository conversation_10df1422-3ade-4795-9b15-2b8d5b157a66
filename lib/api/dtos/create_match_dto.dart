import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:common/models/game.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

part 'create_match_dto.freezed.dart';
part 'create_match_dto.g.dart';

@freezed
abstract class CreateMatchDto with _$CreateMatchDto {
  const factory CreateMatchDto({
    required GameTypeId gameTypeId,
    required PlayerId creatorId,

    /// Custom name for the game
    String? gameName,
    
    /// Player slots information
    List<PlayerSlot>? playerSlots,
  }) = _CreateMatchDto;

  factory CreateMatchDto.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchDtoFromJson(json);
}