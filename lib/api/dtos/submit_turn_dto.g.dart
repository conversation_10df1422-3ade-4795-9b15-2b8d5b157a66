// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'submit_turn_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SubmitTurnDto _$SubmitTurnDtoFromJson(Map<String, dynamic> json) =>
    _SubmitTurnDto(
      gameMatchId: json['gameMatchId'] as String,
      playerId: json['id'] as String,
      move: (json['move'] as List<dynamic>)
          .map((e) => TargetedAction.fromJson(e as Map<String, dynamic>))
          .toList(),
      turnNumber: (json['turnNumber'] as num).toInt(),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$SubmitTurnDtoToJson(_SubmitTurnDto instance) =>
    <String, dynamic>{
      'gameMatchId': instance.gameMatchId,
      'id': instance.playerId,
      'move': instance.move,
      'turnNumber': instance.turnNumber,
      'metadata': instance.metadata,
    };
