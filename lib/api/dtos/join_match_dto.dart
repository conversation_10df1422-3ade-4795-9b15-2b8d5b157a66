import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:common/models/game.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

part 'join_match_dto.freezed.dart';
part 'join_match_dto.g.dart';

@freezed
abstract class JoinMatchDto with _$JoinMatchDto {
  const factory JoinMatchDto({
    @JsonKey(name: 'matchId') required GameMatchId id,
    @JsonKey(name: 'id') required PlayerId playerId
  }) = _JoinMatchDto;

  factory JoinMatchDto.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchDtoFromJson(json);
}