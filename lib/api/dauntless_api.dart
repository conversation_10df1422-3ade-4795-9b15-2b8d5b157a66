import 'package:common/models/game.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:retrofit/retrofit.dart';
import 'package:common/models/game_match.dart';

import 'dtos/create_match_dto.dart';
import 'dtos/join_match_dto.dart';
import 'dtos/submit_turn_dto.dart';

part 'dauntless_api.g.dart';

/// API client for interacting with the Dauntless server
@RestApi()
abstract class DauntlessApi {
  /// Create an instance of the API client
  factory DauntlessApi(Dio dio, {String baseUrl}) = _DauntlessApi;

  // void updateBaseUrl(String baseUrl) => (dio as Dio).options.baseUrl = baseUrl;
  
  /// Get a list of open matches (default behavior)
  @GET('/matches?showAll=false')
  Future<List<GameMatch>> getOpenMatches();

  /// Get a specific game_match by ID
  @GET('/matches/{id}')
  Future<GameMatch> getMatch(@Path('id') GameMatchId id);

  /// Create a new game_match
  @POST('/matches')
  Future<GameMatch> createMatch(@Body() CreateMatchDto matchData);

  /// Join an existing game_match
  @POST('/matches/{id}/join')
  Future<GameMatch> joinMatch(
    @Path('id') String id,
    @Body() JoinMatchDto joinMatchDto,
  );
  
  /// Leave an existing game_match
  @POST('/matches/{id}/leave')
  Future<GameMatch> leaveMatch(
    @Path('id') String id,
    @Body() JoinMatchDto leaveMatchDto,
  );

  /// Submit a turn for a game_match
  @POST('/matches/{id}/turns')
  Future<void> submitTurn(
    @Path('id') String id,
    @Body() SubmitTurnDto turnData,
  );

  /// Update game_match status (close/reopen)
  @PUT('/matches/{id}/status')
  Future<GameMatch> updateMatchStatus(
    @Path('id') String id,
    @Body() Map<String, dynamic> statusData,
  );
  
  /// Open a match for joining by other players
  @PUT('/matches/{id}/open')
  Future<GameMatch> openMatchForJoining(@Path('id') String id);
  
  /// Delete a match
  @DELETE('/matches/{id}')
  Future<void> deleteMatch(@Path('id') String id);
}

// extension UpdateBaseUrl on DauntlessApi {
//   static void updateBaseUrl(String baseUrl) {
//     if (GetIt.I.isRegistered<DauntlessApi>()) {
//       final api = GetIt.I.get<DauntlessApi>();
//       GetIt.I.registerSingleton(DauntlessApi(api.dio, baseUrl: baseUrl));
//     }
//   }
// }