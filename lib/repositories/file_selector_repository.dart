import 'package:file_selector/file_selector.dart';

class FileSelectorRepository {
  Future<XFile?> get({
    String? label,
    List<String>? extensions,
    List<String>? mimeTypes,
    List<String>? uniformTypeIdentifiers,
    List<String>? webWildCards,
  }) async {
    final typeGroup = XTypeGroup(
      label: label,
      extensions: extensions,
      mimeTypes: mimeTypes,
      uniformTypeIdentifiers: uniformTypeIdentifiers,
      webWildCards: webWildCards,
    );
    final file = await openFile(acceptedTypeGroups: <XTypeGroup>[typeGroup]);
    return file;
  }
}
