import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/repositories/game_card_repository.dart';

class ActionsRepository extends BaseListRepository<CardAction> {

  @override
  Future<List<CardAction>> get() => _getActionsFromAsset('/Users/<USER>/dev/dauntless/games/liberator/data/dauntless_actions.json');

  @override
  Future<List<CardGrouping>> getGroupings() => JsonReadWriteDataService.parseCardGroupingListFromJsonFile('/Users/<USER>/dev/dauntless/games/liberator/data/dauntless_action_groupings_swap.json');
  // Future<List<CardGrouping>> getGroupings() => parseCardGroupingListFromJsonAsset('/Users/<USER>/dev/dauntless/games/liberator/data/dauntless_action_groupings.json');

  Future<List<CardAction>> _getActionsFromAsset(String filePath) =>
      JsonReadWriteDataService.parseCardActionListFromJsonAsset(filePath);
}