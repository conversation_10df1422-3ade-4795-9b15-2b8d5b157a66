import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/repositories/game_card_repository.dart';
import 'package:dauntless/data_services/json_read_write_data_service.dart';

/// 2/11/25 ... I think it may make more sense to centralize importing our GameCardClasses here, instead of diff repos for vehicles, locations, etc (though locations have special items ... TBD). Trying using this for first 'buildings' and we'll stick what else we can in here while it makes sense!

// TODO: use base_path for these references
class GenericCardClassRepository extends GameCardClassRepository {
  @override
  Future<List<GameCardClass>> get() => JsonReadWriteDataService.parseGameCardGenericListFromJsonFile('/Users/<USER>/dev/dauntless/games/liberator/data/classes.json');

  @override
  Future<List<CardGrouping>> getGroupings() async => [];
}