import 'dart:async';

import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:flutter/material.dart';

class AppLifeCycleRepository with WidgetsBindingObserver {
  final RemoteLogger _logger;

  AppLifeCycleRepository(this._logger);

  final StreamController<AppLifecycleState> _appLifeCycleStream = StreamController<AppLifecycleState>.broadcast();

  Stream<AppLifecycleState> get appLifeCycleStream => _appLifeCycleStream.stream;

  AppLifecycleState? _appLifecycleState;
  AppLifecycleState? get appLifecycleState => _appLifecycleState;

  bool get isForegrounded => _appLifecycleState == AppLifecycleState.resumed;
  
  /// Special method for Chrome extensions to manually set the lifecycle state
  /// This bypasses the normal Platform-dependent initialization
  void manuallySetState(AppLifecycleState state) {
    _logger.info('Manually setting AppLifecycleState to: $state');
    _appLifecycleState = state;
    _appLifeCycleStream.add(state);
  }

  Future<void> init() async {
    WidgetsBinding.instance.addObserver(this);
    _appLifecycleState = WidgetsBinding.instance.lifecycleState;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _logger.info('didChangeAppLifecycleState: $state');
    _appLifecycleState = state;
    _appLifeCycleStream.add(state);
  }
}