import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';

abstract class GameCardRepository extends BaseListRepository<GameCard> {
  // Future<List<GameCard<T>>> get();
  // Future<List<CardGrouping>> getGroupings();
}

abstract class GameCardClassRepository extends BaseListRepository<GameCardClass> {
  // Future<List<GameCard<T>>> get();
  // Future<List<CardGrouping>> getGroupings();
}

abstract class BaseListRepository<T> {
  Future<List<T>> get();
  Future<List<CardGrouping>> getGroupings();
}
