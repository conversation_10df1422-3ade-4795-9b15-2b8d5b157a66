import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/repositories/game_card_repository.dart';
import 'package:dauntless/data_services/json_read_write_data_service.dart';

class VehiclesRepository extends GameCardClassRepository {
  @override
  Future<List<GameCardClass>> get() async {
    final rebelShips = _getVehiclesFromJsonFile('/Users/<USER>/dev/dauntless/games/liberator/data/rebellion_alliance_ships.json');
    final imperialShips = _getVehiclesFromJsonFile('/Users/<USER>/dev/dauntless/games/liberator/data/rebellion_imperial_ships.json');
    return Future.wait([rebelShips, imperialShips]).then((value) => value.expand((element) => element).toList());
  }

  Future<List<GameCardClass>> _getVehiclesFromJsonFile(String filePath) =>
      JsonReadWriteDataService.parseGameCardClassListFromJsonFile(filePath, CardType.vehicle);

  @override
  Future<List<CardGrouping>> getGroupings() async => [];
}
