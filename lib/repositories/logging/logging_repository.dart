import 'dart:io';

class LoggingRepository {
  Future<Result<HttpResponse, ApiError>> logAlert(
      String orgId, LogAlertBody body) async {
    return Result<HttpResponse, ApiError>();
  }

  Future<Result<LogEventResponse, ApiError>> putLogEvents(
      String orgId, LogEventBody body) async {
    return Result<LogEventResponse, ApiError>();
  }
}

class ApiError {
}

class LogAlertBody {
}

class Result<Response, Error>{}

class LogEventResponse {}

class LogEventBody {}