import 'dart:io';

import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/models/base/saved_game_state.dart';

class SaveStateRepository {
  Future<void> saveSavedGameState(String savePath, SavedGameState saveState) async => await JsonReadWriteDataService.writeJsonToFile(
        savePath, saveState.toJson());

  Future<List<FileSystemEntity>> getGameSaves(String saveDirectory) async {
    final dir = Directory(saveDirectory);
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
    return await Directory(saveDirectory).list().toList();
  }

  Future<SavedGameState?> loadSavedGameState(String path) => JsonReadWriteDataService.parseSavedGameStateFromJsonFile(path);
}
