// import 'package:dauntless/dev/network_dev.dart';
// import 'package:dio/dio.dart';
// import 'package:injectable/injectable.dart';
// import 'package:pretty_dio_logger/pretty_dio_logger.dart';
// import 'package:dauntless/api/dauntless_api.dart';
//
// /// Repository for handling network operations with the server
// @singleton
// class NetworkRepository {
//   /// Constructor
//   NetworkRepository();
//
//   late final DauntlessApi _api;
//
//   /// Initialize the network repository
//   @factoryMethod
//   @preResolve
//   static Future<NetworkRepository> create() async {
//     final repository = NetworkRepository();
//     await repository._initialize();
//     return repository;
//   }
//
//   /// Initialize the API client
//   Future<void> _initialize() async {
//     // Create a Dio instance with appropriate configuration
//     final dio = Dio(BaseOptions(
//       baseUrl: NetworkDev.wsUrl,
//       connectTimeout: const Duration(seconds: 5),
//       receiveTimeout: const Duration(seconds: 10),
//       headers: {
//         'Content-Type': 'application/json',
//         'Accept': 'application/json',
//       },
//     ));
//
//     // Add logging interceptor
//     dio.interceptors.add(PrettyDioLogger(
//       requestHeader: true,
//       requestBody: true,
//       responseBody: true,
//       responseHeader: false,
//       compact: false,
//     ));
//
//     // Create the API client
//     _api = DauntlessApi(dio);
//   }
//
//   /// Get the API client
//   DauntlessApi get api => _api;
// }
