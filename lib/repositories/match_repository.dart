import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

class MatchRepository {
  final Map<PlayerId, List<GameCard>> _submittedActiveTurnsPostState = {};

  Map<PlayerId, List<GameCard>> get submittedActiveTurnsPostState => _submittedActiveTurnsPostState;

  void submitPlayerTurnPostState(PlayerId playerId, List<GameCard> playerHand) {
    _submittedActiveTurnsPostState[playerId] = playerHand;
  }

  final Map<PlayerId, List<TargetedAction>> _submittedActiveActions = {};

  Map<PlayerId, List<TargetedAction>> get submittedActiveActions => _submittedActiveActions;

  void submitPlayerTurnActions(PlayerId playerId, List<TargetedAction> playerActions) {
    if (_submittedActiveActions.containsKey(playerId)) {
      throw Exception('Player $playerId has already submitted actions for this turn');
    }
    _submittedActiveActions[playerId] = playerActions;
  }
  
  /// Special method for development purposes to submit turn actions for other players
  /// This allows overwriting existing actions for the specified player
  void submitDevOtherPlayerTurnActions(PlayerId playerId, List<TargetedAction> playerActions) {
    // For development, we allow overwriting existing actions
    _submittedActiveActions[playerId] = playerActions;
  }

  void clearSubmittedActiveActions() => _submittedActiveActions.clear();
}