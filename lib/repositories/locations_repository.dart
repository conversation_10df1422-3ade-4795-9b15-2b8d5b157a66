import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/dto/grid_labels_dto.dart';
import 'package:dauntless/repositories/game_card_repository.dart';
import 'package:dauntless/data_services/json_read_write_data_service.dart';

class LocationsRepository extends GameCardClassRepository {
  @override
  Future<List<GameCardClass>> get() => _getLocationsFromAsset('/Users/<USER>/dev/dauntless/games/liberator/data/systems_imgs_locations.json', end: 200);

  @override
  Future<List<CardGrouping>> getGroupings() => JsonReadWriteDataService.parseCardGroupingListFromJsonFile('/Users/<USER>/dev/dauntless/games/liberator/data/systems_imgs_locations.json', end: 200);
  // Future<List<CardGrouping>> getGroupings() => parseCardGroupingListFromJsonFile('/Users/<USER>/dev/dauntless/games/liberator/data/rebellion_planets.json');

  Future<List<GameCardClass>> _getLocationsFromAsset(String filePath, {
    int start = 0,
    int end = -1,
  }) =>
      JsonReadWriteDataService.parseGameCardClassListFromJsonFile(filePath, CardType.location, start: start, end: end);

  Future<(List<GridLabelComponent>, List<GridLabelComponent>)> getGridLabels() async {
    final gridLabelsDto = await JsonReadWriteDataService.parseGridLabelsFromJsonFile('/Users/<USER>/dev/dauntless/games/liberator/data/grid_labels.json');
    return gridLabelsDto.toGridLabelComponents();
  }
}