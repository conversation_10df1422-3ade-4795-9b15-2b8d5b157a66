// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:dauntless/dev/emulatedDB.dart';
// import 'package:dauntless/firebase_options.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:flutter/foundation.dart';
//
// class FirebaseFirestoreRepository {
//   Future<void> init() async {
//     await Firebase.initializeApp(
//       options: DefaultFirebaseOptions.currentPlatform,
//     );
//
//     if (kDebugMode) {
//       try {
//         FirebaseFirestore.instance.useFirestoreEmulator('localhost', 8080);
//         await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
//         await setupEmulatedFirestoreForWar(FirebaseFirestore.instance);
//       } catch (e) {
//         // ignore: avoid_print
//         print(e);
//       }
//     }
//   }
// }