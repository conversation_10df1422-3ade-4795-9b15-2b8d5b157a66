import 'dart:io';

import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/dev/dev_initial_state.dart';
import 'package:dauntless/models/base/game_config.dart';

class GameConfigRepository {
  Future<GameConfig> loadGameConfig(String path) async {
    final loadedConfig = await JsonReadWriteDataService.parseGameConfigFromJsonFile(path);
    return loadedConfig;
  }

  Future<List<GameConfig>> loadAvailableGameConfigs() async {
    final List<GameConfig> configs = [];
    final Directory gamesDirectory = Directory("$projectBasePath/games");
    
    if (await gamesDirectory.exists()) {
      final List<FileSystemEntity> entities = await gamesDirectory.list().toList();
      final List<Directory> gameDirs = entities
          .whereType<Directory>()
          .toList();
          
      for (final Directory gameDir in gameDirs) {
        final String configPath = '${gameDir.path}/data/config.json';
        final configFile = File(configPath);
        
        if (await configFile.exists()) {
          try {
            final GameConfig config = await loadGameConfig(configPath);
            configs.add(config);
          } catch (e) {
            print('Error loading config from ${configPath}: $e');
            // Skip this config and continue with others
          }
        }
      }
    }
    
    return configs;
  }
}