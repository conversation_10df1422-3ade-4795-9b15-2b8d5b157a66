import 'dart:async';

import 'package:dauntless/api/dauntless_api.dart';
import 'package:dauntless/api/dtos/create_match_dto.dart';
import 'package:dauntless/api/dtos/join_match_dto.dart';
import 'package:dauntless/api/dtos/submit_turn_dto.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:common/models/game.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';

/// Repository for communicating with the game server
class ServerRepository {
  final RemoteLogger _logger;
  final DauntlessApi _api;

  final StreamController<Map<String, dynamic>> _messageStreamController =
      StreamController.broadcast();

  ServerRepository(this._logger, this._api);

  Stream<Map<String, dynamic>> get serverMessages =>
      _messageStreamController.stream;

  /// Submit player turn actions to the server
  /// Returns true if submission was successful, false otherwise
  Future<bool> submitTurnActions(SubmitTurnDto dto) async {
    try {
      final matchIdToUse = dto.gameMatchId;
      final playerId = dto.playerId;
      _logger.info(
          'Submitting turn actions for player $playerId in game_match $matchIdToUse; turn number: ${dto.turnNumber}');

      try {
        final response = await _api.submitTurn(matchIdToUse, dto);

        // Process the response
        // _messageStreamController.add({
        //   'type': 'match_update',
        //   'game_match': response.toJson(),
        //   'matchId': matchIdToUse,
        // });
        _logger.info('Turn submitted successfully via HTTP');
        return true;
      } catch (e) {
        _logger.error('Failed to submit turn: $e');
        return false;
      }
    } catch (e) {
      _logger.error('Failed to submit turn to server: $e');
      return false;
    }
  }

  /// Get the current game state from the server
  Future<Map<String, dynamic>?> fetchGameState(String matchId) async {
    try {
      // Use the API client
      final match = await _api.getMatch(matchId);

      // Convert to JSON for the caller
      return match.toJson();
    } catch (e) {
      _logger.error('Failed to fetch game state: $e');
      return null;
    }
  }

  /// Create a new match on the server
  Future<GameMatch?> createMatch(CreateMatchDto dto) async {
    try {
      _logger.info('Creating match with gameType ${dto.gameTypeId}');
      print(
          'SERVER_REPOSITORY: Creating match with gameType ${dto.gameTypeId}');
      print('SERVER_REPOSITORY DEBUG: Using API instance to create match');

      try {
        // Use the API client
        print('SERVER_REPOSITORY DEBUG: About to call _api.createMatch()');
        final createdMatch = await _api.createMatch(dto);
        print('SERVER_REPOSITORY DEBUG: API call completed');

        print(
            'SERVER_REPOSITORY: Successfully created match: ${createdMatch.id}');
        _logger.info('Match created with ID: ${createdMatch.id}');
        return createdMatch;
      } catch (apiError) {
        _logger.error('Failed to create match: $apiError');
        print('SERVER_REPOSITORY: Failed to create match: $apiError');
        print(
            'SERVER_REPOSITORY DEBUG: API error details: ${apiError.toString()}');
        return null;
      }
    } catch (e) {
      _logger.error('Failed to create match: $e');
      print('SERVER_REPOSITORY: Failed to create match: $e');
      print('SERVER_REPOSITORY DEBUG: Error stack trace: ${e.toString()}');
      return null;
    }
  }

  /// Fetch open matches from the server
  /// Returns a list of available matches - only used for initial load // TODO: add 'gameName' support
  Future<List<GameMatch>> fetchOpenMatches([String? gameName]) async {
    try {
      print('SERVER_REPOSITORY: Fetching initial open matches...');

      try {
        // Now we can use the API client directly since our model has proper JsonKey annotations
        final matches = await _api.getOpenMatches();

        print(
            'SERVER_REPOSITORY: Successfully fetched ${matches.length} open matches');
        _logger.info('Fetched ${matches.length} open matches');
        return matches;
      } catch (apiError) {
        print('SERVER_REPOSITORY: Error fetching open matches: $apiError');
        _logger.error('Failed to fetch open matches: $apiError');
        return [];
      }
    } catch (e) {
      print('SERVER_REPOSITORY: Failed to fetch open matches: $e');
      _logger.error('Failed to fetch open matches: $e');
      return [];
    }
  }

  /// Join an existing match
  Future<bool> joinMatch(String matchId, String playerId) async {
    try {
      print('SERVER_REPOSITORY: Player $playerId joining match: $matchId');
      final dto = JoinMatchDto(id: matchId, playerId: playerId);

      try {
        await _api.joinMatch(matchId, dto);
        print('SERVER_REPOSITORY: Player $playerId successfully joined match: $matchId');
        _logger.info('Player $playerId joined match: $matchId');
        return true;
      } catch (apiError) {
        print('SERVER_REPOSITORY: Failed to join match: $apiError');
        _logger.error('Failed to join match: $apiError');
        return false;
      }
    } catch (e) {
      print('SERVER_REPOSITORY: Error joining match: $e');
      _logger.error('Error joining match: $e');
      return false;
    }
  }

  /// Leave an existing match
  Future<bool> leaveMatch(String matchId, String playerId) async {
    try {
      print('SERVER_REPOSITORY: Player $playerId leaving match: $matchId');
      final dto = JoinMatchDto(id: matchId, playerId: playerId);

      try {
        await _api.leaveMatch(matchId, dto);
        print('SERVER_REPOSITORY: Player $playerId successfully left match: $matchId');
        _logger.info('Player $playerId left match: $matchId');
        return true;
      } catch (apiError) {
        print('SERVER_REPOSITORY: Failed to leave match: $apiError');
        _logger.error('Failed to leave match: $apiError');
        return false;
      }
    } catch (e) {
      print('SERVER_REPOSITORY: Error leaving match: $e');
      _logger.error('Error leaving match: $e');
      return false;
    }
  }

  /// Delete an existing match
  /// Returns true if the match was successfully deleted, false otherwise
  Future<bool> deleteMatch(GameMatchId matchId) async {
    try {
      _logger.info('Deleting match: $matchId');
      print('SERVER_REPOSITORY: Deleting match: $matchId');

      try {
        // Use the API client
        await _api.deleteMatch(matchId);

        print('SERVER_REPOSITORY: Successfully deleted match: $matchId');
        _logger.info('Match successfully deleted: $matchId');
        return true;
      } catch (apiError) {
        _logger.error('Failed to delete match: $apiError');
        print('SERVER_REPOSITORY: Failed to delete match: $apiError');
        return false;
      }
    } catch (e) {
      _logger.error('Failed to delete match: $e');
      print('SERVER_REPOSITORY: Failed to delete match: $e');
      return false;
    }
  }

  Future<void> testConnection() async {
    try {
      final response = await _api.getOpenMatches();
      print('SERVER_REPOSITORY: Test connection response: $response');
      _logger.info('Test connection response: $response');
    } catch (e) {
      print('SERVER_REPOSITORY: Error testing connection: $e');
      _logger.error('Error testing connection: $e');
      rethrow;
    }
  }
  
  /// Update player slots in a match
  /// Returns true if the slots were successfully updated, false otherwise
  Future<bool> updateMatchPlayerSlots(
    String matchId,
    List<PlayerSlot> playerSlots,
    String updatedByPlayerId,
  ) async {
    try {
      _logger.info('Updating player slots for match: $matchId by player $updatedByPlayerId');
      print('SERVER_REPOSITORY: Updating player slots for match: $matchId');
      
      try {
        // Convert the player slots to a format the API can use
        final Map<String, dynamic> statusData = {
          'playerSlots': playerSlots.map((slot) => slot.toJson()).toList(),
          'updatedBy': updatedByPlayerId,
        };
        
        // Use the existing updateMatchStatus API endpoint to update the match
        await _api.updateMatchStatus(matchId, statusData);
        
        print('SERVER_REPOSITORY: Successfully updated player slots for match: $matchId');
        _logger.info('Player slots updated successfully for match: $matchId');
        return true;
      } catch (apiError) {
        print('SERVER_REPOSITORY: Failed to update player slots: $apiError');
        _logger.error('Failed to update player slots: $apiError');
        return false;
      }
    } catch (e) {
      print('SERVER_REPOSITORY: Error updating player slots: $e');
      _logger.error('Error updating player slots: $e');
      return false;
    }
  }
}
