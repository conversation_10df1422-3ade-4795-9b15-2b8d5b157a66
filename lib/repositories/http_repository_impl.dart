// import 'package:common/models/game_match.dart';
// import 'package:injectable/injectable.dart';
//
// import '../api/dauntless_api.dart';
// import 'websocket_repository.dart';
//
// /// Implementation of HttpRepository that provides fallback data fetching
// /// when WebSocket connections are interrupted
// @injectable
// class HttpRepositoryImpl implements HttpRepository {
//   /// API client for server communication
//   final DauntlessApi _api;
//
//   /// Game match factory methods for creating match objects
//   final Map<String, GameMatch Function(Map<String, dynamic>)> _matchFactories;
//
//   /// Constructor
//   HttpRepositoryImpl({
//     required DauntlessApi api,
//     required Map<String, GameMatch Function(Map<String, dynamic>)> matchFactories,
//   }) :
//     _api = api,
//     _matchFactories = matchFactories;
//
//   /// Update match factories after initialization
//   void updateMatchFactories(Map<String, GameMatch Function(Map<String, dynamic>)> factories) {
//     _matchFactories.clear();
//     _matchFactories.addAll(factories);
//   }
//
//   @override
//   Future<List<GameMatch>> fetchOpenMatches() async {
//     try {
//       // Use the Retrofit-generated API client to fetch open matches
//       final matches = await _api.getOpenMatches();
//       return matches;
//     } catch (e) {
//       return [];
//     }
//   }
//
//   @override
//   Future<GameMatch?> fetchMatchDetails(String matchId) async {
//     try {
//       // Use the Retrofit-generated API client to fetch match details
//       final match = await _api.getMatch(matchId);
//       return match;
//     } catch (e) {
//       return null;
//     }
//   }
// }
