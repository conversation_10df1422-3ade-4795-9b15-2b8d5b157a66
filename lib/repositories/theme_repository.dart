import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:json_theme/json_theme.dart';

const themeJsonPath = 'assets/themes/teal_theme.json';

class ThemeRepository {
  Future<ThemeData?> get() async {
    final jsonString = await rootBundle.loadString(themeJsonPath);
    final (jsonMap, jsonList) = JsonReadWriteDataService.safeJsonDecode(jsonString);
    return ThemeDecoder.decodeThemeData(jsonMap);
  }
}