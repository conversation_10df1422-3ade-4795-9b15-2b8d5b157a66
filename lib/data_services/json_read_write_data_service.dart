import 'dart:convert';
import 'dart:io';

import 'package:dauntless/frameworks/environment/server_environment/server_environment_config.dart';
import 'package:dauntless/frameworks/user/user_config.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/dto/grid_labels_dto.dart';
import 'package:dauntless/models/base/saved_game_state.dart';
import 'package:file_selector/file_selector.dart';
import 'package:flutter/services.dart';

/// perhaps this is a 'provider'?
class JsonReadWriteDataService {
  static (Map<String, dynamic>?, List?) safeJsonDecode(dynamic inputJson) {
    if (inputJson is Map<String, dynamic>) {
      return (inputJson, null);
    } else if (inputJson is List) {
      return (null, inputJson);
    } else if (inputJson is String) {
      try {
        final decodedJson = jsonDecode(inputJson);
        if (decodedJson is Map<String, dynamic>) {
          return (decodedJson, null);
        } else if (decodedJson is List) {
          return (null, decodedJson);
        }
      } catch (e) {
        print('Error decoding JSON: $e');
        rethrow;
        // Ignore the error and return the original input as data
      }
    }
    return ({'data': inputJson}, null);
  }

// TODO: handle JSON Maps

  static Future<ServerEnvironmentConfig> parseServerConfigsFromJsonFile(String filePath) async {
    String data = await _loadStringFromFile(filePath);
    return _parseObjectFromString(data, ServerEnvironmentConfig.fromJson);
  }

  static Future<List<GameCard>> parseGameCardListFromJsonAsset(String assetPath,
      CardType type, {
        int start = 0,
        int end = -1,
      }) =>
      _parseListFromJsonAsset<GameCard>(
          assetPath, (entry) => _gameCardFromJson(entry, typeFallback: type),
          start: start, end: end);

  static Future<List<GameCard>> parseGameCardListFromJsonFile(String filePath,
      CardType type, {
        int start = 0,
        int end = -1,
      }) =>
      _parseListFromJsonFile(
          filePath, (entry) => _gameCardFromJson(entry, typeFallback: type),
          start: start, end: end);

  static Future<List<GameCardClass>> parseGameCardClassListFromJsonFile(
      String filePath, CardType type, {
        int start = 0,
        int end = -1,
      }) => _parseListFromJsonFile(filePath, (entry) =>
      _gameCardClassFromJson(entry, typeFallback: type), start: start,
      end: end);

  static Future<List<GameCardClass>> parseGameCardGenericListFromJsonFile(
      String filePath, {
        int start = 0,
        int end = -1,
      }) => _parseListFromJsonFile(
      filePath, (entry) => _gameCardClassFromJson(entry), start: start,
      end: end);

  static Future<List<CardGrouping>> parseCardGroupingListFromJsonAsset(
      String filePath) =>
      _parseListFromJsonAsset(filePath, CardGrouping.fromJson);

  static Future<List<CardGrouping>> parseCardGroupingListFromJsonFile(String filePath,
      {
        int start = 0,
        int end = -1,
      }) =>
      _parseListFromJsonFile(
          filePath, CardGrouping.fromJson, start: start, end: end);

  static Future<List<CardAction>> parseCardActionListFromJsonAsset(String filePath, {
    int start = 0,
    int end = -1,
  }) =>
      _parseListFromJsonAsset(
          filePath, CardAction.fromJson, start: start, end: end);

  static Future<List<CardAction>> parseCardActionListFromJsonFile(String filePath) =>
      _parseListFromJsonFile(filePath, CardAction.fromJson);

  static Future<GameConfig> parseGameConfigFromJsonFile(String filePath) =>
      _parseObjectFromJsonFile<GameConfig>(filePath, GameConfig.fromJson);

  static Future<GridLabelsDto> parseGridLabelsFromJsonFile(String filePath) =>
      _parseObjectFromJsonFile<GridLabelsDto>(filePath, GridLabelsDto.fromJson);

  static GameCard _gameCardFromJson(Map<String, dynamic> entryJson,
      {CardType? typeFallback, CardType? typeOverride}) {
    entryJson['type'] =
        typeOverride?.name ?? entryJson['type'] ?? typeFallback?.name;
    // entryJson.addAll({'type': typeOverride ?? typeFallback});
    final type = CardType.fromJson(entryJson['type']);
    if (type.isUnique) {
      entryJson.addAll({'classId': entryJson['id'] ?? entryJson['classId']});
    }
    final listObject = GameCard.fromJson(entryJson);
    return listObject;
  }

  static GameCardClass _gameCardClassFromJson(Map<String, dynamic> entryJson,
      {CardType? typeFallback, CardType? typeOverride}) {
    entryJson['type'] =
        typeOverride?.name ?? entryJson['type'] ?? typeFallback?.name;
    // entryJson.addAll({'type': typeOverride ?? typeFallback});
    // final type = CardType.fromJson(entryJson['type']);
    // if (type.isUnique) {
    //   entryJson.addAll({'id': entryJson['id'] ?? entryJson['classId']});
    // }
    final listObject = GameCardClass.fromJson(entryJson);
    return listObject;
  }

  static Future<List<T>> _parseListFromJsonAsset<T>(String filePath,
      T Function(Map<String, dynamic>) fromJson, {
        int start = 0,
        int end = -1,
      }) async {
    String data = await _loadStringFromAsset(filePath);
    return _parseListFromString(data, fromJson, start: start, end: end);
  }

  static Future<List<T>> _parseListFromJsonFile<T>(String filePath,
      T Function(Map<String, dynamic>) fromJson, {
        int start = 0,
        int end = -1,
      }) async {
    String data = await _loadStringFromFile(filePath);
    return _parseListFromString(data, fromJson, start: start, end: end);
  }

  static Future<T> _parseObjectFromJsonFile<T>(String filePath,
      T Function(Map<String, dynamic>) fromJson) async {
    String data = await _loadStringFromFile(filePath);
    return _parseObjectFromString(data, fromJson);
  }

  static Future<List<T>> _parseListFromString<T>(String data,
      T Function(Map<String, dynamic>) fromJson, {
        int start = 0,
        int end = -1,
      }) async {
    final (jsonMap, jsonList) = safeJsonDecode(data); //latest Dart

    List<T> list = [];
    if (jsonList is List) {
      // for (var element in jsonList) {
      for (var element in jsonList.sublist(
          start, end == -1 ? jsonList.length : end)) {
        final listObject = fromJson(element);
        list.add(listObject);
      }
    }
    return list;
  }

  static T _parseObjectFromString<T>(String data,
      T Function(Map<String, dynamic>) fromJson) {
    final (jsonMap, _) = safeJsonDecode(data);
    if (jsonMap is Map<String, dynamic>) {
      return fromJson(jsonMap);
    }
    throw Exception('Invalid JSON data');
  }

// TODO: handle error
  static Future<String> _loadStringFromAsset(String filePath) async {
    return rootBundle.loadString(filePath);
  }

// TODO: handle error
  static Future<String> _loadStringFromFile(String filePath) async {
    XFile file = XFile(filePath);
    return file.readAsString();
  }

  static Future<File> _saveStringToFile(String filePath, String data) async {
    File file = File(filePath);
    return file.writeAsString(data);
  }

  static Future<File> writeJsonToFile(String savePath, Map<String, dynamic> json) async {
    final jsonString = jsonEncode(json);
    return _saveStringToFile(savePath, jsonString);
  }

  static Future<SavedGameState?> parseSavedGameStateFromJsonFile(String path) async {
    final jsonString = await _loadStringFromFile(path);
    return _parseObjectFromString(jsonString, SavedGameState.fromJson);
  }

  static Future<UserConfig> parseUserConfigFromJsonFile(String filePath) async {
    String data = await _loadStringFromFile(filePath);
    return _parseObjectFromString(data, UserConfig.fromJson);
  }
}