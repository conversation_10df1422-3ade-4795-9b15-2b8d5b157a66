import 'dart:math';

import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/ui/blocs/theme/theme_bloc.dart';
import 'package:dauntless/ui/blocs/theme/theme_sizings.dart';
import 'package:dauntless/ui/blocs/theme/theme_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

extension ThemeGetter on BuildContext {
  ThemeData get theme => Theme.of(this);
  ThemeState get themeState => read<ThemeBloc>().state;
  ThemeSizings get sizings => themeState.sizings;
}

mixin BlocName<T> on BlocBase<T> {
  String get name;// => runtimeType.toString();
}

// extension UniqueChildren on List<GameCard> {
//   /// all groups, and all children not in a group
//   List<GameCard> get uniqueChildren {
//     final groups = where((card) => card.type == CardType.grouping)
//         .toList();
//     final nonGroups = where((card) => card.type != CardType.grouping)
//         .toList();
//     List<GameCard> result = List.from(groups);
//     for (final nonGroup in nonGroups) {
//       if (groups.where((group) => group.id == nonGroup.locationId).isEmpty) {
//         result.add(nonGroup);
//       }
//     }
//     return result;
//   }
// }

extension UniqueChildren on List<(GameCard, GameCardClass?)> {
  /// all groups, and all children not in a group
  List<(GameCard, GameCardClass?)> get uniqueChildren {
    final groups = where((cardAndClass) => cardAndClass.$1.type == CardType.grouping)
        .toList();
    final nonGroups = where((cardAndClass) => cardAndClass.$1.type != CardType.grouping)
        .toList();
    List<(GameCard, GameCardClass?)> result = List.from(groups);
    for (final nonGroup in nonGroups) {
      if (groups.where((group) => group.$1.id == nonGroup.$1.locationId).isEmpty) {
        result.add(nonGroup);
      }
    }
    return result;
  }
}

extension ReconciledAttributes on List<(GameCard, GameCardClass?)> {
  Map<String, dynamic> getReconciledAttributes() {
    Map<String, dynamic> reconciledAttributes = {};

    for (final cardAndClass in this) {
      final groupedCardClass = cardAndClass.$2;
      if (groupedCardClass == null) continue;

      for (final attribute in groupedCardClass.attributes.keys) {
        final value = groupedCardClass.attributes[attribute];

        if (reconciledAttributes[attribute] == null) {
          // Handle initial assignment with deep copy for collections
          if (value is Map) {
            reconciledAttributes[attribute] = Map<String, dynamic>.from(value);
          } else if (value is List) {
            reconciledAttributes[attribute] = List.from(value);
          } else {
            reconciledAttributes[attribute] = value;
          }
        } else {
          // Handle merging based on type
          if (value is Map && reconciledAttributes[attribute] is Map) {
            reconciledAttributes[attribute] = _mergeDeepMaps(
              reconciledAttributes[attribute] as Map<String, dynamic>,
              value as Map<String, dynamic>,
            );
          } else if (value is List && reconciledAttributes[attribute] is List) {
            // Merge lists while removing duplicates
            final existingList = reconciledAttributes[attribute] as List;
            final newItems = value.where((item) => !existingList.contains(item));
            existingList.addAll(newItems);
          } else if (value is num && reconciledAttributes[attribute] is num) {
            reconciledAttributes[attribute] = min(
              value,
              reconciledAttributes[attribute] as num,
            );
          } else if (value != null) {
            // Handle primitive values
            reconciledAttributes[attribute] = value;
          }
        }
      }
    }
    return reconciledAttributes;
  }

  Map<String, dynamic> _mergeDeepMaps(
      Map<String, dynamic> map1,
      Map<String, dynamic> map2,
      ) {
    Map<String, dynamic> result = Map<String, dynamic>.from(map1);

    map2.forEach((key, value) {
      if (!result.containsKey(key)) {
        // Handle new key
        if (value is Map) {
          result[key] = Map<String, dynamic>.from(value);
        } else if (value is List) {
          result[key] = List.from(value);
        } else {
          result[key] = value;
        }
      } else {
        // Handle existing key
        if (value is Map && result[key] is Map) {
          // Recursively merge nested maps
          result[key] = _mergeDeepMaps(
            result[key] as Map<String, dynamic>,
            value as Map<String, dynamic>,
          );
        } else if (value is List && result[key] is List) {
          // Merge lists while removing duplicates
          final existingList = result[key] as List;
          final newItems = value.where((item) => !existingList.contains(item));
          existingList.addAll(newItems);
        } else if (value is num && result[key] is num) {
          // Take minimum of numbers
          result[key] = min(value, result[key] as num);
        } else if (value != null) {
          // Override with new value for other types
          result[key] = value;
        }
      }
    });

    return result;
  }}