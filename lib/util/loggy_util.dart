import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loggy/loggy.dart';

extension BlocLoggy on Bloc {
  Loggy<ViewModelLoggy> get loggy => Loggy<ViewModelLoggy>('blocs-$runtimeType');
}

extension StatlessWidgetLoggy on StatelessWidget {
  Loggy<UiLoggy> get loggy => Loggy<UiLoggy>('stateless-$runtimeType');
}

extension StatefulWidgetLoggy on StatefulWidget {
  Loggy<UiLoggy> get loggy => Loggy<UiLoggy>('stateful-$runtimeType');
}

extension StateWidgetLoggy on State {
  Loggy<UiLoggy> get loggy => Loggy<UiLoggy>('state-$runtimeType');
}

// mixin GameLoggy implements LoggyType {
//   @override
//   Loggy<GameLoggy> get loggy => Loggy<GameLoggy>('game-$runtimeType');
// }
//
// mixin GameTypeLoggy implements LoggyType {
//   @override
//   Loggy<GameTypeLoggy> get loggy =>
//       Loggy<GameTypeLoggy>('gameType-$runtimeType');
// }

mixin ViewModelLoggy implements LoggyType {
  @override
  Loggy<ViewModelLoggy> get loggy => Loggy<ViewModelLoggy>('vm-$runtimeType');
}

mixin ServiceLoggy implements LoggyType {
  @override
  Loggy<ServiceLoggy> get loggy => Loggy<ServiceLoggy>('svc-$runtimeType');
}

mixin ModelLoggy implements LoggyType {
  @override
  Loggy<ModelLoggy> get loggy => Loggy<ModelLoggy>('model-$runtimeType');
}
