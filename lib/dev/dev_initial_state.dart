import 'package:common/models/player.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/models/base/resource_value.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

const projectBasePath = '/Users/<USER>/dev/dauntless';
const gamesBasePath = '$projectBasePath/games';

class DevInitialState {
  static const basePath = '$gamesBasePath/liberator';
  // '/Users/<USER>/dev/dauntless/games/liberator';
  static const _rawResourceKey = 'raw';
  static const _refinedResourceKey = 'refined';

  static const PlayerId _alliancePlayerId = 'alliance';
  static const Player alliancePlayer = Player(
      id: _alliancePlayerId,
      name: '<PERSON> Skywalker',
      type: PlayerType.humanLocal);

  static const List<Player> players = [alliancePlayer, imperialPlayer];

  static const List<GameCard> _rebelStartingHand = [
    GameCard(
        id: '1',
        name: 'Abe The X-Wing',
        locationId: 'fleet1',
        classId: 'alliance_x_wing',
        type: CardType.vehicle),
    GameCard(
        id: 'fleet1',
        name: 'Fleet 1',
        locationId: 'unknown_aargau',
        classId: 'alliance_fleet',
        type: CardType.grouping),
    GameCard(
        id: '2',
        name: 'Nancy The Nebulon',
        locationId: 'fleet1',
        classId: 'alliance_nebulon_b_frigate',
        type: CardType.vehicle),
    GameCard(
        id: '3',
        name: 'Transport Me Brah',
        locationId: 'fleet1',
        classId: 'alliance_medium_transport',
        type: CardType.vehicle)
  ];

  static const Map<ResourceId, ResourceValue> _allianceResources = {
    _refinedResourceKey: ResourceValue(id: _refinedResourceKey, value: 100.0),
    _rawResourceKey: ResourceValue(id: _rawResourceKey, value: 250.0),
  };

  static const PlayerId _imperialPlayerId = 'imperial';
  static const Player imperialPlayer = Player(
      id: _imperialPlayerId, name: 'Darth Vader', type: PlayerType.botLocal);

  static const List<GameCard> _imperialStartingHand = [
    GameCard(
        id: '4',
        name: 'Death Star',
        locationId: 'fleet2',
        classId: 'imperial_death_star',
        type: CardType.vehicle),
    GameCard(
        id: 'fleet2',
        name: 'Fleet 2',
        locationId: 'coreWorlds_coruscant',
        classId: 'imperial_fleet',
        type: CardType.grouping),
    GameCard(
        id: '5',
        name: 'Star Destroyer',
        locationId: 'fleet2',
        classId: 'imperial_star_destroyer',
        type: CardType.vehicle),
    GameCard(
        id: '6',
        name: 'Transporter',
        locationId: 'fleet2',
        classId: 'imperial_medium_transport',
        type: CardType.vehicle),
    GameCard(
      id: 'building0',
      name: 'Construction Yard',
      locationId: 'coreWorlds_coruscant',
      classId: 'construction_yard',
      type: CardType.building,
    )
  ];

  static const Map<ResourceId, ResourceValue> _imperialResources = {
    _refinedResourceKey: ResourceValue(id: _refinedResourceKey, value: 100.0),
    _rawResourceKey: ResourceValue(id: _rawResourceKey, value: 250.0),
  };

  static const MatchConfig _matchConfig = MatchConfig(
      gameId: 'liberator',
      matchId: 'dauntless',
      name: 'Dauntless',
      hostId: _alliancePlayerId,
      selectedGameMode: GameMode.server);

  static GameMatchState initialMatchState = const GameMatchState(
    matchConfig: _matchConfig,
    userPlayerId: _alliancePlayerId,
    hands: {
      _alliancePlayerId: _rebelStartingHand,
      _imperialPlayerId: _imperialStartingHand
    },
    resources: {
      _alliancePlayerId: _allianceResources,
      _imperialPlayerId: _imperialResources
    },
    turnCount: 0,
    loadedCardClasses: {},
  );
}
