// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theme_sizings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ThemeSizings _$ThemeSizingsFromJson(Map<String, dynamic> json) =>
    _ThemeSizings(
      iconSize: (json['iconSize'] as num?)?.toDouble() ?? 24,
      selectedActionWidth:
          (json['selectedActionWidth'] as num?)?.toDouble() ?? 184,
      primaryCardWidth: (json['primaryCardWidth'] as num?)?.toDouble() ?? 496,
      detailedCardWidth: (json['detailedCardWidth'] as num?)?.toDouble() ?? 304,
      mediumImageWidth: (json['mediumImageWidth'] as num?)?.toDouble() ?? 120,
      primaryGridSquareSize:
          (json['primaryGridSquareSize'] as num?)?.toDouble() ?? 800,
      padding: (json['padding'] as num?)?.toDouble() ?? 8,
      smallPadding: (json['smallPadding'] as num?)?.toDouble() ?? 4,
      tightBorderRadius: (json['tightBorderRadius'] as num?)?.toDouble() ?? 8,
    );

Map<String, dynamic> _$ThemeSizingsToJson(_ThemeSizings instance) =>
    <String, dynamic>{
      'iconSize': instance.iconSize,
      'selectedActionWidth': instance.selectedActionWidth,
      'primaryCardWidth': instance.primaryCardWidth,
      'detailedCardWidth': instance.detailedCardWidth,
      'mediumImageWidth': instance.mediumImageWidth,
      'primaryGridSquareSize': instance.primaryGridSquareSize,
      'padding': instance.padding,
      'smallPadding': instance.smallPadding,
      'tightBorderRadius': instance.tightBorderRadius,
    };
