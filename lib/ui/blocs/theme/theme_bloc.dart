import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/theme_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'theme_state.dart';

abstract class ThemeEvent {}

class LoadThemeEvent extends ThemeEvent {}

class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final ThemeUseCase _themeUseCase;
  ThemeBloc(this._themeUseCase) : super(const ThemeState()) {
    on<LoadThemeEvent>(_onLoadThemeEvent);
  }

  void _onLoadThemeEvent(LoadThemeEvent event, Emitter<ThemeState> emit) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    await _themeUseCase.init();
    emit(state.copyWith(
      theme: _themeUseCase.theme,
      processingStatus: ProcessingStatus.loaded,
    ));
  }
}