import 'package:freezed_annotation/freezed_annotation.dart';

part 'theme_sizings.freezed.dart';
part 'theme_sizings.g.dart';

@freezed
abstract class ThemeSizings with _$ThemeSizings {
  const factory ThemeSizings({
    @Default(24) double iconSize,
    @Default(184) double selectedActionWidth,
    @Default(496) double primaryCardWidth,
    @Default(304) double detailedCardWidth,
    @Default(120) double mediumImageWidth,
    @Deprecated('old galaxy view')
    @Default(800) double primaryGridSquareSize,
    @Default(8) double padding,
    @Default(4) double smallPadding,
    @Default(8) double tightBorderRadius,
  }) = _ThemeSizings;

  factory ThemeSizings.fromJson(Map<String, dynamic> json) => _$ThemeSizingsFromJson(json);
  }
