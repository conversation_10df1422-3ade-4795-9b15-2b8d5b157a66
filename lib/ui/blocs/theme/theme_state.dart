import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'theme_sizings.dart';

part 'theme_state.freezed.dart';
// part 'theme_state.g.dart';

// TODO: use ThemeDecoder in a Converter here directly
@freezed
abstract class ThemeState with _$ThemeState {
  const factory ThemeState({
    ThemeData? theme,
    @Default(ThemeSizings()) ThemeSizings sizings,
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
  }) = _ThemeState;

  // factory ThemeState.fromJson(Map<String, dynamic> json) => _$ThemeStateFromJson(json);
}