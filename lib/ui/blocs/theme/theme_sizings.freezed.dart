// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'theme_sizings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ThemeSizings {
  double get iconSize;
  double get selectedActionWidth;
  double get primaryCardWidth;
  double get detailedCardWidth;
  double get mediumImageWidth;
  @Deprecated('old galaxy view')
  double get primaryGridSquareSize;
  double get padding;
  double get smallPadding;
  double get tightBorderRadius;

  /// Create a copy of ThemeSizings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ThemeSizingsCopyWith<ThemeSizings> get copyWith =>
      _$ThemeSizingsCopyWithImpl<ThemeSizings>(
          this as ThemeSizings, _$identity);

  /// Serializes this ThemeSizings to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ThemeSizings &&
            (identical(other.iconSize, iconSize) ||
                other.iconSize == iconSize) &&
            (identical(other.selectedActionWidth, selectedActionWidth) ||
                other.selectedActionWidth == selectedActionWidth) &&
            (identical(other.primaryCardWidth, primaryCardWidth) ||
                other.primaryCardWidth == primaryCardWidth) &&
            (identical(other.detailedCardWidth, detailedCardWidth) ||
                other.detailedCardWidth == detailedCardWidth) &&
            (identical(other.mediumImageWidth, mediumImageWidth) ||
                other.mediumImageWidth == mediumImageWidth) &&
            (identical(other.primaryGridSquareSize, primaryGridSquareSize) ||
                other.primaryGridSquareSize == primaryGridSquareSize) &&
            (identical(other.padding, padding) || other.padding == padding) &&
            (identical(other.smallPadding, smallPadding) ||
                other.smallPadding == smallPadding) &&
            (identical(other.tightBorderRadius, tightBorderRadius) ||
                other.tightBorderRadius == tightBorderRadius));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      iconSize,
      selectedActionWidth,
      primaryCardWidth,
      detailedCardWidth,
      mediumImageWidth,
      primaryGridSquareSize,
      padding,
      smallPadding,
      tightBorderRadius);

  @override
  String toString() {
    return 'ThemeSizings(iconSize: $iconSize, selectedActionWidth: $selectedActionWidth, primaryCardWidth: $primaryCardWidth, detailedCardWidth: $detailedCardWidth, mediumImageWidth: $mediumImageWidth, primaryGridSquareSize: $primaryGridSquareSize, padding: $padding, smallPadding: $smallPadding, tightBorderRadius: $tightBorderRadius)';
  }
}

/// @nodoc
abstract mixin class $ThemeSizingsCopyWith<$Res> {
  factory $ThemeSizingsCopyWith(
          ThemeSizings value, $Res Function(ThemeSizings) _then) =
      _$ThemeSizingsCopyWithImpl;
  @useResult
  $Res call(
      {double iconSize,
      double selectedActionWidth,
      double primaryCardWidth,
      double detailedCardWidth,
      double mediumImageWidth,
      @Deprecated('old galaxy view') double primaryGridSquareSize,
      double padding,
      double smallPadding,
      double tightBorderRadius});
}

/// @nodoc
class _$ThemeSizingsCopyWithImpl<$Res> implements $ThemeSizingsCopyWith<$Res> {
  _$ThemeSizingsCopyWithImpl(this._self, this._then);

  final ThemeSizings _self;
  final $Res Function(ThemeSizings) _then;

  /// Create a copy of ThemeSizings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? iconSize = null,
    Object? selectedActionWidth = null,
    Object? primaryCardWidth = null,
    Object? detailedCardWidth = null,
    Object? mediumImageWidth = null,
    Object? primaryGridSquareSize = null,
    Object? padding = null,
    Object? smallPadding = null,
    Object? tightBorderRadius = null,
  }) {
    return _then(_self.copyWith(
      iconSize: null == iconSize
          ? _self.iconSize
          : iconSize // ignore: cast_nullable_to_non_nullable
              as double,
      selectedActionWidth: null == selectedActionWidth
          ? _self.selectedActionWidth
          : selectedActionWidth // ignore: cast_nullable_to_non_nullable
              as double,
      primaryCardWidth: null == primaryCardWidth
          ? _self.primaryCardWidth
          : primaryCardWidth // ignore: cast_nullable_to_non_nullable
              as double,
      detailedCardWidth: null == detailedCardWidth
          ? _self.detailedCardWidth
          : detailedCardWidth // ignore: cast_nullable_to_non_nullable
              as double,
      mediumImageWidth: null == mediumImageWidth
          ? _self.mediumImageWidth
          : mediumImageWidth // ignore: cast_nullable_to_non_nullable
              as double,
      primaryGridSquareSize: null == primaryGridSquareSize
          ? _self.primaryGridSquareSize
          : primaryGridSquareSize // ignore: cast_nullable_to_non_nullable
              as double,
      padding: null == padding
          ? _self.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as double,
      smallPadding: null == smallPadding
          ? _self.smallPadding
          : smallPadding // ignore: cast_nullable_to_non_nullable
              as double,
      tightBorderRadius: null == tightBorderRadius
          ? _self.tightBorderRadius
          : tightBorderRadius // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ThemeSizings implements ThemeSizings {
  const _ThemeSizings(
      {this.iconSize = 24,
      this.selectedActionWidth = 184,
      this.primaryCardWidth = 496,
      this.detailedCardWidth = 304,
      this.mediumImageWidth = 120,
      @Deprecated('old galaxy view') this.primaryGridSquareSize = 800,
      this.padding = 8,
      this.smallPadding = 4,
      this.tightBorderRadius = 8});
  factory _ThemeSizings.fromJson(Map<String, dynamic> json) =>
      _$ThemeSizingsFromJson(json);

  @override
  @JsonKey()
  final double iconSize;
  @override
  @JsonKey()
  final double selectedActionWidth;
  @override
  @JsonKey()
  final double primaryCardWidth;
  @override
  @JsonKey()
  final double detailedCardWidth;
  @override
  @JsonKey()
  final double mediumImageWidth;
  @override
  @JsonKey()
  @Deprecated('old galaxy view')
  final double primaryGridSquareSize;
  @override
  @JsonKey()
  final double padding;
  @override
  @JsonKey()
  final double smallPadding;
  @override
  @JsonKey()
  final double tightBorderRadius;

  /// Create a copy of ThemeSizings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ThemeSizingsCopyWith<_ThemeSizings> get copyWith =>
      __$ThemeSizingsCopyWithImpl<_ThemeSizings>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ThemeSizingsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ThemeSizings &&
            (identical(other.iconSize, iconSize) ||
                other.iconSize == iconSize) &&
            (identical(other.selectedActionWidth, selectedActionWidth) ||
                other.selectedActionWidth == selectedActionWidth) &&
            (identical(other.primaryCardWidth, primaryCardWidth) ||
                other.primaryCardWidth == primaryCardWidth) &&
            (identical(other.detailedCardWidth, detailedCardWidth) ||
                other.detailedCardWidth == detailedCardWidth) &&
            (identical(other.mediumImageWidth, mediumImageWidth) ||
                other.mediumImageWidth == mediumImageWidth) &&
            (identical(other.primaryGridSquareSize, primaryGridSquareSize) ||
                other.primaryGridSquareSize == primaryGridSquareSize) &&
            (identical(other.padding, padding) || other.padding == padding) &&
            (identical(other.smallPadding, smallPadding) ||
                other.smallPadding == smallPadding) &&
            (identical(other.tightBorderRadius, tightBorderRadius) ||
                other.tightBorderRadius == tightBorderRadius));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      iconSize,
      selectedActionWidth,
      primaryCardWidth,
      detailedCardWidth,
      mediumImageWidth,
      primaryGridSquareSize,
      padding,
      smallPadding,
      tightBorderRadius);

  @override
  String toString() {
    return 'ThemeSizings(iconSize: $iconSize, selectedActionWidth: $selectedActionWidth, primaryCardWidth: $primaryCardWidth, detailedCardWidth: $detailedCardWidth, mediumImageWidth: $mediumImageWidth, primaryGridSquareSize: $primaryGridSquareSize, padding: $padding, smallPadding: $smallPadding, tightBorderRadius: $tightBorderRadius)';
  }
}

/// @nodoc
abstract mixin class _$ThemeSizingsCopyWith<$Res>
    implements $ThemeSizingsCopyWith<$Res> {
  factory _$ThemeSizingsCopyWith(
          _ThemeSizings value, $Res Function(_ThemeSizings) _then) =
      __$ThemeSizingsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {double iconSize,
      double selectedActionWidth,
      double primaryCardWidth,
      double detailedCardWidth,
      double mediumImageWidth,
      @Deprecated('old galaxy view') double primaryGridSquareSize,
      double padding,
      double smallPadding,
      double tightBorderRadius});
}

/// @nodoc
class __$ThemeSizingsCopyWithImpl<$Res>
    implements _$ThemeSizingsCopyWith<$Res> {
  __$ThemeSizingsCopyWithImpl(this._self, this._then);

  final _ThemeSizings _self;
  final $Res Function(_ThemeSizings) _then;

  /// Create a copy of ThemeSizings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? iconSize = null,
    Object? selectedActionWidth = null,
    Object? primaryCardWidth = null,
    Object? detailedCardWidth = null,
    Object? mediumImageWidth = null,
    Object? primaryGridSquareSize = null,
    Object? padding = null,
    Object? smallPadding = null,
    Object? tightBorderRadius = null,
  }) {
    return _then(_ThemeSizings(
      iconSize: null == iconSize
          ? _self.iconSize
          : iconSize // ignore: cast_nullable_to_non_nullable
              as double,
      selectedActionWidth: null == selectedActionWidth
          ? _self.selectedActionWidth
          : selectedActionWidth // ignore: cast_nullable_to_non_nullable
              as double,
      primaryCardWidth: null == primaryCardWidth
          ? _self.primaryCardWidth
          : primaryCardWidth // ignore: cast_nullable_to_non_nullable
              as double,
      detailedCardWidth: null == detailedCardWidth
          ? _self.detailedCardWidth
          : detailedCardWidth // ignore: cast_nullable_to_non_nullable
              as double,
      mediumImageWidth: null == mediumImageWidth
          ? _self.mediumImageWidth
          : mediumImageWidth // ignore: cast_nullable_to_non_nullable
              as double,
      primaryGridSquareSize: null == primaryGridSquareSize
          ? _self.primaryGridSquareSize
          : primaryGridSquareSize // ignore: cast_nullable_to_non_nullable
              as double,
      padding: null == padding
          ? _self.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as double,
      smallPadding: null == smallPadding
          ? _self.smallPadding
          : smallPadding // ignore: cast_nullable_to_non_nullable
              as double,
      tightBorderRadius: null == tightBorderRadius
          ? _self.tightBorderRadius
          : tightBorderRadius // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

// dart format on
