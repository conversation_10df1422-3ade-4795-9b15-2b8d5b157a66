import 'package:dauntless/ui/blocs/ui_event.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vector_math/vector_math_64.dart';

part 'map_instance_state.freezed.dart';

@Freezed(fromJson: false, toJson: false)
abstract class MapInstanceState with _$MapInstanceState {

  const factory MapInstanceState({
    @Default(5) double maxScale,
    @Default(0.1) double minScale,
    @Default(0.1) double interactionEndFrictionCoefficient,
    // TODO: better name this ... needs clear distinction from the event that triggers it ... is there a more elegant way to handle bloc => bloc communication?
    UiEvent<Matrix4>? navigateToPositionMatrix4,
    BoxConstraints? constraints,
    // TODO: can we get the identity matrix as the default?
    Matrix4? transformationControllerValue
}) = _MapInstanceState;
}