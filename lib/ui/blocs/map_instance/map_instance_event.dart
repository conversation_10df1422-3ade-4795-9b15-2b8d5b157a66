import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart';

abstract class MapInstanceEvent {}
class MapInstanceOnInteractionEndEvent extends MapInstanceEvent {
  final ScaleEndDetails details;
  final Matrix4 transformationControllerValue;
  MapInstanceOnInteractionEndEvent(this.details, this.transformationControllerValue);
}
class MapInstanceOnInteractionStartEvent extends MapInstanceEvent {
  final ScaleStartDetails details;
  MapInstanceOnInteractionStartEvent(this.details);
}
class MapInstanceOnInteractionUpdateEvent extends MapInstanceEvent {
  final ScaleUpdateDetails details;
  final Matrix4 transformationControllerValue;
  MapInstanceOnInteractionUpdateEvent(this.details, this.transformationControllerValue);
}
class NavigateToPositionEvent extends MapInstanceEvent {
  final Vector2 position;
  NavigateToPositionEvent(this.position);
}
class MapInstanceOnSizeChangedEvent extends MapInstanceEvent {
  final BoxConstraints constraints;
  MapInstanceOnSizeChangedEvent(this.constraints);
}