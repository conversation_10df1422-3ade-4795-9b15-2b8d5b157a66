// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'map_instance_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MapInstanceState {
  double get maxScale;
  double get minScale;
  double
      get interactionEndFrictionCoefficient; // TODO: better name this ... needs clear distinction from the event that triggers it ... is there a more elegant way to handle bloc => bloc communication?
  UiEvent<Matrix4>? get navigateToPositionMatrix4;
  BoxConstraints?
      get constraints; // TODO: can we get the identity matrix as the default?
  Matrix4? get transformationControllerValue;

  /// Create a copy of MapInstanceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MapInstanceStateCopyWith<MapInstanceState> get copyWith =>
      _$MapInstanceStateCopyWithImpl<MapInstanceState>(
          this as MapInstanceState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MapInstanceState &&
            (identical(other.maxScale, maxScale) ||
                other.maxScale == maxScale) &&
            (identical(other.minScale, minScale) ||
                other.minScale == minScale) &&
            (identical(other.interactionEndFrictionCoefficient,
                    interactionEndFrictionCoefficient) ||
                other.interactionEndFrictionCoefficient ==
                    interactionEndFrictionCoefficient) &&
            (identical(other.navigateToPositionMatrix4,
                    navigateToPositionMatrix4) ||
                other.navigateToPositionMatrix4 == navigateToPositionMatrix4) &&
            (identical(other.constraints, constraints) ||
                other.constraints == constraints) &&
            (identical(other.transformationControllerValue,
                    transformationControllerValue) ||
                other.transformationControllerValue ==
                    transformationControllerValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      maxScale,
      minScale,
      interactionEndFrictionCoefficient,
      navigateToPositionMatrix4,
      constraints,
      transformationControllerValue);

  @override
  String toString() {
    return 'MapInstanceState(maxScale: $maxScale, minScale: $minScale, interactionEndFrictionCoefficient: $interactionEndFrictionCoefficient, navigateToPositionMatrix4: $navigateToPositionMatrix4, constraints: $constraints, transformationControllerValue: $transformationControllerValue)';
  }
}

/// @nodoc
abstract mixin class $MapInstanceStateCopyWith<$Res> {
  factory $MapInstanceStateCopyWith(
          MapInstanceState value, $Res Function(MapInstanceState) _then) =
      _$MapInstanceStateCopyWithImpl;
  @useResult
  $Res call(
      {double maxScale,
      double minScale,
      double interactionEndFrictionCoefficient,
      UiEvent<Matrix4>? navigateToPositionMatrix4,
      BoxConstraints? constraints,
      Matrix4? transformationControllerValue});
}

/// @nodoc
class _$MapInstanceStateCopyWithImpl<$Res>
    implements $MapInstanceStateCopyWith<$Res> {
  _$MapInstanceStateCopyWithImpl(this._self, this._then);

  final MapInstanceState _self;
  final $Res Function(MapInstanceState) _then;

  /// Create a copy of MapInstanceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxScale = null,
    Object? minScale = null,
    Object? interactionEndFrictionCoefficient = null,
    Object? navigateToPositionMatrix4 = freezed,
    Object? constraints = freezed,
    Object? transformationControllerValue = freezed,
  }) {
    return _then(_self.copyWith(
      maxScale: null == maxScale
          ? _self.maxScale
          : maxScale // ignore: cast_nullable_to_non_nullable
              as double,
      minScale: null == minScale
          ? _self.minScale
          : minScale // ignore: cast_nullable_to_non_nullable
              as double,
      interactionEndFrictionCoefficient: null ==
              interactionEndFrictionCoefficient
          ? _self.interactionEndFrictionCoefficient
          : interactionEndFrictionCoefficient // ignore: cast_nullable_to_non_nullable
              as double,
      navigateToPositionMatrix4: freezed == navigateToPositionMatrix4
          ? _self.navigateToPositionMatrix4
          : navigateToPositionMatrix4 // ignore: cast_nullable_to_non_nullable
              as UiEvent<Matrix4>?,
      constraints: freezed == constraints
          ? _self.constraints
          : constraints // ignore: cast_nullable_to_non_nullable
              as BoxConstraints?,
      transformationControllerValue: freezed == transformationControllerValue
          ? _self.transformationControllerValue
          : transformationControllerValue // ignore: cast_nullable_to_non_nullable
              as Matrix4?,
    ));
  }
}

/// @nodoc

class _MapInstanceState implements MapInstanceState {
  const _MapInstanceState(
      {this.maxScale = 5,
      this.minScale = 0.1,
      this.interactionEndFrictionCoefficient = 0.1,
      this.navigateToPositionMatrix4,
      this.constraints,
      this.transformationControllerValue});

  @override
  @JsonKey()
  final double maxScale;
  @override
  @JsonKey()
  final double minScale;
  @override
  @JsonKey()
  final double interactionEndFrictionCoefficient;
// TODO: better name this ... needs clear distinction from the event that triggers it ... is there a more elegant way to handle bloc => bloc communication?
  @override
  final UiEvent<Matrix4>? navigateToPositionMatrix4;
  @override
  final BoxConstraints? constraints;
// TODO: can we get the identity matrix as the default?
  @override
  final Matrix4? transformationControllerValue;

  /// Create a copy of MapInstanceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MapInstanceStateCopyWith<_MapInstanceState> get copyWith =>
      __$MapInstanceStateCopyWithImpl<_MapInstanceState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MapInstanceState &&
            (identical(other.maxScale, maxScale) ||
                other.maxScale == maxScale) &&
            (identical(other.minScale, minScale) ||
                other.minScale == minScale) &&
            (identical(other.interactionEndFrictionCoefficient,
                    interactionEndFrictionCoefficient) ||
                other.interactionEndFrictionCoefficient ==
                    interactionEndFrictionCoefficient) &&
            (identical(other.navigateToPositionMatrix4,
                    navigateToPositionMatrix4) ||
                other.navigateToPositionMatrix4 == navigateToPositionMatrix4) &&
            (identical(other.constraints, constraints) ||
                other.constraints == constraints) &&
            (identical(other.transformationControllerValue,
                    transformationControllerValue) ||
                other.transformationControllerValue ==
                    transformationControllerValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      maxScale,
      minScale,
      interactionEndFrictionCoefficient,
      navigateToPositionMatrix4,
      constraints,
      transformationControllerValue);

  @override
  String toString() {
    return 'MapInstanceState(maxScale: $maxScale, minScale: $minScale, interactionEndFrictionCoefficient: $interactionEndFrictionCoefficient, navigateToPositionMatrix4: $navigateToPositionMatrix4, constraints: $constraints, transformationControllerValue: $transformationControllerValue)';
  }
}

/// @nodoc
abstract mixin class _$MapInstanceStateCopyWith<$Res>
    implements $MapInstanceStateCopyWith<$Res> {
  factory _$MapInstanceStateCopyWith(
          _MapInstanceState value, $Res Function(_MapInstanceState) _then) =
      __$MapInstanceStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {double maxScale,
      double minScale,
      double interactionEndFrictionCoefficient,
      UiEvent<Matrix4>? navigateToPositionMatrix4,
      BoxConstraints? constraints,
      Matrix4? transformationControllerValue});
}

/// @nodoc
class __$MapInstanceStateCopyWithImpl<$Res>
    implements _$MapInstanceStateCopyWith<$Res> {
  __$MapInstanceStateCopyWithImpl(this._self, this._then);

  final _MapInstanceState _self;
  final $Res Function(_MapInstanceState) _then;

  /// Create a copy of MapInstanceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? maxScale = null,
    Object? minScale = null,
    Object? interactionEndFrictionCoefficient = null,
    Object? navigateToPositionMatrix4 = freezed,
    Object? constraints = freezed,
    Object? transformationControllerValue = freezed,
  }) {
    return _then(_MapInstanceState(
      maxScale: null == maxScale
          ? _self.maxScale
          : maxScale // ignore: cast_nullable_to_non_nullable
              as double,
      minScale: null == minScale
          ? _self.minScale
          : minScale // ignore: cast_nullable_to_non_nullable
              as double,
      interactionEndFrictionCoefficient: null ==
              interactionEndFrictionCoefficient
          ? _self.interactionEndFrictionCoefficient
          : interactionEndFrictionCoefficient // ignore: cast_nullable_to_non_nullable
              as double,
      navigateToPositionMatrix4: freezed == navigateToPositionMatrix4
          ? _self.navigateToPositionMatrix4
          : navigateToPositionMatrix4 // ignore: cast_nullable_to_non_nullable
              as UiEvent<Matrix4>?,
      constraints: freezed == constraints
          ? _self.constraints
          : constraints // ignore: cast_nullable_to_non_nullable
              as BoxConstraints?,
      transformationControllerValue: freezed == transformationControllerValue
          ? _self.transformationControllerValue
          : transformationControllerValue // ignore: cast_nullable_to_non_nullable
              as Matrix4?,
    ));
  }
}

// dart format on
