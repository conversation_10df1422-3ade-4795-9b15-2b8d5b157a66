import 'package:dauntless/ui/blocs/ui_event.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vector_math/vector_math_64.dart';

import 'map_instance_event.dart';
import 'map_instance_state.dart';

class MapInstanceBloc extends Bloc<MapInstanceEvent, MapInstanceState> {
  MapInstanceBloc() : super(const MapInstanceState()) {
    on<NavigateToPositionEvent>(_onNavigateToPosition);
    on<MapInstanceOnInteractionStartEvent>(_onInteractionStart);
    on<MapInstanceOnInteractionUpdateEvent>(_onInteractionUpdate);
    on<MapInstanceOnInteractionEndEvent>(_onInteractionEnd);
    on<MapInstanceOnSizeChangedEvent>(_onSizeChanged);
  }

  void _onNavigateToPosition(NavigateToPositionEvent event, Emitter<MapInstanceState> emit) {
    // TODO: standardize +/- map handling here & GalaxyMapView
    final centerAdjustment = state.constraints != null ? Vector2(
        -(state.constraints!.maxWidth / 2),
        -(state.constraints!.maxHeight / 2)
    ) : Vector2(0, 0);
    final position = event.position.clone()..add(centerAdjustment);
    final matrix = Matrix4.identity()
      ..setEntry(0, 3, -position.x)
      ..setEntry(1, 3, -position.y);
    emit(state.copyWith(navigateToPositionMatrix4: UiEvent(matrix)));
  }

  void _onInteractionStart(MapInstanceOnInteractionStartEvent event, Emitter<MapInstanceState> emit) {

  }

  void _onInteractionUpdate(MapInstanceOnInteractionUpdateEvent event, Emitter<MapInstanceState> emit) {
    emit(state.copyWith(
      transformationControllerValue: event.transformationControllerValue,
    ));
  }

  void _onInteractionEnd(MapInstanceOnInteractionEndEvent event, Emitter<MapInstanceState> emit) {
    emit(state.copyWith(
      transformationControllerValue: event.transformationControllerValue,
    ));
  }

  void _onSizeChanged(MapInstanceOnSizeChangedEvent event, Emitter<MapInstanceState> emit) {
    emit(state.copyWith(constraints: event.constraints));
  }
}
