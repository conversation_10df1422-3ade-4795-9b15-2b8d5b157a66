import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';

part 'map_grid_state.freezed.dart';

@Freezed(fromJson: false, toJson: false)
abstract class MapGridState<T extends GameCard> with _$MapGridState<T> {
  //extends Equatable { // TODO: add equatable? Figure out the right freezed-way to handle this? something else for a generic?

  const MapGridState._();

  const factory MapGridState({
    @Default([]) List<T> list,
    GameCardId? listCardId,
    T? selectedObject,
    // bool Function(T)? objectViewCondition,
    // @Default(MapGridViewType.list) MapGridViewType viewType,
    // @Default(
    //     MapGridViewBuildOption.children) MapGridViewBuildOption buildType,
    @Default([]) List<CardGrouping> groupings,
    T? hoverObject,
  }) = _MapGridState<T>;

}