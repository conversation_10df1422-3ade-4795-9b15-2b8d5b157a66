import 'package:dauntless/models/base/game_card.dart';

abstract class MapGridEvent<T extends GameCard> {}

class InitMapGridEvent<T extends GameCard> extends MapGridEvent<T> {
  final bool Function(T)? objectViewCondition;

  InitMapGridEvent(this.objectViewCondition);
}

class SelectMapGridEvent<T extends GameCard> extends MapGridEvent<T> {
  final T object;

  SelectMapGridEvent(this.object);
}
class HoverMapGridEvent<T extends GameCard> extends MapGridEvent<T> {
  final T? object;

  HoverMapGridEvent(this.object);
}