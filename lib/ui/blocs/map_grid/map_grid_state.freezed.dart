// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'map_grid_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MapGridState<T extends GameCard> {
  List<T> get list;
  GameCardId? get listCardId;
  T? get selectedObject; // bool Function(T)? objectViewCondition,
// @Default(MapGridViewType.list) MapGridViewType viewType,
// @Default(
//     MapGridViewBuildOption.children) MapGridViewBuildOption buildType,
  List<CardGrouping> get groupings;
  T? get hoverObject;

  /// Create a copy of MapGridState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MapGridStateCopyWith<T, MapGridState<T>> get copyWith =>
      _$MapGridStateCopyWithImpl<T, MapGridState<T>>(
          this as MapGridState<T>, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MapGridState<T> &&
            const DeepCollectionEquality().equals(other.list, list) &&
            (identical(other.listCardId, listCardId) ||
                other.listCardId == listCardId) &&
            const DeepCollectionEquality()
                .equals(other.selectedObject, selectedObject) &&
            const DeepCollectionEquality().equals(other.groupings, groupings) &&
            const DeepCollectionEquality()
                .equals(other.hoverObject, hoverObject));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(list),
      listCardId,
      const DeepCollectionEquality().hash(selectedObject),
      const DeepCollectionEquality().hash(groupings),
      const DeepCollectionEquality().hash(hoverObject));

  @override
  String toString() {
    return 'MapGridState<$T>(list: $list, listCardId: $listCardId, selectedObject: $selectedObject, groupings: $groupings, hoverObject: $hoverObject)';
  }
}

/// @nodoc
abstract mixin class $MapGridStateCopyWith<T extends GameCard, $Res> {
  factory $MapGridStateCopyWith(
          MapGridState<T> value, $Res Function(MapGridState<T>) _then) =
      _$MapGridStateCopyWithImpl;
  @useResult
  $Res call(
      {List<T> list,
      GameCardId? listCardId,
      T? selectedObject,
      List<CardGrouping> groupings,
      T? hoverObject});
}

/// @nodoc
class _$MapGridStateCopyWithImpl<T extends GameCard, $Res>
    implements $MapGridStateCopyWith<T, $Res> {
  _$MapGridStateCopyWithImpl(this._self, this._then);

  final MapGridState<T> _self;
  final $Res Function(MapGridState<T>) _then;

  /// Create a copy of MapGridState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = null,
    Object? listCardId = freezed,
    Object? selectedObject = freezed,
    Object? groupings = null,
    Object? hoverObject = freezed,
  }) {
    return _then(_self.copyWith(
      list: null == list
          ? _self.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<T>,
      listCardId: freezed == listCardId
          ? _self.listCardId
          : listCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      selectedObject: freezed == selectedObject
          ? _self.selectedObject
          : selectedObject // ignore: cast_nullable_to_non_nullable
              as T?,
      groupings: null == groupings
          ? _self.groupings
          : groupings // ignore: cast_nullable_to_non_nullable
              as List<CardGrouping>,
      hoverObject: freezed == hoverObject
          ? _self.hoverObject
          : hoverObject // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

/// @nodoc

class _MapGridState<T extends GameCard> extends MapGridState<T> {
  const _MapGridState(
      {final List<T> list = const [],
      this.listCardId,
      this.selectedObject,
      final List<CardGrouping> groupings = const [],
      this.hoverObject})
      : _list = list,
        _groupings = groupings,
        super._();

  final List<T> _list;
  @override
  @JsonKey()
  List<T> get list {
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_list);
  }

  @override
  final GameCardId? listCardId;
  @override
  final T? selectedObject;
// bool Function(T)? objectViewCondition,
// @Default(MapGridViewType.list) MapGridViewType viewType,
// @Default(
//     MapGridViewBuildOption.children) MapGridViewBuildOption buildType,
  final List<CardGrouping> _groupings;
// bool Function(T)? objectViewCondition,
// @Default(MapGridViewType.list) MapGridViewType viewType,
// @Default(
//     MapGridViewBuildOption.children) MapGridViewBuildOption buildType,
  @override
  @JsonKey()
  List<CardGrouping> get groupings {
    if (_groupings is EqualUnmodifiableListView) return _groupings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_groupings);
  }

  @override
  final T? hoverObject;

  /// Create a copy of MapGridState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MapGridStateCopyWith<T, _MapGridState<T>> get copyWith =>
      __$MapGridStateCopyWithImpl<T, _MapGridState<T>>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MapGridState<T> &&
            const DeepCollectionEquality().equals(other._list, _list) &&
            (identical(other.listCardId, listCardId) ||
                other.listCardId == listCardId) &&
            const DeepCollectionEquality()
                .equals(other.selectedObject, selectedObject) &&
            const DeepCollectionEquality()
                .equals(other._groupings, _groupings) &&
            const DeepCollectionEquality()
                .equals(other.hoverObject, hoverObject));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_list),
      listCardId,
      const DeepCollectionEquality().hash(selectedObject),
      const DeepCollectionEquality().hash(_groupings),
      const DeepCollectionEquality().hash(hoverObject));

  @override
  String toString() {
    return 'MapGridState<$T>(list: $list, listCardId: $listCardId, selectedObject: $selectedObject, groupings: $groupings, hoverObject: $hoverObject)';
  }
}

/// @nodoc
abstract mixin class _$MapGridStateCopyWith<T extends GameCard, $Res>
    implements $MapGridStateCopyWith<T, $Res> {
  factory _$MapGridStateCopyWith(
          _MapGridState<T> value, $Res Function(_MapGridState<T>) _then) =
      __$MapGridStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<T> list,
      GameCardId? listCardId,
      T? selectedObject,
      List<CardGrouping> groupings,
      T? hoverObject});
}

/// @nodoc
class __$MapGridStateCopyWithImpl<T extends GameCard, $Res>
    implements _$MapGridStateCopyWith<T, $Res> {
  __$MapGridStateCopyWithImpl(this._self, this._then);

  final _MapGridState<T> _self;
  final $Res Function(_MapGridState<T>) _then;

  /// Create a copy of MapGridState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? list = null,
    Object? listCardId = freezed,
    Object? selectedObject = freezed,
    Object? groupings = null,
    Object? hoverObject = freezed,
  }) {
    return _then(_MapGridState<T>(
      list: null == list
          ? _self._list
          : list // ignore: cast_nullable_to_non_nullable
              as List<T>,
      listCardId: freezed == listCardId
          ? _self.listCardId
          : listCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      selectedObject: freezed == selectedObject
          ? _self.selectedObject
          : selectedObject // ignore: cast_nullable_to_non_nullable
              as T?,
      groupings: null == groupings
          ? _self._groupings
          : groupings // ignore: cast_nullable_to_non_nullable
              as List<CardGrouping>,
      hoverObject: freezed == hoverObject
          ? _self.hoverObject
          : hoverObject // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

// dart format on
