import 'package:dauntless/models/base/game_card.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'map_grid_event.dart';
import 'map_grid_state.dart';

class MapGridBloc<T extends GameCard> extends Bloc<MapGridEvent<T>, MapGridState<T>> {
  final List<T> objectList;
  final bool Function(T)? objectViewCondition;

  MapGridBloc(this.objectList, this.objectViewCondition) : super(MapGridState()) {
    on<InitMapGridEvent<T>>(_onInitMapGridEvent);
    on<SelectMapGridEvent<T>>(_onSelectMapGridEvent);
    on<HoverMapGridEvent<T>>(_onHoverMapGridEvent);
  }

  void _onInitMapGridEvent(
      InitMapGridEvent<T> event, Emitter<MapGridState<T>> emit) async {
    emit(state.copyWith(
        // buildType: event.buildType ?? state.buildType,
        // objectViewCondition: event.objectViewCondition
    ));
  }

  void _onSelectMapGridEvent(
      SelectMapGridEvent<T> event, Emitter<MapGridState<T>> emit) async {
    emit(state.copyWith(
        selectedObject: event.object
    ));
  }

  void _onHoverMapGridEvent(
      HoverMapGridEvent<T> event, Emitter<MapGridState<T>> emit) async {
    emit(state.copyWith(
        hoverObject: event.object
    ));
  }
}