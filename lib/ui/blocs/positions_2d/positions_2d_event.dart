import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/mapping/positioned_card_2d.dart';

abstract class Positions2dEvent {}
class LoadPositionItems2dEvent extends Positions2dEvent {
  final List<GameCardClass> items;
  LoadPositionItems2dEvent(this.items);
}
class HoverPositionItem2dEvent extends Positions2dEvent {
  final PositionedCard2d? item;
  HoverPositionItem2dEvent(this.item);
}
class TapPositionItem2dEvent extends Positions2dEvent {
  final PositionedCard2d item;
  TapPositionItem2dEvent(this.item);
}