import 'package:dauntless/ui/blocs/positions_2d/positions_2d_event.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/use_cases/map_grid_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'positions_2d_state.dart';

class Positions2dBloc extends Bloc<Positions2dEvent, Positions2dState> {
  final MapGridUseCase _mapGridUseCase;
  final LocationsUseCase _locationsUseCase;

  Positions2dBloc(this._mapGridUseCase, this._locationsUseCase)
      : super(const Positions2dState()) {
    on<LoadPositionItems2dEvent>(_onLoadPositionItems2dEvent);
    on<HoverPositionItem2dEvent>(_onHoverPositionItem2dEvent);
    on<TapPositionItem2dEvent>(_onTapPositionItem2dEvent);
  }

  void _onTapPositionItem2dEvent(
      TapPositionItem2dEvent event, Emitter<Positions2dState> emit) {
    // emit(state.copyWith(tappedItem: event.item));
  }

  void _onHoverPositionItem2dEvent(
      HoverPositionItem2dEvent event, Emitter<Positions2dState> emit) {
    /// move item to top of stack
    if (event.item != null) {
      final items = state.positionedCards
          .where((element) => element != event.item)
          .toList();
      items.add(event.item!);
      emit(state.copyWith(hoveredItem: event.item, positionedCards: items));
    } else {
      emit(state.copyWith(hoveredItem: event.item));
    }
  }

  // TODO: should the locations come directly from the locations use case?
  void _onLoadPositionItems2dEvent(
      LoadPositionItems2dEvent event, Emitter<Positions2dState> emit) {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    final (positionedCards, scaleFactor, screenOffset) =
        _mapGridUseCase.getPositionedCards(
            cards: event.items,
            gridPxWidth: state.gridPxWidth,
            gridPxHeight: state.gridPxHeight,
            padding: state.mapPadding);
    final gridLabels = _mapGridUseCase.getPositionedLabels(
      rowLabels: _locationsUseCase.gridRowLabels,
      columnLabels: _locationsUseCase.gridColumnLabels,
      offset: screenOffset,
      scaleFactor: scaleFactor,
    );
    final gridLines = _mapGridUseCase.getGeneratedGridLines(
        gridPxWidth: state.gridPxWidth,
        gridPxHeight: state.gridPxHeight,
        offset: screenOffset,
        // TODO: get from state <= config file; 1500 parsecs/grid square in Galaxy Map
        gridSize: 1500 * scaleFactor);
    emit(state.copyWith(
        processingStatus: ProcessingStatus.loaded,
        positionedCards: positionedCards,
        gridLines: gridLines,
        labels: gridLabels));
  }
}
