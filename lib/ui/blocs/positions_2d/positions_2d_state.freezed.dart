// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'positions_2d_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Positions2dState {
  PositionedCard2d? get hoveredItem;
  String? get initialFocusCardId;
  List<PositionedCard2d> get positionedCards;
  List<PositionedGridLine2d> get gridLines;
  List<PositionedLabel2d> get labels;
  int get gridPxHeight;
  int get gridPxWidth;
  int get locationImageSize; // @Default(64) int locationIconSize,
  EdgeInsets get mapPadding;
  ProcessingStatus get processingStatus;

  /// Create a copy of Positions2dState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $Positions2dStateCopyWith<Positions2dState> get copyWith =>
      _$Positions2dStateCopyWithImpl<Positions2dState>(
          this as Positions2dState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Positions2dState &&
            (identical(other.hoveredItem, hoveredItem) ||
                other.hoveredItem == hoveredItem) &&
            (identical(other.initialFocusCardId, initialFocusCardId) ||
                other.initialFocusCardId == initialFocusCardId) &&
            const DeepCollectionEquality()
                .equals(other.positionedCards, positionedCards) &&
            const DeepCollectionEquality().equals(other.gridLines, gridLines) &&
            const DeepCollectionEquality().equals(other.labels, labels) &&
            (identical(other.gridPxHeight, gridPxHeight) ||
                other.gridPxHeight == gridPxHeight) &&
            (identical(other.gridPxWidth, gridPxWidth) ||
                other.gridPxWidth == gridPxWidth) &&
            (identical(other.locationImageSize, locationImageSize) ||
                other.locationImageSize == locationImageSize) &&
            (identical(other.mapPadding, mapPadding) ||
                other.mapPadding == mapPadding) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      hoveredItem,
      initialFocusCardId,
      const DeepCollectionEquality().hash(positionedCards),
      const DeepCollectionEquality().hash(gridLines),
      const DeepCollectionEquality().hash(labels),
      gridPxHeight,
      gridPxWidth,
      locationImageSize,
      mapPadding,
      processingStatus);

  @override
  String toString() {
    return 'Positions2dState(hoveredItem: $hoveredItem, initialFocusCardId: $initialFocusCardId, positionedCards: $positionedCards, gridLines: $gridLines, labels: $labels, gridPxHeight: $gridPxHeight, gridPxWidth: $gridPxWidth, locationImageSize: $locationImageSize, mapPadding: $mapPadding, processingStatus: $processingStatus)';
  }
}

/// @nodoc
abstract mixin class $Positions2dStateCopyWith<$Res> {
  factory $Positions2dStateCopyWith(
          Positions2dState value, $Res Function(Positions2dState) _then) =
      _$Positions2dStateCopyWithImpl;
  @useResult
  $Res call(
      {PositionedCard2d? hoveredItem,
      String? initialFocusCardId,
      List<PositionedCard2d> positionedCards,
      List<PositionedGridLine2d> gridLines,
      List<PositionedLabel2d> labels,
      int gridPxHeight,
      int gridPxWidth,
      int locationImageSize,
      EdgeInsets mapPadding,
      ProcessingStatus processingStatus});

  $PositionedCard2dCopyWith<$Res>? get hoveredItem;
}

/// @nodoc
class _$Positions2dStateCopyWithImpl<$Res>
    implements $Positions2dStateCopyWith<$Res> {
  _$Positions2dStateCopyWithImpl(this._self, this._then);

  final Positions2dState _self;
  final $Res Function(Positions2dState) _then;

  /// Create a copy of Positions2dState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hoveredItem = freezed,
    Object? initialFocusCardId = freezed,
    Object? positionedCards = null,
    Object? gridLines = null,
    Object? labels = null,
    Object? gridPxHeight = null,
    Object? gridPxWidth = null,
    Object? locationImageSize = null,
    Object? mapPadding = null,
    Object? processingStatus = null,
  }) {
    return _then(_self.copyWith(
      hoveredItem: freezed == hoveredItem
          ? _self.hoveredItem
          : hoveredItem // ignore: cast_nullable_to_non_nullable
              as PositionedCard2d?,
      initialFocusCardId: freezed == initialFocusCardId
          ? _self.initialFocusCardId
          : initialFocusCardId // ignore: cast_nullable_to_non_nullable
              as String?,
      positionedCards: null == positionedCards
          ? _self.positionedCards
          : positionedCards // ignore: cast_nullable_to_non_nullable
              as List<PositionedCard2d>,
      gridLines: null == gridLines
          ? _self.gridLines
          : gridLines // ignore: cast_nullable_to_non_nullable
              as List<PositionedGridLine2d>,
      labels: null == labels
          ? _self.labels
          : labels // ignore: cast_nullable_to_non_nullable
              as List<PositionedLabel2d>,
      gridPxHeight: null == gridPxHeight
          ? _self.gridPxHeight
          : gridPxHeight // ignore: cast_nullable_to_non_nullable
              as int,
      gridPxWidth: null == gridPxWidth
          ? _self.gridPxWidth
          : gridPxWidth // ignore: cast_nullable_to_non_nullable
              as int,
      locationImageSize: null == locationImageSize
          ? _self.locationImageSize
          : locationImageSize // ignore: cast_nullable_to_non_nullable
              as int,
      mapPadding: null == mapPadding
          ? _self.mapPadding
          : mapPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
    ));
  }

  /// Create a copy of Positions2dState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PositionedCard2dCopyWith<$Res>? get hoveredItem {
    if (_self.hoveredItem == null) {
      return null;
    }

    return $PositionedCard2dCopyWith<$Res>(_self.hoveredItem!, (value) {
      return _then(_self.copyWith(hoveredItem: value));
    });
  }
}

/// @nodoc

class _Positions2dState extends Positions2dState {
  const _Positions2dState(
      {this.hoveredItem,
      this.initialFocusCardId,
      final List<PositionedCard2d> positionedCards = const [],
      final List<PositionedGridLine2d> gridLines = const [],
      final List<PositionedLabel2d> labels = const [],
      this.gridPxHeight = 16000,
      this.gridPxWidth = 21200,
      this.locationImageSize = 64,
      this.mapPadding = const EdgeInsets.all(160),
      this.processingStatus = ProcessingStatus.start})
      : _positionedCards = positionedCards,
        _gridLines = gridLines,
        _labels = labels,
        super._();

  @override
  final PositionedCard2d? hoveredItem;
  @override
  final String? initialFocusCardId;
  final List<PositionedCard2d> _positionedCards;
  @override
  @JsonKey()
  List<PositionedCard2d> get positionedCards {
    if (_positionedCards is EqualUnmodifiableListView) return _positionedCards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_positionedCards);
  }

  final List<PositionedGridLine2d> _gridLines;
  @override
  @JsonKey()
  List<PositionedGridLine2d> get gridLines {
    if (_gridLines is EqualUnmodifiableListView) return _gridLines;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_gridLines);
  }

  final List<PositionedLabel2d> _labels;
  @override
  @JsonKey()
  List<PositionedLabel2d> get labels {
    if (_labels is EqualUnmodifiableListView) return _labels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_labels);
  }

  @override
  @JsonKey()
  final int gridPxHeight;
  @override
  @JsonKey()
  final int gridPxWidth;
  @override
  @JsonKey()
  final int locationImageSize;
// @Default(64) int locationIconSize,
  @override
  @JsonKey()
  final EdgeInsets mapPadding;
  @override
  @JsonKey()
  final ProcessingStatus processingStatus;

  /// Create a copy of Positions2dState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$Positions2dStateCopyWith<_Positions2dState> get copyWith =>
      __$Positions2dStateCopyWithImpl<_Positions2dState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Positions2dState &&
            (identical(other.hoveredItem, hoveredItem) ||
                other.hoveredItem == hoveredItem) &&
            (identical(other.initialFocusCardId, initialFocusCardId) ||
                other.initialFocusCardId == initialFocusCardId) &&
            const DeepCollectionEquality()
                .equals(other._positionedCards, _positionedCards) &&
            const DeepCollectionEquality()
                .equals(other._gridLines, _gridLines) &&
            const DeepCollectionEquality().equals(other._labels, _labels) &&
            (identical(other.gridPxHeight, gridPxHeight) ||
                other.gridPxHeight == gridPxHeight) &&
            (identical(other.gridPxWidth, gridPxWidth) ||
                other.gridPxWidth == gridPxWidth) &&
            (identical(other.locationImageSize, locationImageSize) ||
                other.locationImageSize == locationImageSize) &&
            (identical(other.mapPadding, mapPadding) ||
                other.mapPadding == mapPadding) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      hoveredItem,
      initialFocusCardId,
      const DeepCollectionEquality().hash(_positionedCards),
      const DeepCollectionEquality().hash(_gridLines),
      const DeepCollectionEquality().hash(_labels),
      gridPxHeight,
      gridPxWidth,
      locationImageSize,
      mapPadding,
      processingStatus);

  @override
  String toString() {
    return 'Positions2dState(hoveredItem: $hoveredItem, initialFocusCardId: $initialFocusCardId, positionedCards: $positionedCards, gridLines: $gridLines, labels: $labels, gridPxHeight: $gridPxHeight, gridPxWidth: $gridPxWidth, locationImageSize: $locationImageSize, mapPadding: $mapPadding, processingStatus: $processingStatus)';
  }
}

/// @nodoc
abstract mixin class _$Positions2dStateCopyWith<$Res>
    implements $Positions2dStateCopyWith<$Res> {
  factory _$Positions2dStateCopyWith(
          _Positions2dState value, $Res Function(_Positions2dState) _then) =
      __$Positions2dStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {PositionedCard2d? hoveredItem,
      String? initialFocusCardId,
      List<PositionedCard2d> positionedCards,
      List<PositionedGridLine2d> gridLines,
      List<PositionedLabel2d> labels,
      int gridPxHeight,
      int gridPxWidth,
      int locationImageSize,
      EdgeInsets mapPadding,
      ProcessingStatus processingStatus});

  @override
  $PositionedCard2dCopyWith<$Res>? get hoveredItem;
}

/// @nodoc
class __$Positions2dStateCopyWithImpl<$Res>
    implements _$Positions2dStateCopyWith<$Res> {
  __$Positions2dStateCopyWithImpl(this._self, this._then);

  final _Positions2dState _self;
  final $Res Function(_Positions2dState) _then;

  /// Create a copy of Positions2dState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? hoveredItem = freezed,
    Object? initialFocusCardId = freezed,
    Object? positionedCards = null,
    Object? gridLines = null,
    Object? labels = null,
    Object? gridPxHeight = null,
    Object? gridPxWidth = null,
    Object? locationImageSize = null,
    Object? mapPadding = null,
    Object? processingStatus = null,
  }) {
    return _then(_Positions2dState(
      hoveredItem: freezed == hoveredItem
          ? _self.hoveredItem
          : hoveredItem // ignore: cast_nullable_to_non_nullable
              as PositionedCard2d?,
      initialFocusCardId: freezed == initialFocusCardId
          ? _self.initialFocusCardId
          : initialFocusCardId // ignore: cast_nullable_to_non_nullable
              as String?,
      positionedCards: null == positionedCards
          ? _self._positionedCards
          : positionedCards // ignore: cast_nullable_to_non_nullable
              as List<PositionedCard2d>,
      gridLines: null == gridLines
          ? _self._gridLines
          : gridLines // ignore: cast_nullable_to_non_nullable
              as List<PositionedGridLine2d>,
      labels: null == labels
          ? _self._labels
          : labels // ignore: cast_nullable_to_non_nullable
              as List<PositionedLabel2d>,
      gridPxHeight: null == gridPxHeight
          ? _self.gridPxHeight
          : gridPxHeight // ignore: cast_nullable_to_non_nullable
              as int,
      gridPxWidth: null == gridPxWidth
          ? _self.gridPxWidth
          : gridPxWidth // ignore: cast_nullable_to_non_nullable
              as int,
      locationImageSize: null == locationImageSize
          ? _self.locationImageSize
          : locationImageSize // ignore: cast_nullable_to_non_nullable
              as int,
      mapPadding: null == mapPadding
          ? _self.mapPadding
          : mapPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
    ));
  }

  /// Create a copy of Positions2dState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PositionedCard2dCopyWith<$Res>? get hoveredItem {
    if (_self.hoveredItem == null) {
      return null;
    }

    return $PositionedCard2dCopyWith<$Res>(_self.hoveredItem!, (value) {
      return _then(_self.copyWith(hoveredItem: value));
    });
  }
}

// dart format on
