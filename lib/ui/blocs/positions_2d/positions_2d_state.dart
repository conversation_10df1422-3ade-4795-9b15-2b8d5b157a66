import 'package:collection/collection.dart';
import 'package:dauntless/models/base/mapping/positioned_card_2d.dart';
import 'package:dauntless/models/base/mapping/positioned_grid_line_2d.dart';
import 'package:dauntless/models/base/mapping/positioned_label_2d.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter/cupertino.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'positions_2d_state.freezed.dart';

@Freezed(fromJson: false, toJson: false)
abstract class Positions2dState with _$Positions2dState {
  //}<T extends GameCard> with _$Positions2dState<T> {
  //extends Equatable { // TODO: add equatable? Figure out the right freezed-way to handle this? something else for a generic?

  const Positions2dState._();

  PositionedCard2d? get initialFocusCard => positionedCards.isEmpty
      ? null
      : positionedCards.firstWhere(
          (element) => element.card.id == initialFocusCardId,
          orElse: () => positionedCards.first,
        );

  const factory Positions2dState({
    PositionedCard2d? hoveredItem,
    String? initialFocusCardId,
    @Default([]) List<PositionedCard2d> positionedCards,
    @Default([]) List<PositionedGridLine2d> gridLines,
    @Default([]) List<PositionedLabel2d> labels,
    @Default(16000) int gridPxHeight,
    @Default(21200) int gridPxWidth,
    @Default(64) int locationImageSize,
    // @Default(64) int locationIconSize,
    @Default(EdgeInsets.all(160)) EdgeInsets mapPadding,
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
    // GameCardId? listCardId,
    // T? selectedObject,
    // bool Function(T)? objectViewCondition,
    // @Default(Positions2dViewType.list) Positions2dViewType viewType,
    // @Default(
    //     Positions2dViewBuildOption.children) Positions2dViewBuildOption buildType,
    // @Default([]) List<CardGrouping> groupings,
    // T? hoverObject,
  }) = _Positions2dState;
}
