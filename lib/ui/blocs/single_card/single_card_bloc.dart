import 'dart:math';

import 'package:collection/collection.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/use_cases/actions_use_case.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'single_card_state.dart';

abstract class SingleCardEvent {}

class LoadCardChildrenEvent extends SingleCardEvent {}

class RefreshCardEvent extends SingleCardEvent {
  final GameCard? card;

  RefreshCardEvent({this.card});
}

class LoadCardActionsEvent extends SingleCardEvent {
  final Map<String, List<GameCard>> children;

  LoadCardActionsEvent(this.children);
}

class SingleCardBloc extends Bloc<SingleCardEvent, SingleCardState>
    with BlocName<SingleCardState> {
  @override
  String get name => '$runtimeType—${state.card?.id}';

  final GameMatchManager _gameMatchManager;
  final ActionsUseCase _actionsUseCase;
  final ActionsGroupingsUseCase _actionsGroupingsUseCase;
  final PlayersUseCase _playersUseCase;

  SingleCardBloc(GameCard card, this._gameMatchManager, this._actionsUseCase,
      this._actionsGroupingsUseCase, this._playersUseCase)
      : super(SingleCardState(
          id: card.id,
          card: card.copyWith(),
          cardClass: _gameMatchManager.state.getCardClassForId(card.classId),
          // children: _matchManager.state.getCardsForLocation(card.id)
        )) {
    on<RefreshCardEvent>(_onRefreshCardEvent);
    on<LoadCardChildrenEvent>(_onLoadCardChildrenEvent);
    on<LoadCardActionsEvent>(_onLoadCardActionsEvent);
  }

  void _onRefreshCardEvent(
      RefreshCardEvent event, Emitter<SingleCardState> emit) {
    emit(state.copyWith(status: ProcessingStatus.loading));
    final card = event.card ?? _gameMatchManager.state.getCardForId(state.id).$2;
    emit(state.copyWith(card: card, status: ProcessingStatus.loaded));
  }

  void _onLoadCardChildrenEvent(
      LoadCardChildrenEvent event, Emitter<SingleCardState> emit) {
    if (state.card == null) {
      return;
    }
    final children = _gameMatchManager.state
        .getCardsAndClassesForLocation(state.card!.id, getGroupChildren: true);

    final reconciledAttributes =
        children[_gameMatchManager.state.userPlayerId]?.getReconciledAttributes() ??
            {};

    emit(state.copyWith(
      children: children,
      reconciledAttributes: reconciledAttributes,
    ));
  }

  void _onLoadCardActionsEvent(
      LoadCardActionsEvent event, Emitter<SingleCardState> emit) async {
    final List<GameCardId> idsForGroupings = [];
    if (state.card?.type == CardType.grouping) {
      idsForGroupings.addAll(event.children[_gameMatchManager.state.userPlayerId]
              ?.map((child) => child.classId)
              .toList() ??
          []);
    } else {
      if (_gameMatchManager.state.getCardForId(state.card!.id).$1 ==
          _gameMatchManager.state.userPlayerId) {
        idsForGroupings.add(state.card!.classId);
      }
    }
    final actionIdsForCard =
        _actionsGroupingsUseCase.getGroupingIdsByIds(idsForGroupings);

    final actionGroupings =
        _actionsGroupingsUseCase.getGroupingsByIds(idsForGroupings);

    final actionsForCard = _actionsUseCase.objects
        .where((action) => actionIdsForCard.contains(action.id))
        .toList();

    // TODO: handle this filtering at a use case level?
    if (state.card?.type == CardType.grouping) {
      actionsForCard.removeWhere((action) =>
          action.subjectFilters.contains(ActionSubjectFilter.notGrouping));
    }

    /// *******************
    /// IMPORTANT: TODO: figure out how to properly handle resources, turn-based, how to define these/ handle them
    /// Resource Types:
    /// - currency-type things: can be stored, spent, earned, etc.
    /// - labor-type things: cannot be stored, but can be spent; can be used as earned
    /// *******************

    /// add any defined target actions
    final unTargetedActions = <CardAction>[];
    final actionsWithPredefinedTargets = <TargetedAction>[];

    for (final action in actionsForCard) {
      final grouping = actionGroupings
          .firstWhereOrNull((grouping) => grouping.groupingId == action.id);

      if (grouping?.attributes['targetClasses'] != null &&
          grouping?.attributes['targetClasses'] is List) {
        for (final targetClassEntry in grouping!.attributes['targetClasses']) {
          if (targetClassEntry is Map && targetClassEntry['class'] != null) {
            final objectCardClass = _gameMatchManager.state
                .getCardClassForId(targetClassEntry['class']);
            final objectRelevantAttributes =
                Map<String, dynamic>.from(objectCardClass?.attributes ?? {});
            objectRelevantAttributes.removeWhere(
                (key, value) => !action.targetRelevantAttributes.contains(key));

            const suppliedResourcesKey = 'suppliedResources';

            final Map<String, dynamic> subjectSuppliedResourcesAttributes =
                (state.cardClass?.attributes[suppliedResourcesKey]) ?? {};

            final Map<String, dynamic> locallyReconciledPerTurnResources = {};

            for (final key in subjectSuppliedResourcesAttributes.keys) {
              for (final objectRelevantAttributeCategoryKey
                  in objectRelevantAttributes.keys) {
                if (objectRelevantAttributes
                    .containsKey(objectRelevantAttributeCategoryKey)) {
                  for (final objectRelevantAttributeKey
                      in objectRelevantAttributes[
                              objectRelevantAttributeCategoryKey]
                          .keys) {
                    if (objectRelevantAttributes[
                            objectRelevantAttributeCategoryKey]
                        .containsKey(objectRelevantAttributeKey)) {
                      final valueRequired = objectRelevantAttributes[
                              objectRelevantAttributeCategoryKey]
                          [objectRelevantAttributeKey];
                      final valueSupplied =
                          subjectSuppliedResourcesAttributes[key];
                      // TODO: how to we best ensure type safety?
                      locallyReconciledPerTurnResources[key] =
                          (valueRequired / valueSupplied).ceil();
                    }
                  }
                }
              }
            }

            final targetedActionAttributes = <String, dynamic>{
              ...objectRelevantAttributes,
              suppliedResourcesKey: subjectSuppliedResourcesAttributes,
              'locallyReconciledPerTurnResources':
                  locallyReconciledPerTurnResources,
            };

            // TODO: better define attribute reconciliation
            final remainingTurns = locallyReconciledPerTurnResources.values
                .map((value) => value as int)
                .reduce((value, element) => max(value, element));

            final targetedAction = TargetedAction.genId(
              action: action,
              subjectCardId: state.card!.id,
              subjectLocationId: state.card!.locationId,
              objectCardClassId: objectCardClass?.id,
              reconciledAttributes: targetedActionAttributes,
              remainingTurns: remainingTurns,
            );
            actionsWithPredefinedTargets.add(targetedAction);
          }
        }
      } else {
        unTargetedActions.add(action);
      }
    }

    emit(state.copyWith(
        actions: unTargetedActions,
        targetedActions: actionsWithPredefinedTargets
    ));
  }
}
