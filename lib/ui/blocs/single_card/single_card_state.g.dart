// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'single_card_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SingleCardState _$SingleCardStateFromJson(Map<String, dynamic> json) =>
    _SingleCardState(
      id: json['id'] as String,
      card: json['card'] == null
          ? null
          : GameCard.fromJson(json['card'] as Map<String, dynamic>),
      cardClass: json['cardClass'] == null
          ? null
          : GameCardClass.fromJson(json['cardClass'] as Map<String, dynamic>),
      status: $enumDecodeNullable(_$ProcessingStatusEnumMap, json['status']) ??
          ProcessingStatus.start,
      children: (json['children'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                k,
                (e as List<dynamic>)
                    .map((e) => _$recordConvert(
                          e,
                          ($jsonValue) => (
                            GameCard.fromJson(
                                $jsonValue[r'$1'] as Map<String, dynamic>),
                            $jsonValue[r'$2'] == null
                                ? null
                                : GameCardClass.fromJson(
                                    $jsonValue[r'$2'] as Map<String, dynamic>),
                          ),
                        ))
                    .toList()),
          ) ??
          const {},
      reconciledAttributes:
          json['reconciledAttributes'] as Map<String, dynamic>? ?? const {},
      actions: (json['actions'] as List<dynamic>?)
              ?.map((e) => CardAction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      targetedActions: (json['targetedActions'] as List<dynamic>?)
              ?.map((e) => TargetedAction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$SingleCardStateToJson(_SingleCardState instance) =>
    <String, dynamic>{
      'id': instance.id,
      'card': instance.card,
      'cardClass': instance.cardClass,
      'status': _$ProcessingStatusEnumMap[instance.status]!,
      'children': instance.children.map((k, e) => MapEntry(
          k,
          e
              .map((e) => <String, dynamic>{
                    r'$1': e.$1,
                    r'$2': e.$2,
                  })
              .toList())),
      'reconciledAttributes': instance.reconciledAttributes,
      'actions': instance.actions,
      'targetedActions': instance.targetedActions,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};

$Rec _$recordConvert<$Rec>(
  Object? value,
  $Rec Function(Map) convert,
) =>
    convert(value as Map<String, dynamic>);
