import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/models/base/game_card.dart';

part 'single_card_state.freezed.dart';

part 'single_card_state.g.dart';

@freezed
abstract class SingleCardState with _$SingleCardState {
  const SingleCardState._();

  /// all groups, and all children not in a group
  Map<PlayerId, List<(GameCard, GameCardClass?)>> get uniqueChildren => children.map((key, value) => MapEntry(key, value.uniqueChildren));

  List<PlayerId> get vehicleChildrenPlayerIds => children.entries
      .where((element) => element.value.any((card) => card.$1.type == CardType.vehicle))
      .map((e) => e.key)
      .toList();

  List<PlayerId> get buildingChildrenPlayerIds => children.entries
      .where((element) => element.value.any((card) => card.$1.type == CardType.building))
      .map((e) => e.key)
      .toList();

  int get energySlots => int.parse(cardClass?.attributes['energySlots'] ?? '0');

  int get usedEnergy => reconciledAttributes['energyUsed'] ?? 0;//int.parse(reconciledAttributes['energyUsed'] ?? '0');

  int get remainingEnergy => (energySlots - usedEnergy) < 0 ? 0 : energySlots - usedEnergy;

  const factory SingleCardState({
    required GameCardId id,
    GameCard? card,
    GameCardClass? cardClass,
    @Default(ProcessingStatus.start) ProcessingStatus status,
    @Default({}) Map<PlayerId, List<(GameCard, GameCardClass?)>> children,
    @Default({}) Map<String, dynamic> reconciledAttributes,
    @Default([]) List<CardAction> actions,
    @Default([]) List<TargetedAction> targetedActions,
  }) = _SingleCardState;

  factory SingleCardState.fromJson(Map<String, dynamic> json) =>
      _$SingleCardStateFromJson(json);

}
