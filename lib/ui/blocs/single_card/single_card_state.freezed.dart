// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'single_card_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SingleCardState {
  GameCardId get id;
  GameCard? get card;
  GameCardClass? get cardClass;
  ProcessingStatus get status;
  Map<PlayerId, List<(GameCard, GameCardClass?)>> get children;
  Map<String, dynamic> get reconciledAttributes;
  List<CardAction> get actions;
  List<TargetedAction> get targetedActions;

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SingleCardStateCopyWith<SingleCardState> get copyWith =>
      _$SingleCardStateCopyWithImpl<SingleCardState>(
          this as SingleCardState, _$identity);

  /// Serializes this SingleCardState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SingleCardState &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.card, card) || other.card == card) &&
            (identical(other.cardClass, cardClass) ||
                other.cardClass == cardClass) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other.children, children) &&
            const DeepCollectionEquality()
                .equals(other.reconciledAttributes, reconciledAttributes) &&
            const DeepCollectionEquality().equals(other.actions, actions) &&
            const DeepCollectionEquality()
                .equals(other.targetedActions, targetedActions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      card,
      cardClass,
      status,
      const DeepCollectionEquality().hash(children),
      const DeepCollectionEquality().hash(reconciledAttributes),
      const DeepCollectionEquality().hash(actions),
      const DeepCollectionEquality().hash(targetedActions));

  @override
  String toString() {
    return 'SingleCardState(id: $id, card: $card, cardClass: $cardClass, status: $status, children: $children, reconciledAttributes: $reconciledAttributes, actions: $actions, targetedActions: $targetedActions)';
  }
}

/// @nodoc
abstract mixin class $SingleCardStateCopyWith<$Res> {
  factory $SingleCardStateCopyWith(
          SingleCardState value, $Res Function(SingleCardState) _then) =
      _$SingleCardStateCopyWithImpl;
  @useResult
  $Res call(
      {GameCardId id,
      GameCard? card,
      GameCardClass? cardClass,
      ProcessingStatus status,
      Map<PlayerId, List<(GameCard, GameCardClass?)>> children,
      Map<String, dynamic> reconciledAttributes,
      List<CardAction> actions,
      List<TargetedAction> targetedActions});

  $GameCardCopyWith<$Res>? get card;
  $GameCardClassCopyWith<$Res>? get cardClass;
}

/// @nodoc
class _$SingleCardStateCopyWithImpl<$Res>
    implements $SingleCardStateCopyWith<$Res> {
  _$SingleCardStateCopyWithImpl(this._self, this._then);

  final SingleCardState _self;
  final $Res Function(SingleCardState) _then;

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? card = freezed,
    Object? cardClass = freezed,
    Object? status = null,
    Object? children = null,
    Object? reconciledAttributes = null,
    Object? actions = null,
    Object? targetedActions = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      card: freezed == card
          ? _self.card
          : card // ignore: cast_nullable_to_non_nullable
              as GameCard?,
      cardClass: freezed == cardClass
          ? _self.cardClass
          : cardClass // ignore: cast_nullable_to_non_nullable
              as GameCardClass?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      children: null == children
          ? _self.children
          : children // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, List<(GameCard, GameCardClass?)>>,
      reconciledAttributes: null == reconciledAttributes
          ? _self.reconciledAttributes
          : reconciledAttributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      actions: null == actions
          ? _self.actions
          : actions // ignore: cast_nullable_to_non_nullable
              as List<CardAction>,
      targetedActions: null == targetedActions
          ? _self.targetedActions
          : targetedActions // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
    ));
  }

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardCopyWith<$Res>? get card {
    if (_self.card == null) {
      return null;
    }

    return $GameCardCopyWith<$Res>(_self.card!, (value) {
      return _then(_self.copyWith(card: value));
    });
  }

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardClassCopyWith<$Res>? get cardClass {
    if (_self.cardClass == null) {
      return null;
    }

    return $GameCardClassCopyWith<$Res>(_self.cardClass!, (value) {
      return _then(_self.copyWith(cardClass: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _SingleCardState extends SingleCardState {
  const _SingleCardState(
      {required this.id,
      this.card,
      this.cardClass,
      this.status = ProcessingStatus.start,
      final Map<PlayerId, List<(GameCard, GameCardClass?)>> children = const {},
      final Map<String, dynamic> reconciledAttributes = const {},
      final List<CardAction> actions = const [],
      final List<TargetedAction> targetedActions = const []})
      : _children = children,
        _reconciledAttributes = reconciledAttributes,
        _actions = actions,
        _targetedActions = targetedActions,
        super._();
  factory _SingleCardState.fromJson(Map<String, dynamic> json) =>
      _$SingleCardStateFromJson(json);

  @override
  final GameCardId id;
  @override
  final GameCard? card;
  @override
  final GameCardClass? cardClass;
  @override
  @JsonKey()
  final ProcessingStatus status;
  final Map<PlayerId, List<(GameCard, GameCardClass?)>> _children;
  @override
  @JsonKey()
  Map<PlayerId, List<(GameCard, GameCardClass?)>> get children {
    if (_children is EqualUnmodifiableMapView) return _children;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_children);
  }

  final Map<String, dynamic> _reconciledAttributes;
  @override
  @JsonKey()
  Map<String, dynamic> get reconciledAttributes {
    if (_reconciledAttributes is EqualUnmodifiableMapView)
      return _reconciledAttributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_reconciledAttributes);
  }

  final List<CardAction> _actions;
  @override
  @JsonKey()
  List<CardAction> get actions {
    if (_actions is EqualUnmodifiableListView) return _actions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_actions);
  }

  final List<TargetedAction> _targetedActions;
  @override
  @JsonKey()
  List<TargetedAction> get targetedActions {
    if (_targetedActions is EqualUnmodifiableListView) return _targetedActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_targetedActions);
  }

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SingleCardStateCopyWith<_SingleCardState> get copyWith =>
      __$SingleCardStateCopyWithImpl<_SingleCardState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SingleCardStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SingleCardState &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.card, card) || other.card == card) &&
            (identical(other.cardClass, cardClass) ||
                other.cardClass == cardClass) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._children, _children) &&
            const DeepCollectionEquality()
                .equals(other._reconciledAttributes, _reconciledAttributes) &&
            const DeepCollectionEquality().equals(other._actions, _actions) &&
            const DeepCollectionEquality()
                .equals(other._targetedActions, _targetedActions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      card,
      cardClass,
      status,
      const DeepCollectionEquality().hash(_children),
      const DeepCollectionEquality().hash(_reconciledAttributes),
      const DeepCollectionEquality().hash(_actions),
      const DeepCollectionEquality().hash(_targetedActions));

  @override
  String toString() {
    return 'SingleCardState(id: $id, card: $card, cardClass: $cardClass, status: $status, children: $children, reconciledAttributes: $reconciledAttributes, actions: $actions, targetedActions: $targetedActions)';
  }
}

/// @nodoc
abstract mixin class _$SingleCardStateCopyWith<$Res>
    implements $SingleCardStateCopyWith<$Res> {
  factory _$SingleCardStateCopyWith(
          _SingleCardState value, $Res Function(_SingleCardState) _then) =
      __$SingleCardStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {GameCardId id,
      GameCard? card,
      GameCardClass? cardClass,
      ProcessingStatus status,
      Map<PlayerId, List<(GameCard, GameCardClass?)>> children,
      Map<String, dynamic> reconciledAttributes,
      List<CardAction> actions,
      List<TargetedAction> targetedActions});

  @override
  $GameCardCopyWith<$Res>? get card;
  @override
  $GameCardClassCopyWith<$Res>? get cardClass;
}

/// @nodoc
class __$SingleCardStateCopyWithImpl<$Res>
    implements _$SingleCardStateCopyWith<$Res> {
  __$SingleCardStateCopyWithImpl(this._self, this._then);

  final _SingleCardState _self;
  final $Res Function(_SingleCardState) _then;

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? card = freezed,
    Object? cardClass = freezed,
    Object? status = null,
    Object? children = null,
    Object? reconciledAttributes = null,
    Object? actions = null,
    Object? targetedActions = null,
  }) {
    return _then(_SingleCardState(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as GameCardId,
      card: freezed == card
          ? _self.card
          : card // ignore: cast_nullable_to_non_nullable
              as GameCard?,
      cardClass: freezed == cardClass
          ? _self.cardClass
          : cardClass // ignore: cast_nullable_to_non_nullable
              as GameCardClass?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      children: null == children
          ? _self._children
          : children // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, List<(GameCard, GameCardClass?)>>,
      reconciledAttributes: null == reconciledAttributes
          ? _self._reconciledAttributes
          : reconciledAttributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      actions: null == actions
          ? _self._actions
          : actions // ignore: cast_nullable_to_non_nullable
              as List<CardAction>,
      targetedActions: null == targetedActions
          ? _self._targetedActions
          : targetedActions // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
    ));
  }

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardCopyWith<$Res>? get card {
    if (_self.card == null) {
      return null;
    }

    return $GameCardCopyWith<$Res>(_self.card!, (value) {
      return _then(_self.copyWith(card: value));
    });
  }

  /// Create a copy of SingleCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameCardClassCopyWith<$Res>? get cardClass {
    if (_self.cardClass == null) {
      return null;
    }

    return $GameCardClassCopyWith<$Res>(_self.cardClass!, (value) {
      return _then(_self.copyWith(cardClass: value));
    });
  }
}

// dart format on
