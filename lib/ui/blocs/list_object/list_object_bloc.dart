import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/views/list_object_view.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'list_object_event.dart';
import 'list_object_state.dart';

// abstract class ListObjectHook<T> {
//   // void onInitListObject(ListObjectState<T> state);
//   // void onSetListViewType(ListObjectState<T> state);
//   void onSelectListObject(ListObjectState<T> state);
// }

class ListObjectHook<T extends GameCard, ListObjectEvent> {
  void Function(ListObjectEvent event, ListObjectState<T> state) onEvent;

  ListObjectHook(this.onEvent);
}

class ListObjectBloc<T extends GameCard>
    extends Bloc<ListObjectEvent<T>, ListObjectState<T>> {
  final ListObjectUseCase<T> _listObjectUseCase;
  final GroupingsUseCase? _groupingsUseCase;
  final List<ListObjectHook<T, ListObjectEvent>> _hooks;

  ListObjectBloc(this._listObjectUseCase, this._hooks,
      {List<T> startingListObject = const [],
        GameCardId? listCardId,
      GroupingsUseCase? groupingsUseCase})
      : _groupingsUseCase = groupingsUseCase,
        super(ListObjectState(
          list: startingListObject,
          listCardId: listCardId
      )) {
    on<InitListObjectEvent<T>>(_onInitListObjectEvent);
    on<SetListViewTypeEvent<T>>(_onSetListViewTypeEvent);
    on<SelectListObjectEvent<T>>(_onSelectListObjectEvent);
    on<HoverListObjectEvent<T>>(_onHoverListObjectEvent);
  }

  void _onInitListObjectEvent(
      InitListObjectEvent<T> event, Emitter<ListObjectState<T>> emit) async {
    emit(state.copyWith(
        buildType: event.buildType ?? state.buildType,
        // objectViewCondition: event.objectViewCondition
    ));
    await _listObjectUseCase.init();
    await _groupingsUseCase?.init();

    final groupingIds = state.buildType == ListObjectViewBuildOption.groupings ? _groupingsUseCase?.allGroupingIds
        : _groupingsUseCase?.getIdsByGroupingIds([state.listCardId ?? '']);
    // _groupingsUseCase?.objects.map((grouping) => grouping.id)
    //     .toSet();
    final groupingList =
        // state.buildType == ListObjectViewBuildOption.groupings ? _groupingsUseCase?.objects :
    groupingIds == null
        ? null
        : _listObjectUseCase.objects
            .where((object) => groupingIds.contains(object.id))
            .toList();

    emit(state.copyWith(
        list: groupingList ?? _listObjectUseCase.objects,
        // list: state.buildType == ListObjectViewBuildOption.groupings
        //     ? groupingList : _listObjectUseCase.objects,
        groupings: _groupingsUseCase?.objects ?? [],
    ));
  }

  void _onSetListViewTypeEvent(
      SetListViewTypeEvent event, Emitter<ListObjectState<T>> emit) {
    emit(state.copyWith(viewType: event.viewType));
  }

  void _onSelectListObjectEvent(
      SelectListObjectEvent<T> event, Emitter<ListObjectState<T>> emit) {
    final selectedObject =
        state.selectedObject?.id == event.object.id ? null : event.object;
    if (selectedObject != null) {
      _listObjectUseCase.selectObject(selectedObject);
    }
    // _groupingsUseCase?.setSelectedGroupingIds(selectedObject?.id == null ? [] : [selectedObject!.id]);

    for (var hook in _hooks) {
      if (hook is ListObjectHook<T, SelectListObjectEvent<T>>) {
        hook.onEvent(event, state);
      }
    }
    emit(state.copyWith(
        // selectedObject: selectedObject,
        groupings: _groupingsUseCase?.getGroupingsByIds([selectedObject?.id ?? '']) ?? []));
  }

  void _onHoverListObjectEvent(
      HoverListObjectEvent<T> event, Emitter<ListObjectState<T>> emit) {
    emit(state.copyWith(hoverObject: event.object));
  }
}
