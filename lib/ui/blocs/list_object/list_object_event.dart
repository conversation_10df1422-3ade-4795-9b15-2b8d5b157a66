import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/views/list_object_view.dart';

import 'list_object_state.dart';

abstract class ListObjectEvent<T extends GameCard> {}

class InitListObjectEvent<T extends GameCard> extends ListObjectEvent<T> {
  final bool Function(T)? objectViewCondition;
  final ListObjectViewBuildOption? buildType;

  InitListObjectEvent(this.objectViewCondition, {this.buildType});
}

class SetListViewTypeEvent<T extends GameCard> extends ListObjectEvent<T> {
  final ListObjectViewType viewType;

  SetListViewTypeEvent(this.viewType);
}

class SelectListObjectEvent<T extends GameCard> extends ListObjectEvent<T> {
  final T object;

  SelectListObjectEvent(this.object);
}
class HoverListObjectEvent<T extends GameCard> extends ListObjectEvent<T> {
  final T? object;

  HoverListObjectEvent(this.object);
}