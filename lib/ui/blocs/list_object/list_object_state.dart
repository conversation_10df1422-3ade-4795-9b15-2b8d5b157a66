
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/views/list_object_view.dart';

part 'list_object_state.freezed.dart';

enum ListObjectViewType {
  list,
  grid,
}

@Freezed(fromJson: false, toJson: false)
abstract class ListObjectState<T extends GameCard> with _$ListObjectState<T> {
  //extends Equatable { // TODO: add equatable? Figure out the right freezed-way to handle this? something else for a generic?

  const ListObjectState._();

  const factory ListObjectState({
    @Default([]) List<T> list,
    GameCardId? listCardId,
    T? selectedObject,
    // bool Function(T)? objectViewCondition,
    @Default(ListObjectViewType.list) ListObjectViewType viewType,
    @Default(
        ListObjectViewBuildOption.children) ListObjectViewBuildOption buildType,
    @Default([]) List<CardGrouping> groupings,
    T? hoverObject,
  }) = _ListObjectState<T>;

}

// final GameCardId? listCardId;
// final List<T> _list;
// final List<CardGrouping> groupings;
// final ListObjectViewType viewType;
// final ListObjectViewBuildOption buildType;
// final T? selectedObject;
// @Deprecated('not used/well tested')
// final bool Function(T)? objectViewCondition;
// final T? hoverObject;

// List<T> get list {
//   if (objectViewCondition == null) {
//     return _list;
//   }
//   return _list.where(objectViewCondition!).toList();
// }

// const ListObjectState({
//   // this.list = const [],
//   this.listCardId,
//   List<T> list = const [],
//   this.viewType = ListObjectViewType.list,
//   this.buildType = ListObjectViewBuildOption.children,
//   this.selectedObject,
//   this.objectViewCondition,
//   this.groupings = const [],
//   this.hoverObject,
// }) : _list = list;
//
// // copywith
// ListObjectState<T> copyWith({
//   List<T>? list,
//   GameCardId? listCardId,
//   ListObjectViewType? viewType,
//   ListObjectViewBuildOption? buildType,
//   T? selectedObject,
//   bool Function(T)? objectViewCondition,
//   List<CardGrouping>? groupings,
//   T? hoverObject,
// }) {
//   return ListObjectState<T>(
//     list: list ?? this.list,
//     listCardId: listCardId ?? this.listCardId,
//     viewType: viewType ?? this.viewType,
//     buildType: buildType ?? this.buildType,
//     selectedObject: selectedObject ?? this.selectedObject,
//     objectViewCondition: objectViewCondition ?? this.objectViewCondition,
//     groupings: groupings ?? this.groupings,
//     hoverObject: hoverObject ?? this.hoverObject,
//   );
// }
// factory ListObjectState.fromJson(Map<String, dynamic> json) => _$ListObjectStateFromJson(json);
// }

// @freezed
// class ListObjectState<T> with _$ListObjectState {
//   const factory ListObjectState({
//     required List<T> list, // T => ListObject?
//     @Default(ListObjectViewType.list) ListObjectViewType viewType,
//   }) = _ListObjectState;
//
//   factory ListObjectState.fromJson(Map<String, dynamic> json) => _$ListObjectStateFromJson(json);
// }

// @freezed
// class ListObjectState<T> {
//   final List<T> _list;
//
//   List<T> get list => _list;
//
//   const ListObjectState(this._list);
//
//   ListObjectState<T> copyWith({
//     List<T>? list,
//   }) {
//     return ListObjectState<T>( list ?? this._list,
//     );
//   }
// }


// import 'package:dauntless/models/abstract/list_object.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';
//
// part 'list_object_state.freezed.dart';
// part 'list_object_state.g.dart';

// @freezed
// class ListObjectState<T> with _$ListObjectState {
//   const factory ListObjectState({
//     required List<T> object, // T => ListObject?
//   }) = _ListObjectState;
//
//   factory ListObjectState.fromJson(Map<String, dynamic> json) => _$ListObjectStateFromJson(json);
// }


// class GameCard<T extends GameCardType> with _$GameCard {

// @JsonSerializable(genericArgumentFactories: true)
