// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'list_object_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ListObjectState<T extends GameCard> {
  List<T> get list;
  GameCardId? get listCardId;
  T? get selectedObject; // bool Function(T)? objectViewCondition,
  ListObjectViewType get viewType;
  ListObjectViewBuildOption get buildType;
  List<CardGrouping> get groupings;
  T? get hoverObject;

  /// Create a copy of ListObjectState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ListObjectStateCopyWith<T, ListObjectState<T>> get copyWith =>
      _$ListObjectStateCopyWithImpl<T, ListObjectState<T>>(
          this as ListObjectState<T>, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ListObjectState<T> &&
            const DeepCollectionEquality().equals(other.list, list) &&
            (identical(other.listCardId, listCardId) ||
                other.listCardId == listCardId) &&
            const DeepCollectionEquality()
                .equals(other.selectedObject, selectedObject) &&
            (identical(other.viewType, viewType) ||
                other.viewType == viewType) &&
            (identical(other.buildType, buildType) ||
                other.buildType == buildType) &&
            const DeepCollectionEquality().equals(other.groupings, groupings) &&
            const DeepCollectionEquality()
                .equals(other.hoverObject, hoverObject));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(list),
      listCardId,
      const DeepCollectionEquality().hash(selectedObject),
      viewType,
      buildType,
      const DeepCollectionEquality().hash(groupings),
      const DeepCollectionEquality().hash(hoverObject));

  @override
  String toString() {
    return 'ListObjectState<$T>(list: $list, listCardId: $listCardId, selectedObject: $selectedObject, viewType: $viewType, buildType: $buildType, groupings: $groupings, hoverObject: $hoverObject)';
  }
}

/// @nodoc
abstract mixin class $ListObjectStateCopyWith<T extends GameCard, $Res> {
  factory $ListObjectStateCopyWith(
          ListObjectState<T> value, $Res Function(ListObjectState<T>) _then) =
      _$ListObjectStateCopyWithImpl;
  @useResult
  $Res call(
      {List<T> list,
      GameCardId? listCardId,
      T? selectedObject,
      ListObjectViewType viewType,
      ListObjectViewBuildOption buildType,
      List<CardGrouping> groupings,
      T? hoverObject});
}

/// @nodoc
class _$ListObjectStateCopyWithImpl<T extends GameCard, $Res>
    implements $ListObjectStateCopyWith<T, $Res> {
  _$ListObjectStateCopyWithImpl(this._self, this._then);

  final ListObjectState<T> _self;
  final $Res Function(ListObjectState<T>) _then;

  /// Create a copy of ListObjectState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = null,
    Object? listCardId = freezed,
    Object? selectedObject = freezed,
    Object? viewType = null,
    Object? buildType = null,
    Object? groupings = null,
    Object? hoverObject = freezed,
  }) {
    return _then(_self.copyWith(
      list: null == list
          ? _self.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<T>,
      listCardId: freezed == listCardId
          ? _self.listCardId
          : listCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      selectedObject: freezed == selectedObject
          ? _self.selectedObject
          : selectedObject // ignore: cast_nullable_to_non_nullable
              as T?,
      viewType: null == viewType
          ? _self.viewType
          : viewType // ignore: cast_nullable_to_non_nullable
              as ListObjectViewType,
      buildType: null == buildType
          ? _self.buildType
          : buildType // ignore: cast_nullable_to_non_nullable
              as ListObjectViewBuildOption,
      groupings: null == groupings
          ? _self.groupings
          : groupings // ignore: cast_nullable_to_non_nullable
              as List<CardGrouping>,
      hoverObject: freezed == hoverObject
          ? _self.hoverObject
          : hoverObject // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

/// @nodoc

class _ListObjectState<T extends GameCard> extends ListObjectState<T> {
  const _ListObjectState(
      {final List<T> list = const [],
      this.listCardId,
      this.selectedObject,
      this.viewType = ListObjectViewType.list,
      this.buildType = ListObjectViewBuildOption.children,
      final List<CardGrouping> groupings = const [],
      this.hoverObject})
      : _list = list,
        _groupings = groupings,
        super._();

  final List<T> _list;
  @override
  @JsonKey()
  List<T> get list {
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_list);
  }

  @override
  final GameCardId? listCardId;
  @override
  final T? selectedObject;
// bool Function(T)? objectViewCondition,
  @override
  @JsonKey()
  final ListObjectViewType viewType;
  @override
  @JsonKey()
  final ListObjectViewBuildOption buildType;
  final List<CardGrouping> _groupings;
  @override
  @JsonKey()
  List<CardGrouping> get groupings {
    if (_groupings is EqualUnmodifiableListView) return _groupings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_groupings);
  }

  @override
  final T? hoverObject;

  /// Create a copy of ListObjectState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ListObjectStateCopyWith<T, _ListObjectState<T>> get copyWith =>
      __$ListObjectStateCopyWithImpl<T, _ListObjectState<T>>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ListObjectState<T> &&
            const DeepCollectionEquality().equals(other._list, _list) &&
            (identical(other.listCardId, listCardId) ||
                other.listCardId == listCardId) &&
            const DeepCollectionEquality()
                .equals(other.selectedObject, selectedObject) &&
            (identical(other.viewType, viewType) ||
                other.viewType == viewType) &&
            (identical(other.buildType, buildType) ||
                other.buildType == buildType) &&
            const DeepCollectionEquality()
                .equals(other._groupings, _groupings) &&
            const DeepCollectionEquality()
                .equals(other.hoverObject, hoverObject));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_list),
      listCardId,
      const DeepCollectionEquality().hash(selectedObject),
      viewType,
      buildType,
      const DeepCollectionEquality().hash(_groupings),
      const DeepCollectionEquality().hash(hoverObject));

  @override
  String toString() {
    return 'ListObjectState<$T>(list: $list, listCardId: $listCardId, selectedObject: $selectedObject, viewType: $viewType, buildType: $buildType, groupings: $groupings, hoverObject: $hoverObject)';
  }
}

/// @nodoc
abstract mixin class _$ListObjectStateCopyWith<T extends GameCard, $Res>
    implements $ListObjectStateCopyWith<T, $Res> {
  factory _$ListObjectStateCopyWith(
          _ListObjectState<T> value, $Res Function(_ListObjectState<T>) _then) =
      __$ListObjectStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<T> list,
      GameCardId? listCardId,
      T? selectedObject,
      ListObjectViewType viewType,
      ListObjectViewBuildOption buildType,
      List<CardGrouping> groupings,
      T? hoverObject});
}

/// @nodoc
class __$ListObjectStateCopyWithImpl<T extends GameCard, $Res>
    implements _$ListObjectStateCopyWith<T, $Res> {
  __$ListObjectStateCopyWithImpl(this._self, this._then);

  final _ListObjectState<T> _self;
  final $Res Function(_ListObjectState<T>) _then;

  /// Create a copy of ListObjectState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? list = null,
    Object? listCardId = freezed,
    Object? selectedObject = freezed,
    Object? viewType = null,
    Object? buildType = null,
    Object? groupings = null,
    Object? hoverObject = freezed,
  }) {
    return _then(_ListObjectState<T>(
      list: null == list
          ? _self._list
          : list // ignore: cast_nullable_to_non_nullable
              as List<T>,
      listCardId: freezed == listCardId
          ? _self.listCardId
          : listCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      selectedObject: freezed == selectedObject
          ? _self.selectedObject
          : selectedObject // ignore: cast_nullable_to_non_nullable
              as T?,
      viewType: null == viewType
          ? _self.viewType
          : viewType // ignore: cast_nullable_to_non_nullable
              as ListObjectViewType,
      buildType: null == buildType
          ? _self.buildType
          : buildType // ignore: cast_nullable_to_non_nullable
              as ListObjectViewBuildOption,
      groupings: null == groupings
          ? _self._groupings
          : groupings // ignore: cast_nullable_to_non_nullable
              as List<CardGrouping>,
      hoverObject: freezed == hoverObject
          ? _self.hoverObject
          : hoverObject // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

// dart format on
