import 'package:dauntless/models/base/game_card.dart';

abstract class StackedMapSquareEvent<T extends GameCard> {}

class InitStackedMapSquareEvent<T extends GameCard> extends StackedMapSquareEvent<T> {
  final bool Function(T)? objectViewCondition;

  InitStackedMapSquareEvent(this.objectViewCondition);
}

class SelectStackedMapSquareEvent<T extends GameCard> extends StackedMapSquareEvent<T> {
  final T object;

  SelectStackedMapSquareEvent(this.object);
}
class HoverStackedMapSquareEvent<T extends GameCard> extends StackedMapSquareEvent<T> {
  final T? object;

  HoverStackedMapSquareEvent(this.object);
}