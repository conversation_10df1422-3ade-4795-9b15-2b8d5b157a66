import 'package:dauntless/models/base/game_card.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'stacked_map_square_event.dart';
import 'stacked_map_square_state.dart';

class StackedMapSquareBloc<T extends GameCard> extends Bloc<StackedMapSquareEvent<T>, StackedMapSquareState<T>> {
  final List<T> objectList;
  final bool Function(T)? objectViewCondition;

  StackedMapSquareBloc(this.objectList, this.objectViewCondition) : super(StackedMapSquareState()) {
    on<InitStackedMapSquareEvent<T>>(_onInitStackedMapSquareEvent);
    on<SelectStackedMapSquareEvent<T>>(_onSelectStackedMapSquareEvent);
    on<HoverStackedMapSquareEvent<T>>(_onHoverStackedMapSquareEvent);
  }

  void _onInitStackedMapSquareEvent(
      InitStackedMapSquareEvent<T> event, Emitter<StackedMapSquareState<T>> emit) async {
    emit(state.copyWith(
        // buildType: event.buildType ?? state.buildType,
        // objectViewCondition: event.objectViewCondition
    ));
  }

  void _onSelectStackedMapSquareEvent(
      SelectStackedMapSquareEvent<T> event, Emitter<StackedMapSquareState<T>> emit) async {
    emit(state.copyWith(
        selectedObject: event.object
    ));
  }

  void _onHoverStackedMapSquareEvent(
      HoverStackedMapSquareEvent<T> event, Emitter<StackedMapSquareState<T>> emit) async {
    emit(state.copyWith(
        hoverObject: event.object
    ));
  }
}