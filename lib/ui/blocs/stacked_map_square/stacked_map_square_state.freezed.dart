// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stacked_map_square_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$StackedMapSquareState<T extends GameCard> {
  List<T> get list;
  GameCardId? get listCardId;
  T? get selectedObject; // bool Function(T)? objectViewCondition,
// @Default(StackedMapSquareViewType.list) StackedMapSquareViewType viewType,
// @Default(
//     StackedMapSquareViewBuildOption.children) StackedMapSquareViewBuildOption buildType,
  List<CardGrouping> get groupings;
  T? get hoverObject;

  /// Create a copy of StackedMapSquareState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $StackedMapSquareStateCopyWith<T, StackedMapSquareState<T>> get copyWith =>
      _$StackedMapSquareStateCopyWithImpl<T, StackedMapSquareState<T>>(
          this as StackedMapSquareState<T>, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is StackedMapSquareState<T> &&
            const DeepCollectionEquality().equals(other.list, list) &&
            (identical(other.listCardId, listCardId) ||
                other.listCardId == listCardId) &&
            const DeepCollectionEquality()
                .equals(other.selectedObject, selectedObject) &&
            const DeepCollectionEquality().equals(other.groupings, groupings) &&
            const DeepCollectionEquality()
                .equals(other.hoverObject, hoverObject));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(list),
      listCardId,
      const DeepCollectionEquality().hash(selectedObject),
      const DeepCollectionEquality().hash(groupings),
      const DeepCollectionEquality().hash(hoverObject));

  @override
  String toString() {
    return 'StackedMapSquareState<$T>(list: $list, listCardId: $listCardId, selectedObject: $selectedObject, groupings: $groupings, hoverObject: $hoverObject)';
  }
}

/// @nodoc
abstract mixin class $StackedMapSquareStateCopyWith<T extends GameCard, $Res> {
  factory $StackedMapSquareStateCopyWith(StackedMapSquareState<T> value,
          $Res Function(StackedMapSquareState<T>) _then) =
      _$StackedMapSquareStateCopyWithImpl;
  @useResult
  $Res call(
      {List<T> list,
      GameCardId? listCardId,
      T? selectedObject,
      List<CardGrouping> groupings,
      T? hoverObject});
}

/// @nodoc
class _$StackedMapSquareStateCopyWithImpl<T extends GameCard, $Res>
    implements $StackedMapSquareStateCopyWith<T, $Res> {
  _$StackedMapSquareStateCopyWithImpl(this._self, this._then);

  final StackedMapSquareState<T> _self;
  final $Res Function(StackedMapSquareState<T>) _then;

  /// Create a copy of StackedMapSquareState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? list = null,
    Object? listCardId = freezed,
    Object? selectedObject = freezed,
    Object? groupings = null,
    Object? hoverObject = freezed,
  }) {
    return _then(_self.copyWith(
      list: null == list
          ? _self.list
          : list // ignore: cast_nullable_to_non_nullable
              as List<T>,
      listCardId: freezed == listCardId
          ? _self.listCardId
          : listCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      selectedObject: freezed == selectedObject
          ? _self.selectedObject
          : selectedObject // ignore: cast_nullable_to_non_nullable
              as T?,
      groupings: null == groupings
          ? _self.groupings
          : groupings // ignore: cast_nullable_to_non_nullable
              as List<CardGrouping>,
      hoverObject: freezed == hoverObject
          ? _self.hoverObject
          : hoverObject // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

/// @nodoc

class _StackedMapSquareState<T extends GameCard>
    extends StackedMapSquareState<T> {
  const _StackedMapSquareState(
      {final List<T> list = const [],
      this.listCardId,
      this.selectedObject,
      final List<CardGrouping> groupings = const [],
      this.hoverObject})
      : _list = list,
        _groupings = groupings,
        super._();

  final List<T> _list;
  @override
  @JsonKey()
  List<T> get list {
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_list);
  }

  @override
  final GameCardId? listCardId;
  @override
  final T? selectedObject;
// bool Function(T)? objectViewCondition,
// @Default(StackedMapSquareViewType.list) StackedMapSquareViewType viewType,
// @Default(
//     StackedMapSquareViewBuildOption.children) StackedMapSquareViewBuildOption buildType,
  final List<CardGrouping> _groupings;
// bool Function(T)? objectViewCondition,
// @Default(StackedMapSquareViewType.list) StackedMapSquareViewType viewType,
// @Default(
//     StackedMapSquareViewBuildOption.children) StackedMapSquareViewBuildOption buildType,
  @override
  @JsonKey()
  List<CardGrouping> get groupings {
    if (_groupings is EqualUnmodifiableListView) return _groupings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_groupings);
  }

  @override
  final T? hoverObject;

  /// Create a copy of StackedMapSquareState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$StackedMapSquareStateCopyWith<T, _StackedMapSquareState<T>> get copyWith =>
      __$StackedMapSquareStateCopyWithImpl<T, _StackedMapSquareState<T>>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _StackedMapSquareState<T> &&
            const DeepCollectionEquality().equals(other._list, _list) &&
            (identical(other.listCardId, listCardId) ||
                other.listCardId == listCardId) &&
            const DeepCollectionEquality()
                .equals(other.selectedObject, selectedObject) &&
            const DeepCollectionEquality()
                .equals(other._groupings, _groupings) &&
            const DeepCollectionEquality()
                .equals(other.hoverObject, hoverObject));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_list),
      listCardId,
      const DeepCollectionEquality().hash(selectedObject),
      const DeepCollectionEquality().hash(_groupings),
      const DeepCollectionEquality().hash(hoverObject));

  @override
  String toString() {
    return 'StackedMapSquareState<$T>(list: $list, listCardId: $listCardId, selectedObject: $selectedObject, groupings: $groupings, hoverObject: $hoverObject)';
  }
}

/// @nodoc
abstract mixin class _$StackedMapSquareStateCopyWith<T extends GameCard, $Res>
    implements $StackedMapSquareStateCopyWith<T, $Res> {
  factory _$StackedMapSquareStateCopyWith(_StackedMapSquareState<T> value,
          $Res Function(_StackedMapSquareState<T>) _then) =
      __$StackedMapSquareStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<T> list,
      GameCardId? listCardId,
      T? selectedObject,
      List<CardGrouping> groupings,
      T? hoverObject});
}

/// @nodoc
class __$StackedMapSquareStateCopyWithImpl<T extends GameCard, $Res>
    implements _$StackedMapSquareStateCopyWith<T, $Res> {
  __$StackedMapSquareStateCopyWithImpl(this._self, this._then);

  final _StackedMapSquareState<T> _self;
  final $Res Function(_StackedMapSquareState<T>) _then;

  /// Create a copy of StackedMapSquareState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? list = null,
    Object? listCardId = freezed,
    Object? selectedObject = freezed,
    Object? groupings = null,
    Object? hoverObject = freezed,
  }) {
    return _then(_StackedMapSquareState<T>(
      list: null == list
          ? _self._list
          : list // ignore: cast_nullable_to_non_nullable
              as List<T>,
      listCardId: freezed == listCardId
          ? _self.listCardId
          : listCardId // ignore: cast_nullable_to_non_nullable
              as GameCardId?,
      selectedObject: freezed == selectedObject
          ? _self.selectedObject
          : selectedObject // ignore: cast_nullable_to_non_nullable
              as T?,
      groupings: null == groupings
          ? _self._groupings
          : groupings // ignore: cast_nullable_to_non_nullable
              as List<CardGrouping>,
      hoverObject: freezed == hoverObject
          ? _self.hoverObject
          : hoverObject // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

// dart format on
