import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';

part 'stacked_map_square_state.freezed.dart';


@Freezed(fromJson: false, toJson: false)
abstract class StackedMapSquareState<T extends GameCard> with _$StackedMapSquareState<T> {
  //extends Equatable { // TODO: add equatable? Figure out the right freezed-way to handle this? something else for a generic?

  const StackedMapSquareState._();

  const factory StackedMapSquareState({
    @Default([]) List<T> list,
    GameCardId? listCardId,
    T? selectedObject,
    // bool Function(T)? objectViewCondition,
    // @Default(StackedMapSquareViewType.list) StackedMapSquareViewType viewType,
    // @Default(
    //     StackedMapSquareViewBuildOption.children) StackedMapSquareViewBuildOption buildType,
    @Default([]) List<CardGrouping> groupings,
    T? hoverObject,
  }) = _StackedMapSquareState<T>;

}