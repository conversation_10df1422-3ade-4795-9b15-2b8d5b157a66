import 'dart:io';

import 'package:dauntless/frameworks/assets/assets_manager.dart';
import 'package:dauntless/frameworks/assets/assets_state.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

String? _getImagePathForCard(GameCardId? cardId, AssetsState state) {
  var path = cardId == null ? null : state.refForAsset(cardId);
  if (path != null) {
    return path;
  }
  if (cardId == null) {
    return null;
  }
  final (playerId, card) = GetIt.I<GameMatchManager>().state.getCardForId(cardId);
  if (playerId == null || card == null) {
    return null;
  }
  return state.fallbackRefForCardType(playerId, card.type);
}

class CircularMediumCardImage extends StatelessWidget {
  final GameCardId? cardId;
  final double? width;
  final double? height;

  const CircularMediumCardImage({
    super.key,
    required this.cardId,
    this.width,
    this.height,
  });

  // TODO: refactor/extract
  @override
  Widget build(BuildContext context) => BlocBuilder<AssetsManager, AssetsState>(
      bloc: GetIt.I<AssetsManager>(),
      builder: (context, state) {
        final path = _getImagePathForCard(cardId, state);
        if (state.processingStatus != ProcessingStatus.loaded || path == null) {
          return const SizedBox.shrink();
        }
        final File file = File(path);
        final radius =
            width != null ? width! / 2 : context.sizings.mediumImageWidth;
        return CircleAvatar(
          backgroundColor: null,
          foregroundColor: null,
          radius: radius,
          foregroundImage: FileImage(file),
        );
      });
}

class MediumCardImage extends StatelessWidget {
  final GameCardId? cardId;
  final double? width;
  final double? height;

  const MediumCardImage({
    super.key,
    required this.cardId,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) => BlocBuilder<AssetsManager, AssetsState>(
      bloc: GetIt.I<AssetsManager>(),
      builder: (context, state) {
        final path = _getImagePathForCard(cardId, state);
        if (state.processingStatus != ProcessingStatus.loaded || path == null) {
          return const SizedBox.shrink();
        }
        return MediumImage(
          path: path,
          width: width,
          height: height,
        );
      });
}

class CardIcon extends StatelessWidget {
  final String? iconBaseId;
  final double? width;
  final double? height;

  const CardIcon({
    super.key,
    required this.iconBaseId,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) => BlocBuilder<AssetsManager, AssetsState>(
      bloc: GetIt.I<AssetsManager>(),
      builder: (context, state) {
        // final path = iconBaseId == null ? null : state.iconRefForAsset(iconBaseId!);
        if (state.processingStatus != ProcessingStatus.loaded ||
            iconBaseId == null) {
          // || path == null) {
          return const SizedBox.shrink();
        }
        final String path = "${state.basePath}/images/icons/${iconBaseId!}.png";
        return MediumImage(
          path: path,
          width: width,
          height: height,
        );
      });
}

class MediumImage extends StatelessWidget {
  final String path;
  final double? width;
  final double? height;

  const MediumImage({
    super.key,
    required this.path,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final File file = File(path);
    return Image.file(
      file,
      width: width ?? context.sizings.mediumImageWidth,
      height: height,
    );
  }
}

// class MediumImageProvider extends StatelessWidget {
//   final String path;
//   final double? width;
//   final double? height;
//
//   const MediumImageProvider({
//     super.key,
//     required this.path,
//     this.width,
//     this.height,
//   });
//
//   @override
//   Widget build(BuildContext context) => ImageProvider();
// }
