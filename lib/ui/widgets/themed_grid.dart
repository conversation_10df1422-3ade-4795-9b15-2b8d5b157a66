import 'package:flutter/material.dart';

class ThemedGrid extends StatelessWidget {
  final int gridPxWidth;
  final int gridPxHeight;

  const ThemedGrid(this.gridPxWidth, this.gridPxHeight, {super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: gridPxWidth.toDouble(),
      height: gridPxHeight.toDouble(),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 10,
          crossAxisSpacing: 0,
          mainAxisSpacing: 0,
        ),
        itemBuilder: (BuildContext context, int index) {
          return Container(
            color: index % 2 == 0 ? Colors.black : Colors.black.withGreen(20),
          );
        },
      ),
    );
  }
}