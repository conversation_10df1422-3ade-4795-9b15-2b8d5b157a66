import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_state.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_profile.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';

/// Widget that allows users to select different server profiles
/// for connecting to different server environments
class NewServerProfileSourceSelector extends StatelessWidget {
  NewServerProfileSourceSelector({super.key});

  final ValueNotifier<bool> isExpanded = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<bool>(
      valueListenable: isExpanded,
      builder: (context, serverProfileSelectorExpanded, _) =>
          BlocProvider.value(
            value: GetIt.I<ServerEnvironmentManager>(),
            child: serverProfileSelectorExpanded
                ? _ServerProfileSelectorExpandedContent(
                    toggleExpanded: () => isExpanded.value = !isExpanded.value)
                : BlocProvider.value(
                    value: GetIt.I<UserManager>(),
                    child: _MatchSelectionSourceButton(
                      // onTap: () => _connectToDefaultSource(context),
                      onExpand: () => isExpanded.value = !isExpanded.value,
                    ),
                  ),
          ));

  // void _connectToDefaultSource(BuildContext context) {
  //   final userManager = GetIt.I<UserManager>();
  //   // Only proceed if user has set a username
  //   if (userManager.state.user?.id == null ||
  //       userManager.state.user!.id.isEmpty) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('You must set a username before connecting'),
  //         backgroundColor: Colors.red,
  //       ),
  //     );
  //     return;
  //   }
  //
  //   final manager = GetIt.I<ServerEnvironmentManager>();
  //   // Initialize if not already done
  //   manager.add(InitializeEnvironmentEvent());
  //
  //   // Small delay to ensure initialization completes if needed
  //   Future.delayed(const Duration(milliseconds: 200), () {
  //     // This will use the default profile or last selected profile
  //     manager.add(ReconnectWebSocket());
  //
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('Connecting to server...'),
  //         backgroundColor: Colors.blue,
  //       ),
  //     );
  //   });
  // }
}

class _MatchSelectionSourceButton extends StatelessWidget {
  // final void Function() onTap;
  final void Function() onExpand;

  const _MatchSelectionSourceButton({
    // required this.onTap,
    required this.onExpand,
  });

  @override
  Widget build(BuildContext context) => BlocBuilder<UserManager, UserState>(
        builder: (context, userState) {
          final hasUsername = userState.hasUsername;

          return BlocBuilder<ServerEnvironmentManager, ServerEnvironmentState>(
            builder: (context, state) {
              final hasSuccessfulConnection =
                  state.selectedProfileConnectionStatus !=
                      ProcessingStatus.error;
              final connectionStatusColor =
                  hasSuccessfulConnection ? Colors.green : Colors.red;
              final profileName = state.profile?.name ?? 'Default';

              final hasServerProfileSelected =
                  state.profile != null;

              final canSelectServerProfile = !hasServerProfileSelected && hasUsername;

              return Card(
                margin: const EdgeInsets.all(16.0),
                elevation: 4.0,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      // onTap: hasUsername ? onTap : null,
                      onTap: canSelectServerProfile ? onExpand : null,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 12.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.add_circle,
                                    size: 16,
                                    color: canSelectServerProfile
                                        ? Theme.of(context).primaryColor
                                        : Theme.of(context).disabledColor,
                                  ),
                                  const SizedBox(width: 8),
                                  Flexible(
                                    child: Text(
                                      'Add Source',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: canSelectServerProfile
                                            ? null
                                            : Theme.of(context).disabledColor,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.expand_more),
                              tooltip: 'Show profiles',
                              onPressed: canSelectServerProfile ? onExpand : null,
                              color: canSelectServerProfile
                                  ? null
                                  : Theme.of(context).disabledColor,
                            ),
                          ],
                        ),
                      ),
                    ),
                    // Warning message when username is not set
                    if (!canSelectServerProfile)
                      Padding(
                        padding: const EdgeInsets.only(
                            bottom: 8.0, left: 16.0, right: 16.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning_amber_rounded,
                              color: Colors.amber[700],
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                hasUsername ? 'Disconnect from selected server profile to select a new one' : 'Set a username before connecting',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.amber[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              );
            },
          );
        },
      );
}

class _ServerProfileSelectorExpandedContent extends StatelessWidget {
  final void Function() toggleExpanded;
  const _ServerProfileSelectorExpandedContent({required this.toggleExpanded});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<ServerEnvironmentManager, ServerEnvironmentState>(
        builder: (context, state) {
          final isLoading = state.processingStatus == ProcessingStatus.loading;

          return Card(
            margin: const EdgeInsets.all(16.0),
            elevation: 4.0,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Add Source',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.expand_less),
                        tooltip: 'Minimize',
                        onPressed: toggleExpanded,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (isLoading)
                    const Center(child: CircularProgressIndicator())
                  else if (state.profiles.isEmpty)
                    const Center(
                      child: Text('No server profiles available'),
                    )
                  else
                    _buildProfileList(context, state),
                  // const SizedBox(height: 16),
                  // _buildConnectionStatus(state),
                  // const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed:
                        isLoading ? null : () => _testConnection(context),
                    child: const Text('Test Connection'),
                  ),
                ],
              ),
            ),
          );
        },
      );

  Widget _buildProfileList(BuildContext context, ServerEnvironmentState state) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: state.profiles.map((profile) {
        final isSelected = profile.name == state.selectedProfileId;

        return ListTile(
          title: Text(profile.name),
          subtitle: Text('${profile.httpUrl}'),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: Colors.green)
              : null,
          selected: isSelected,
          onTap: () => _selectProfile(context, profile),
        );
      }).toList(),
    );
  }

  Widget _buildConnectionStatus(ServerEnvironmentState state) {
    final hasSuccessfulConnection =
        state.selectedProfileConnectionStatus != ProcessingStatus.error;
    final lastConnectionTime = state.lastSuccessfulConnection != null
        ? _formatDateTime(state.lastSuccessfulConnection!)
        : 'Never';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('Status: '),
            Text(state.selectedProfileConnectionStatus.name,
                style: hasSuccessfulConnection
                    ? const TextStyle(color: Colors.green)
                    : null)
          ],
        ),
        if (state.profile != null) ...[
          const SizedBox(height: 4),
          Text('Current Profile: ${state.profile!.name}'),
          const SizedBox(height: 4),
          Text('HTTP URL: ${state.profile!.httpUrl}'),
          const SizedBox(height: 4),
          Text('WebSocket URL: ${state.profile!.wsUrl}'),
        ],
        const SizedBox(height: 4),
        Text('Last Connected: $lastConnectionTime'),
      ],
    );
  }

  void _selectProfile(BuildContext context, ServerProfile profile) {
    context.read<ServerEnvironmentManager>().add(
          SetSelectedServerProfileEvent(profile.name),
        );
  }

  void _testConnection(BuildContext context) {
    context.read<ServerEnvironmentManager>().add(ConnectToServerEvent());
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
}
