import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';

class BlocStatusBar extends StatelessWidget {
  final int numActive;
  final int numInactive;
  final Color? activeColor;
  final Color? inactiveColor;

  const BlocStatusBar({
    super.key,
    required this.numActive,
    required this.numInactive,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) => Padding(
    padding: const EdgeInsets.all(1.0),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ...List.generate(numActive, (_) => _StatusSquare(
          color: activeColor ?? Colors.green,
        )),
        ...List.generate(numInactive, (_) => _StatusSquare(
          color: inactiveColor ?? Colors.grey,
        )),
      ],
    ),
  );
}

class _StatusSquare extends StatelessWidget {
  final Color color;

  const _StatusSquare({
    super.key,
    required this.color,
  });

  @override
  Widget build(BuildContext context) => Container(
    color: color,
    width: context.sizings.padding,
    height: context.sizings.padding,
    margin: const EdgeInsets.all(2.0),
  );
}