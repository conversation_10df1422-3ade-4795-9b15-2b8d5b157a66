import 'dart:ui';
import 'package:flutter/material.dart';

class BasicTextButton extends StatelessWidget {
  const BasicTextButton({
    super.key,
    required this.text,
    this.onPressed,
  });

  final VoidCallback? onPressed;
  final String text;

  @override
  Widget build(BuildContext context) => TextButton(
        onPressed: onPressed,
        child: Text(text,
          overflow: TextOverflow.ellipsis,
        ),
      );
}