import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/frameworks/user/user_profile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';

class UserNameEditorDEV extends StatelessWidget {
  UserNameEditorDEV({super.key});

  final ValueNotifier<bool> isExpanded = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<bool>(
    valueListenable: isExpanded,
    builder: (context, expanded, _) => expanded
        ? _UserProfileManagerExpanded(
            toggleExpanded: () => isExpanded.value = !isExpanded.value,
          )
        : _UserProfileSelectorCollapsed(
            onExpand: () => isExpanded.value = !isExpanded.value,
          ),
  );
}

class _UserProfileSelectorCollapsed extends StatelessWidget {
  final VoidCallback onExpand;

  const _UserProfileSelectorCollapsed({required this.onExpand});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserManager, UserState>(
      builder: (context, state) {
        final selectedProfile = state.selectedProfile;
        final hasUser = selectedProfile != null;
        
        return Card(
          margin: const EdgeInsets.all(16.0),
          elevation: 2.0,
          child: InkWell(
            onTap: onExpand,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              child: Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 20,
                    color: hasUser 
                        ? Theme.of(context).primaryColor 
                        : Theme.of(context).disabledColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      hasUser 
                          ? 'User: ${selectedProfile.displayName ?? selectedProfile.name}'
                          : 'No user selected',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: hasUser 
                            ? null 
                            : Theme.of(context).disabledColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (state.isLoading) 
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  else
                    Icon(
                      Icons.expand_more,
                      color: Theme.of(context).primaryColor,
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _UserProfileManagerExpanded extends StatefulWidget {
  final VoidCallback toggleExpanded;

  const _UserProfileManagerExpanded({required this.toggleExpanded});

  @override
  State<_UserProfileManagerExpanded> createState() => _UserProfileManagerExpandedState();
}

class _UserProfileManagerExpandedState extends State<_UserProfileManagerExpanded> {
  final TextEditingController _newUsernameController = TextEditingController();
  bool _showAddForm = false;

  @override
  void dispose() {
    _newUsernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserManager, UserState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const _LoadingWidget();
        }

        if (state.hasError) {
          return _ErrorWidget(
            onRetry: () => context.read<UserManager>().add(LoadUserConfigEvent()),
            onCollapse: widget.toggleExpanded,
          );
        }

        return Card(
          margin: const EdgeInsets.all(16.0),
          elevation: 4.0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with collapse button
              _buildHeader(),
              
              // Add new profile form
              if (_showAddForm) _buildAddProfileForm(),
              
              // Profile list
              if (state.profiles.isNotEmpty) _buildProfileList(state),
              
              // Empty state
              if (state.profiles.isEmpty && !_showAddForm) _buildEmptyState(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.people,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'User Profiles',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => setState(() => _showAddForm = !_showAddForm),
            tooltip: 'Add new profile',
          ),
          IconButton(
            icon: const Icon(Icons.expand_less),
            onPressed: widget.toggleExpanded,
            tooltip: 'Collapse',
          ),
        ],
      ),
    );
  }

  Widget _buildAddProfileForm() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          TextField(
            controller: _newUsernameController,
            decoration: const InputDecoration(
              labelText: 'New Username',
              hintText: 'Enter username',
              border: OutlineInputBorder(),
            ),
            onSubmitted: (_) => _addProfile(),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  setState(() => _showAddForm = false);
                  _newUsernameController.clear();
                },
                child: const Text('Cancel'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _addProfile,
                child: const Text('Add Profile'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProfileList(UserState state) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: state.profiles.length,
      itemBuilder: (context, index) {
        final profile = state.profiles[index];
        final isSelected = state.selectedProfile?.id == profile.id;
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ListTile(
            leading: Radio<String>(
              value: profile.id,
              groupValue: state.selectedProfile?.id,
              onChanged: (value) => _selectProfile(profile.id),
            ),
            title: Text(
              profile.displayName ?? profile.name,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            subtitle: profile.displayName != null 
                ? Text(profile.name)
                : null,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isSelected) 
                  const Icon(Icons.check_circle, color: Colors.green),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditDialog(profile),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _confirmDelete(profile),
                ),
              ],
            ),
            onTap: () => _selectProfile(profile.id),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return const Padding(
      padding: EdgeInsets.all(32),
      child: Center(
        child: Column(
          children: [
            Icon(Icons.person_add, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No user profiles yet',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Click the + button to add your first profile',
              style: TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _addProfile() {
    final name = _newUsernameController.text.trim();
    if (name.isEmpty) return;

    final profile = UserProfile(
      id: const Uuid().v4(),
      name: name,
    );

    context.read<UserManager>().add(AddUserProfileEvent(profile: profile));
    
    setState(() => _showAddForm = false);
    _newUsernameController.clear();
  }

  void _selectProfile(String profileId) {
    context.read<UserManager>().add(
      SetSelectedUserProfileEvent(profileId: profileId),
    );
  }

  void _showEditDialog(UserProfile profile) {
    final nameController = TextEditingController(text: profile.name);
    final displayNameController = TextEditingController(text: profile.displayName ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Username',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name (optional)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              if (name.isEmpty) return;

              final displayName = displayNameController.text.trim();
              final updatedProfile = profile.copyWith(
                name: name,
                displayName: displayName.isEmpty ? null : displayName,
              );

              context.read<UserManager>().add(
                UpdateUserProfileEvent(profile: updatedProfile),
              );
              
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _confirmDelete(UserProfile profile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Profile'),
        content: Text('Are you sure you want to delete "${profile.displayName ?? profile.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<UserManager>().add(
                RemoveUserProfileEvent(profileId: profile.id),
              );
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class _LoadingWidget extends StatelessWidget {
  const _LoadingWidget();

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: const Padding(
        padding: EdgeInsets.all(32),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading user profiles...'),
            ],
          ),
        ),
      ),
    );
  }
}

class _ErrorWidget extends StatelessWidget {
  final VoidCallback onRetry;
  final VoidCallback onCollapse;
  
  const _ErrorWidget({required this.onRetry, required this.onCollapse});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Failed to load user profiles'),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: onCollapse,
                    child: const Text('Collapse'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: onRetry,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}