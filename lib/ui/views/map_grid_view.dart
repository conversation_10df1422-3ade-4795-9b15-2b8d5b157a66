import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' show Quad, Vector3;

class MapGridView extends StatelessWidget {
  final double cellWidth;
  final double cellHeight;
  final _CellBuilder gridBuilder;

  const MapGridView({
    super.key,
    required this.cellWidth,
    required this.cellHeight,
    required this.gridBuilder,
  });

  Rect axisAlignedBoundingBox(Quad quad) {
    double xMin = quad.point0.x;
    double xMax = quad.point0.x;
    double yMin = quad.point0.y;
    double yMax = quad.point0.y;
    for (final Vector3 point in <Vector3>[
      quad.point1,
      quad.point2,
      quad.point3,
    ]) {
      if (point.x < xMin) {
        xMin = point.x;
      } else if (point.x > xMax) {
        xMax = point.x;
      }

      if (point.y < yMin) {
        yMin = point.y;
      } else if (point.y > yMax) {
        yMax = point.y;
      }
    }

    return Rect.fromLTRB(xMin, yMin, xMax, yMax);
  }

  @override
  Widget build(BuildContext context) => Center(
        child: InteractiveViewer.builder(
          // TODO: set this from a parameter
          boundaryMargin: EdgeInsets.all(cellWidth * 10),
          // boundaryMargin: const EdgeInsets.all(double.infinity),
          maxScale: 20.0,
          minScale: 0.1,
          scaleFactor: 1.00,
          builder: (BuildContext context, Quad viewport) => _GridBuilder(
            cellWidth: cellWidth,
            cellHeight: cellHeight,
            viewport: axisAlignedBoundingBox(viewport),
            builder: gridBuilder,
          ),
        ),
      );
}

typedef _CellBuilder = Widget Function(
    BuildContext context, int row, int column);

class _GridBuilder extends StatelessWidget {
  const _GridBuilder({
    required this.cellWidth,
    required this.cellHeight,
    required this.viewport,
    required this.builder,
  });

  final double cellWidth;
  final double cellHeight;
  final Rect viewport;
  final _CellBuilder builder;

  @override
  Widget build(BuildContext context) {
    final int firstRow = (viewport.top / cellHeight).floor();
    final int lastRow = (viewport.bottom / cellHeight).ceil();
    final int firstCol = (viewport.left / cellWidth).floor();
    final int lastCol = (viewport.right / cellWidth).ceil();

    // This will create and render exactly (lastRow - firstRow) * (lastCol - firstCol) cells

    return SizedBox(
      // Stack needs constraints, even though we then Clip.none outside of them.
      // InteractiveViewer.builder always sets constrained to false, giving infinite constraints to the child.
      // See: https://api.flutter.dev/flutter/widgets/InteractiveViewer/constrained.html
      width: 1,
      height: 1,
      child: Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          for (int row = firstRow; row < lastRow; row++)
            for (int col = firstCol; col < lastCol; col++)
              Positioned(
                left: col * cellWidth,
                top: row * cellHeight,
                child: builder(context, row, col),
              ),
        ],
      ),
    );
  }
}

// class _PositionedBuilder extends StatelessWidget {
//   final double cellWidth;
//   final double cellHeight;
//   final int row;
//   final int column;
//   final _CellBuilder builder;
//
//   const _PositionedBuilder({
//     super.key,
//     required this.cellWidth,
//     required this.cellHeight,
//     required this.row,
//     required this.column,
//     required this.builder,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final left = column * cellWidth;
//     final top = row * cellHeight;
//     return Positioned(
//       left: left,
//       top: top,
//       child: builder(context, row, column),//, left, top),
//     );
//   }
//
// }