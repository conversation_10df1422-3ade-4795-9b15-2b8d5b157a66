import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_bloc.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_event.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_state.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

typedef ListObjectBuilder<T> = Widget Function(T object,
    {bool isSelected, bool isHovered});

enum ListObjectViewBuildOption { children, groupings }

class ListObjectView<T extends GameCard> extends StatelessWidget {
  final GameCardId? listCardId;
  final ListObjectViewType? viewType;
  final ListObjectViewBuildOption buildType;
  final ListObjectUseCase<T> _useCase;
  final GroupingsUseCase? _groupingsUseCase;
  final ListObjectBuilder<T> _widgetBuilder;
  final List<ListObjectHook<T, ListObjectEvent>> hooks;
  final bool Function(T)? objectViewCondition;

  const ListObjectView(this._useCase, this._widgetBuilder,
      {
        this.viewType,
      this.listCardId,
      this.buildType = ListObjectViewBuildOption.children,
      this.hooks = const [],
      @Deprecated('not currently used') this.objectViewCondition,
      GroupingsUseCase? groupingsUseCase,
      super.key})
      : _groupingsUseCase = groupingsUseCase;

  List<Widget> _getChildren(
      BuildContext context, List<T> listObjects, builder) {
    List<Widget> children = [];
    for (int i = 0; i < listObjects.length; i++) {
      children.add(builder(listObjects[i]));
    }
    return children;
  }

  @override
  Widget build(BuildContext context) => BlocProvider<ListObjectBloc<T>>(
        create: (context) {
          final bloc = ListObjectBloc<T>(_useCase, hooks,
              groupingsUseCase: _groupingsUseCase, listCardId: listCardId)
            ..add(InitListObjectEvent<T>(
              objectViewCondition,
              buildType: buildType,
            ));

          if (viewType != null) {
            bloc.add(SetListViewTypeEvent<T>(viewType!));
          }
          return bloc;
        },
        child: BlocConsumer<ListObjectBloc<T>, ListObjectState<T>>(
          listener: (BuildContext context, state) {},
          builder: (context, state) {
            final listObjectBloc = context.read<ListObjectBloc<T>>();

            /*
            BoxDecoration(
                            border: Border.all(
                                color: Theme.of(context).colorScheme.onSurface),
                            borderRadius: BorderRadius.circular(8),
                            color: Theme.of(context).colorScheme.surface)
             */

            Widget builder(T object) => MouseRegion(
                  onEnter: (event) =>
                      listObjectBloc.add(HoverListObjectEvent<T>(object)),
                  onExit: (event) =>
                      listObjectBloc.add(HoverListObjectEvent<T>(null)),
                  cursor: state.hoverObject != null
                      ? SystemMouseCursors.click
                      : MouseCursor.defer,
                  child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () =>
                          listObjectBloc.add(SelectListObjectEvent<T>(object)),
                      child: _widgetBuilder(
                        object,
                        isHovered: state.hoverObject?.id == object.id,
                        isSelected: state.selectedObject?.id == object.id,
                      )),
                );

            return state.viewType == ListObjectViewType.list
                ? ListView(
                    shrinkWrap: true,
                    children: _getChildren(context, state.list, builder))
                : GridView.extent(
                    maxCrossAxisExtent: 160,
                    shrinkWrap: true,
                    childAspectRatio: 1,
                    children: _getChildren(context, state.list, builder));
          },
        ),
      );
}
