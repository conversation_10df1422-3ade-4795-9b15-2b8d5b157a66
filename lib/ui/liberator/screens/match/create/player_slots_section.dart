import 'package:dauntless/ui/liberator/blocs/create_match/create_match_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_state.dart';
import 'package:dauntless/ui/liberator/screens/match/create/player_slot_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Player slots section widget
class PlayerSlotsSection extends StatelessWidget {
  const PlayerSlotsSection({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<CreateMatchBloc, CreateMatchState>(builder: (context, state) {
        final playerSlots = state.playerSlots;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _PlayerSlotsHeader(state: state),
            const SizedBox(height: 8),

            // Player slots list
            Card(
              elevation: 0, // No shadow
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300),
              ),
              child: Sized<PERSON><PERSON>(
                height: 200,
                child: ListView.builder(
                  itemCount: playerSlots.length,
                  itemBuilder: (context, index) =>
                      PlayerSlotItem(
                        index: index,
                        slot: playerSlots[index],
                      ),
                ),
              ),
            ),
          ],
        );
      });
}

/// Player slots header widget
class _PlayerSlotsHeader extends StatelessWidget {
  final CreateMatchState state;

  const _PlayerSlotsHeader({required this.state});

  @override
  Widget build(BuildContext context) {
    // final playerSlots = state.playerSlots;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text('Players', style: TextStyle(fontWeight: FontWeight.bold)),
        // if (false)
        //   Row(
        //     children: [
        //       IconButton(
        //         onPressed: state.canRemoveSlot
        //             ? () => context
        //             .read<CreateMatchBloc>()
        //             .add(RemovePlayerSlotEvent(playerSlots.length - 1))
        //             : null,
        //         icon: const Icon(Icons.remove_circle_outline),
        //         tooltip: 'Remove player slot',
        //       ),
        //       Text('${playerSlots.length}'),
        //       IconButton(
        //         onPressed: state.canAddSlot
        //             ? () => context
        //             .read<CreateMatchBloc>()
        //             .add(const AddPlayerSlotEvent())
        //             : null,
        //         icon: const Icon(Icons.add_circle_outline),
        //         tooltip: 'Add player slot',
        //       ),
        //     ],
        //   ),
      ],
    );
  }
}

// Content moved to player_slot_item.dart
