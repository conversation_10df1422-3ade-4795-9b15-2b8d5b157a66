import 'package:common/models/player_slot.dart';
import 'package:common/models/player_class.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_event.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_state.dart';
import 'package:dauntless/ui/widgets/image_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Player slot item widget
class PlayerSlotItem extends StatelessWidget {
  final int index;
  final PlayerSlot slot;

  const PlayerSlotItem({
    super.key,
    required this.index,
    required this.slot,
  });

  @override
  Widget build(BuildContext context) {
    final isLocalPlayer = slot.type == PlayerType.humanLocal;
    final isJoinable = slot.type.isHuman && !isLocalPlayer;

    return BlocBuilder<CreateMatchBloc, CreateMatchState>(
      builder: (context, state) {
        final PlayerClass? playerClass =
            state.selectedConfig?.getPlayerClass(slot.playerClassId);
        if (playerClass == null) {
          throw Exception(
              'No player class found for id: ${slot.playerClassId}');
        }
        return ListTile(
          leading: PlayerSlotLeading(
            slot: slot,
            playerClass: playerClass,
          ),
          title: isLocalPlayer
              ? TextField(
                  decoration: InputDecoration(
                    hintText: 'Enter player name',
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                  controller: TextEditingController(text: slot.name),
                  onSubmitted: (newName) {
                    if (newName.trim().isNotEmpty) {
                      context
                          .read<CreateMatchBloc>()
                          .add(UpdatePlayerNameEvent(index, newName));
                    }
                  },
                  onTapOutside: (event) {
                    // Hide keyboard when tapped outside
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                )
              : Text(slot.name ?? 'Unnamed Player'),
          // subtitle: Text(playerClass.name),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (slot.playerId != null && slot.playerId!.isNotEmpty)
                _buildPlayerIdDisplay(slot.playerId!)
              else if (isJoinable)
                _buildPlayerJoinButton(state, index),
              DropdownButton<PlayerType>(
                value: slot.type,
                underline: Container(height: 1, color: Colors.grey.shade300),
                onChanged: (newType) {
                  if (newType != null) {
                    context
                        .read<CreateMatchBloc>()
                        .add(UpdatePlayerTypeEvent(index, newType));
                  }
                },
                items: _getAvailablePlayerTypes(state.hasNetworkCapability)
                    .map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        getPlayerTypeIcon(type),
                        const SizedBox(width: 8),
                        Text(getPlayerTypeDisplayName(type)),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlayerJoinButton(CreateMatchState state, int index) => BlocBuilder<UserManager, UserState>(
      builder: (context, userState) {
        final String? currentUserId = userState.user?.id;
        final bool canJoin = currentUserId != null &&
                          currentUserId.isNotEmpty &&
                          (slot.playerId == null || slot.playerId!.isEmpty);

        return OutlinedButton(
          onPressed: canJoin
              ? () => context.read<CreateMatchBloc>().add(JoinPlayerEvent(index))
              : null,
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.blue,
            side: BorderSide(color: canJoin ? Colors.blue : Colors.grey.shade300),
          ),
          child: const Text('JOIN'),
        );
      },
    );

  /// Get a display name for a player type
  String getPlayerTypeDisplayName(PlayerType type) {
    switch (type) {
      case PlayerType.humanLocal:
        return 'Human (Local)';
      case PlayerType.humanNetwork:
        return 'Human (Network)';
      case PlayerType.botLocal:
        return 'Bot (Local)';
      case PlayerType.botNetwork:
        return 'Bot (Network)';
    }
  }

  // Note: Player ID lookup is now handled by CreateMatchBloc.findPlayerIdForSlot method

  /// Get available player types based on network capability
  List<PlayerType> _getAvailablePlayerTypes(bool hasNetworkCapability) {
    if (hasNetworkCapability) {
      return PlayerType.values;
    } else {
      return [
        PlayerType.humanLocal,
        PlayerType.botLocal,
      ];
    }
  }
}

/// Player slot leading widget
class PlayerSlotLeading extends StatelessWidget {
  final PlayerSlot slot;
  final PlayerClass playerClass;

  const PlayerSlotLeading({
    super.key,
    required this.slot,
    required this.playerClass,
  });

  @override
  Widget build(BuildContext context) => SizedBox(
        width: 128,
        child: Row(
          children: [
            // HostLeadingButton(slot: slot),
            playerClass.icon != null
                ? CardIcon(iconBaseId: playerClass.icon, width: 64, height: 128)
                : getPlayerTypeIcon(slot.type),
            Text(playerClass.name)
          ],
        ),
      );
}

/// Builds a widget to display the player ID
Widget _buildPlayerIdDisplay(String playerId) {
  return Row(
    children: [
      const Icon(Icons.person, size: 14, color: Colors.blue),
      const SizedBox(width: 4),
      Text(
        playerId,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 12,
          color: Colors.blue,
        ),
      ),
    ],
  );
}

/// Host leading button widget
class HostLeadingButton extends StatelessWidget {
  final PlayerSlot slot;

  const HostLeadingButton({
    super.key,
    required this.slot,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<CreateMatchBloc, CreateMatchState>(
        builder: (context, state) {
          final isHost = true; //state.match?.creatorId == slot.id;
          return IconButton(
            icon: Icon(
              Icons.star,
              color: isHost ? Colors.orange : Colors.grey.withOpacity(0.5),
            ),
            tooltip: isHost ? 'Current host player' : 'Set as host player',
            onPressed: () {
              context.read<CreateMatchBloc>().add(SetHostPlayerEvent(slot.id));
            },
          );
        },
      );
}

/// Get an icon for a player type
Widget getPlayerTypeIcon(PlayerType? type) {
  switch (type) {
    case PlayerType.humanLocal:
      return const Icon(Icons.person, color: Colors.blue);
    case PlayerType.humanNetwork:
      return const Icon(Icons.cloud_queue, color: Colors.indigo);
    case PlayerType.botLocal:
      return const Icon(Icons.smart_toy, color: Colors.orange);
    case PlayerType.botNetwork:
      return const Icon(Icons.cloud_queue, color: Colors.orange);
    default:
      return const Icon(Icons.person, color: Colors.grey);
  }
}
