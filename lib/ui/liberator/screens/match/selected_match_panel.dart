// import 'package:dauntless/models/base/game_match.dart';
// import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_bloc.dart';
// import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_event.dart';
// import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_state.dart';
// import 'package:dauntless/use_cases/list_object_use_case.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
//
// /// Panel that displays details for a selected match and updates when players join
// class SelectedMatchPanel extends StatelessWidget {
//   const SelectedMatchPanel({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<MatchSelectionBloc, MatchSelectionState>(
//       builder: (context, state) {
//         if (state.processingStatus == ProcessingStatus.loading) {
//           return const Center(child: CircularProgressIndicator());
//         }
//
//         if (state.selectedMatch == null) {
//           return const Center(
//             child: Text('No match selected'),
//           );
//         }
//
//         return _MatchDetailsContent(match: state.selectedMatch!);
//       },
//     );
//   }
// }
//
// /// Content for the match details panel
// class _MatchDetailsContent extends StatelessWidget {
//   final GameMatch match;
//
//   const _MatchDetailsContent({required this.match});
//
//   @override
//   Widget build(BuildContext context) {
//     final matchName = match.gameName ?? 'Match ${match.id}';
//     final isCurrentUserInMatch = _isCurrentUserInMatch();
//
//     return SingleChildScrollView(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Match info section
//             _buildMatchInfoSection(matchName),
//
//             const SizedBox(height: 16),
//
//             // Players section
//             _buildPlayersSection(),
//
//             const SizedBox(height: 24),
//
//             // Action buttons
//             Center(
//               child: isCurrentUserInMatch
//                 ? ElevatedButton(
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.green,
//                       padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//                     ),
//                     onPressed: () => _startMatch(context),
//                     child: const Text('Start Match'),
//                   )
//                 : ElevatedButton(
//                     style: ElevatedButton.styleFrom(
//                       padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//                     ),
//                     onPressed: () => _joinMatch(context),
//                     child: const Text('Join Match'),
//                   ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildMatchInfoSection(String matchName) {
//     return Card(
//       elevation: 0,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(8.0),
//         side: BorderSide(color: Colors.grey.shade300),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const Text('Match Details',
//                 style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
//             const SizedBox(height: 16),
//
//             // Match name
//             Row(
//               children: [
//                 const Text('Name:', style: TextStyle(fontWeight: FontWeight.w500)),
//                 const SizedBox(width: 8),
//                 Text(matchName),
//               ],
//             ),
//
//             const SizedBox(height: 8),
//
//             // Game type
//             Row(
//               children: [
//                 const Text('Game Type:', style: TextStyle(fontWeight: FontWeight.w500)),
//                 const SizedBox(width: 8),
//                 Text(match.gameTypeId),
//               ],
//             ),
//
//             const SizedBox(height: 8),
//
//             // Status
//             Row(
//               children: [
//                 const Text('Status:', style: TextStyle(fontWeight: FontWeight.w500)),
//                 const SizedBox(width: 8),
//                 Text(match.status.toString().split('.').last),
//               ],
//             ),
//
//             const SizedBox(height: 8),
//
//             // Player count
//             Row(
//               children: [
//                 const Text('Players:', style: TextStyle(fontWeight: FontWeight.w500)),
//                 const SizedBox(width: 8),
//                 Text('${match.players.length} / ${match.playerSlots.length}'),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildPlayersSection() {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const Text('Players', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
//         const SizedBox(height: 8),
//
//         Card(
//           elevation: 0,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(8.0),
//             side: BorderSide(color: Colors.grey.shade300),
//           ),
//           child: match.players.isEmpty
//             ? const Padding(
//                 padding: EdgeInsets.all(16.0),
//                 child: Center(child: Text('No players have joined yet')),
//               )
//             : ListView.builder(
//                 shrinkWrap: true,
//                 physics: const NeverScrollableScrollPhysics(),
//                 itemCount: match.players.length,
//                 itemBuilder: (context, index) {
//                   final player = match.players[index];
//                   final isHost = player.id == match.creatorId;
//
//                   return ListTile(
//                     leading: Icon(
//                       Icons.person,
//                       color: isHost ? Colors.orange : Colors.blue,
//                     ),
//                     title: Text(player.name ?? 'Player ${index + 1}'),
//                     subtitle: Text(
//                       isHost ? 'Host' : 'Player',
//                     ),
//                     trailing: isHost
//                       ? const Icon(Icons.star, color: Colors.orange)
//                       : null,
//                   );
//                 },
//               ),
//         ),
//
//         // Empty slots section
//         if (match.players.length < match.playerSlots.length) ...[
//           const SizedBox(height: 16),
//           const Text('Empty Slots', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
//           const SizedBox(height: 8),
//
//           Card(
//             elevation: 0,
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(8.0),
//               side: BorderSide(color: Colors.grey.shade300),
//             ),
//             child: ListView.builder(
//               shrinkWrap: true,
//               physics: const NeverScrollableScrollPhysics(),
//               itemCount: match.playerSlots.length - match.players.length,
//               itemBuilder: (context, index) {
//                 final slotIndex = match.players.length + index;
//
//                 return ListTile(
//                   leading: const Icon(Icons.person_outline, color: Colors.grey),
//                   title: Text('Empty Slot ${slotIndex + 1}'),
//                   subtitle: const Text('Waiting for player'),
//                 );
//               },
//             ),
//           ),
//         ],
//       ],
//     );
//   }
//
//   bool _isCurrentUserInMatch() {
//     // In a real app, get the current user ID from a user service
//     const currentUserId = 'current_user_id';
//     return match.players.any((p) => p.id == currentUserId);
//   }
//
//   void _joinMatch(BuildContext context) {
//     context.read<MatchSelectionBloc>().add(JoinMatchEvent(matchId: match.id));
//
//     // Show a snackbar to indicate action
//     ScaffoldMessenger.of(context).showSnackBar(
//       const SnackBar(content: Text('Joining match...')),
//     );
//   }
//
//   void _startMatch(BuildContext context) {
//     // TODO: Implement match start functionality
//     ScaffoldMessenger.of(context).showSnackBar(
//       const SnackBar(content: Text('Starting match...')),
//     );
//   }
// }
