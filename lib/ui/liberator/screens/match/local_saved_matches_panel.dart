// import 'package:dauntless/models/base/game_match.dart';
// import 'package:dauntless/ui/liberator/blocs/create_match/create_match_bloc.dart';
// import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_bloc.dart';
// import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_event.dart';
// import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_state.dart';
// import 'package:dauntless/use_cases/list_object_use_case.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
//
// /// Panel that displays locally saved matches for easy access
// @Deprecated('covered by OpenMatchesPanel now')
// class LocalSavedMatchesPanel extends StatelessWidget {
//   const LocalSavedMatchesPanel({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<MatchSelectionBloc, MatchSelectionState>(
//       builder: (context, state) {
//         // Placeholder for local saved matches
//         // In a real implementation, these would be loaded from local storage
//         final localMatches = state.localSavedMatches;
//
//         return Padding(
//           padding: const EdgeInsets.all(8.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               _buildHeader(context, localMatches.length),
//               const SizedBox(height: 8),
//               if (state.processingStatus == ProcessingStatus.loading)
//                 const Center(child: CircularProgressIndicator())
//               else if (localMatches.isEmpty)
//                 _buildEmptyState(context)
//               else
//                 _buildMatchList(context, state, localMatches),
//             ],
//           ),
//         );
//       },
//     );
//   }
//
//   /// Build the panel header
//   Widget _buildHeader(BuildContext context, int matchCount) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 8.0),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           const Text(
//             'Saved Matches',
//             style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
//           ),
//           if (matchCount > 0) Text('$matchCount saved'),
//           IconButton(
//             icon: const Icon(Icons.refresh),
//             tooltip: 'Refresh saved matches',
//             onPressed: () => context.read<MatchSelectionBloc>()
//                 .add(const LoadMatchSelectionDataEvent()),
//           ),
//         ],
//       ),
//     );
//   }
//
//   /// Build the empty state when no saved matches are available
//   Widget _buildEmptyState(BuildContext context) {
//     return Container(
//       height: 100,
//       width: double.infinity,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(8),
//         border: Border.all(color: Colors.grey.shade300),
//       ),
//       child: const Center(
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Icon(Icons.save_outlined, size: 32, color: Colors.grey),
//             SizedBox(height: 8),
//             Text(
//               'No saved matches',
//               style: TextStyle(color: Colors.grey),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   /// Build the list of saved matches
//   Widget _buildMatchList(BuildContext context, MatchSelectionState state, List<GameMatch> matches) {
//     return Container(
//       height: 150, // Fixed height for the local matches panel
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(8),
//         border: Border.all(color: Colors.grey.shade300),
//       ),
//       child: ListView.builder(
//         itemCount: matches.length,
//         itemBuilder: (context, index) => _buildMatchTile(context, state, matches[index]),
//       ),
//     );
//   }
//
//   /// Build a single match tile
//   Widget _buildMatchTile(BuildContext context, MatchSelectionState state, GameMatch match) {
//     final isSelected = context.read<CreateMatchBloc>().state.serverMatchId == match.id;
//
//     return ListTile(
//       dense: true,
//       title: Text(
//         'TODO:fill in match name',
//         style: TextStyle(
//           fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
//         ),
//       ),
//       // subtitle: Text('$gameMode - Last played: ${_getLastPlayedString(match)}'),
//       leading: const Icon(Icons.save),
//       trailing: IconButton(
//         icon: const Icon(Icons.delete_outline),
//         tooltip: 'Delete saved match',
//         onPressed: () => _confirmDeleteMatch(context, match),
//       ),
//       selected: isSelected,
//       // TODO: implement from CreateMatchBloc
//       // onTap: () => context.read<MatchSelectionBloc>().add(SelectMatchEvent(match)),
//     );
//   }
//
//   /// Get a formatted string for when the match was last played
//   // String _getLastPlayedString(GameMatch match) {
//   //   // Get the last modified date from the match settings
//   //   // final lastModified = match.settings['lastModified'] as String?;
//   //
//   //   // if (lastModified == null) {
//   //   //   return 'Unknown';
//   //   // }
//   //
//   //   try {
//   //     final lastModifiedDate = DateTime.parse(lastModified);
//   //     final now = DateTime.now();
//   //     final difference = now.difference(lastModifiedDate);
//   //
//   //     if (difference.inMinutes < 1) {
//   //       return 'Just now';
//   //     } else if (difference.inHours < 1) {
//   //       return '${difference.inMinutes} minutes ago';
//   //     } else if (difference.inDays < 1) {
//   //       return '${difference.inHours} hours ago';
//   //     } else if (difference.inDays < 7) {
//   //       return '${difference.inDays} days ago';
//   //     } else if (difference.inDays < 30) {
//   //       return '${(difference.inDays / 7).floor()} weeks ago';
//   //     } else if (difference.inDays < 365) {
//   //       return '${(difference.inDays / 30).floor()} months ago';
//   //     } else {
//   //       return '${(difference.inDays / 365).floor()} years ago';
//   //     }
//   //   } catch (e) {
//   //     // If we can't parse the date, return a default value
//   //     return 'Unknown date';
//   //   }
//   // }
//
//   /// Show confirmation dialog before deleting a match
//   void _confirmDeleteMatch(BuildContext context, GameMatch match) {
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Delete Saved Match'),
//         content: const Text('Are you sure you want to delete this saved match? This action cannot be undone.'),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('Cancel'),
//           ),
//           TextButton(
//             onPressed: () {
//               // In a real implementation, this would trigger an event to delete the match
//               // context.read<MatchSelectionBloc>().add(DeleteSavedMatchEvent(match.id));
//               Navigator.of(context).pop();
//
//               // Show confirmation snackbar
//               ScaffoldMessenger.of(context).showSnackBar(
//                 const SnackBar(
//                   content: Text('Match deleted'),
//                   duration: Duration(seconds: 2),
//                 ),
//               );
//             },
//             child: const Text('Delete', style: TextStyle(color: Colors.red)),
//           ),
//         ],
//       ),
//     );
//   }
// }
