import 'package:common/models/game_match.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_state.dart';
import 'package:dauntless/ui/blocs/theme/theme_bloc.dart';
import 'package:dauntless/ui/blocs/theme/theme_state.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_event.dart';
import 'package:dauntless/ui/liberator/screens/match/create/create_match_panel.dart';
import 'package:dauntless/ui/widgets/server_profile_selector.dart';
import 'package:dauntless/ui/widgets/user_name_editor.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_bloc.dart';
import 'package:dauntless/ui/liberator/screens/match/open_matches_panel.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// Unified screen for managing matches - both joining existing matches and creating new ones
class MatchManagementScreen extends StatelessWidget {
  const MatchManagementScreen({super.key});

  @override
  Widget build(BuildContext context) => BlocProvider(
        create: (context) => GetIt.I<MatchSelectionEnvironmentManager>(),
        child: BlocBuilder<MatchSelectionEnvironmentManager,
                MatchSelectionEnvironmentState>(
            builder: (context, matchSelectionEnvironmentState) {
          final MatchSelectionEnvironmentManager
              matchSelectionEnvironmentManager =
              GetIt.I<MatchSelectionEnvironmentManager>();
          final openMatchSourceSections = matchSelectionEnvironmentState
              .availableMatchSelectionUseCases
              .map(
                (matchSelectionUseCaseName) => BlocProvider<MatchSelectionBloc>.value(
                  value: matchSelectionEnvironmentManager.getMatchSelectionBloc(
                      matchSelectionUseCaseName)!,
                  // MatchSelectionBloc(
                  //   GetIt.I<GameConfigUseCase>(),
                  //   matchSelectionEnvironmentManager
                  //       .getMatchSelectionUseCase(matchSelectionUseCaseName)!,
                  //   GetIt.I<serverNotificationsUseCase>(),
                  // ),
                  child: OpenMatchesPanel(),
                ),
              )
              .toList();

          return
              BlocProvider(
              create: (_) => CreateMatchBloc(
                GetIt.I<GameConfigUseCase>(),
                GetIt.I<MatchSelectionEnvironmentManager>(),
                GetIt.I<LoggingUseCase>().getRemoteLogger('CreateMatchBloc')
              )..add(const InitializeCreateMatchEvent()),
              child:
              BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) => Row(
              children: [
                // Left panel - Available matches list
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: openMatchSourceSections,
                          ),
                        ),
                      ),
                      UserNameEditorDEV(),
                      NewServerProfileSourceSelector(),
                    ],
                  ),
                ),

                // Divider
                Container(
                  width: 1,
                  // TODO: shouldn't this just come from the theme?
                  color: themeState.theme?.dividerColor ?? Colors.grey.shade600,
                ),

                // Right panel - Create or Edit match form
                Expanded(
                  flex: 2,
                  child: CreateMatchPanel(),
                ),
              ],
            ),
            ),
          );
        }),
      );

  // Removed the _tryGetNetworkMatchSelectionUseCase function as it is no longer needed
}
