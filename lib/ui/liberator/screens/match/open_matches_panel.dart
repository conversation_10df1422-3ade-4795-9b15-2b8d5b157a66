import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/models/base/game_match.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_state.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_state.dart';
import 'package:get_it/get_it.dart';

/// Panel that displays the list of available matches to join
class OpenMatchesPanel extends StatelessWidget {
  const OpenMatchesPanel({super.key});

  @override
  Widget build(BuildContext context) => BlocListener<
          MatchSelectionEnvironmentManager, MatchSelectionEnvironmentState>(
        listener: (context, envState) {
          final sourceName = context.read<MatchSelectionBloc>().sourceName;
          print(
              'OpenMatchesPanel ($sourceName): BlocListener triggered, shouldTriggerMatchLoading: ${envState.shouldTriggerMatchLoading}');
          if (envState.shouldTriggerMatchLoading) {
            // Trigger match loading for this panel
            print(
                'OpenMatchesPanel ($sourceName): Triggering LoadMatchSelectionDataEvent');
            context
                .read<MatchSelectionBloc>()
                .add(const LoadMatchSelectionDataEvent());
            // Reset the trigger flag
            GetIt.I<MatchSelectionEnvironmentManager>()
                .add(ResetMatchLoadingTriggerEvent());
          }
        },
        child: BlocConsumer<MatchSelectionBloc, MatchSelectionState>(
          listenWhen: (previous, current) {
            // Listen for specific changes in the match list or when a match is joined/left
            final previousMatches = {
              for (var match in previous.openMatches) match.id: match
            };
            final currentMatches = {
              for (var match in current.openMatches) match.id: match
            };

            // Check if we have changes in player data (joins/leaves) by comparing playerSlots
            for (final entry in currentMatches.entries) {
              final matchId = entry.key;
              final currentMatch = entry.value;
              final previousMatch = previousMatches[matchId];

              // If this is a new match or there was a change in playerSlots, we need to rebuild
              if (previousMatch == null ||
                  !_arePlayerSlotsEqual(
                      previousMatch.playerSlots, currentMatch.playerSlots)) {
                print(
                    'OpenMatchesPanel: Detected player slot change in match $matchId');
                return true;
              }
            }

            return false;
          },
          listener: (context, state) {
            // Force refresh when player slots change
            print(
                'OpenMatchesPanel: State changed with potential player slot updates');
          },
          builder: (context, state) {
            final sourceName = context.read<MatchSelectionBloc>().sourceName;
            print(
                'OpenMatchesPanel ($sourceName): BlocConsumer builder triggered, status: ${state.processingStatus}, matches: ${state.openMatches.length}');
            if (state.processingStatus == ProcessingStatus.loading) {
              return const Center(child: CircularProgressIndicator());
            } else {
              final availableMatches = state.openMatches;

              return (availableMatches.isEmpty)
                  ? _buildEmptyState(context)
                  : _buildMatchList(context, state, availableMatches);
            }
          },
        ),
      );

  /// Helper method to compare player slots between two matches
  bool _arePlayerSlotsEqual(List<dynamic>? slots1, List<dynamic>? slots2) {
    if (slots1 == null && slots2 == null) return true;
    if (slots1 == null || slots2 == null) return false;

    // Convert dynamic lists to properly typed PlayerSlot lists
    final typedSlots1 = slots1.map((slot) {
      if (slot is PlayerSlot) return slot;
      if (slot is Map<String, dynamic>) return PlayerSlot.fromJson(slot);
      return null;
    }).toList();

    final typedSlots2 = slots2.map((slot) {
      if (slot is PlayerSlot) return slot;
      if (slot is Map<String, dynamic>) return PlayerSlot.fromJson(slot);
      return null;
    }).toList();

    // Current user ID for checking join status
    final userManager = GetIt.instance<UserManager>();
    final currentUserId = userManager.state.user?.id;

    // Check for the current user's join status specifically
    bool previouslyJoined = false;
    bool currentlyJoined = false;

    if (currentUserId != null && currentUserId.isNotEmpty) {
      previouslyJoined =
          typedSlots1.any((slot) => slot?.playerId == currentUserId);

      currentlyJoined =
          typedSlots2.any((slot) => slot?.playerId == currentUserId);

      // If the user's join status changed, definitely need to rebuild
      if (previouslyJoined != currentlyJoined) {
        print(
            'OpenMatchesPanel: Current user join status changed: ${previouslyJoined ? 'joined->not joined' : 'not joined->joined'}');
        return false; // Not equal = rebuild
      }
    }

    // Compare each slot by checking if a player is assigned or not
    for (int i = 0; i < typedSlots1.length; i++) {
      final slot1 = typedSlots1[i];
      final slot2 = typedSlots2[i];

      // If either slot couldn't be parsed, skip it
      if (slot1 == null || slot2 == null) continue;

      // Compare player IDs - null handling is important here
      final hasPlayer1 = slot1.playerId != null && slot1.playerId!.isNotEmpty;
      final hasPlayer2 = slot2.playerId != null && slot2.playerId!.isNotEmpty;

      // If one has a player and the other doesn't, they're different
      if (hasPlayer1 != hasPlayer2) return false;

      // If both have players, compare the IDs
      if (hasPlayer1 && hasPlayer2 && slot1.playerId != slot2.playerId) {
        return false;
      }
    }

    return true;
  }

  /// Build the empty state when no matches are available
  Widget _buildEmptyState(BuildContext context) {
    return BlocBuilder<MatchSelectionBloc, MatchSelectionState>(
      builder: (context, state) {
        final sourceName = context.read<MatchSelectionBloc>().sourceName;
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(width: 16),
                  Icon(
                    Icons.source_outlined,
                    size: 14,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    sourceName,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  // const SizedBox(width: 8),
                  Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, size: 16),
                    tooltip: 'Close match source',
                    constraints: const BoxConstraints(),
                    padding: EdgeInsets.zero,
                    color: Colors.grey[600],
                    onPressed: () {
                      context.read<MatchSelectionBloc>().add(
                            const UnsubscribeFromOpenMatchesEvent(),
                          );

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Match source closed'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 16),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'No open matches available',
              ),
              const SizedBox(height: 16),
              // _CreateMatchButton(),
            ],
          ),
        );
      },
    );
  }

  /// Build the list of available matches
  Widget _buildMatchList(BuildContext context, MatchSelectionState state,
      List<GameMatch> matches) {
    final sourceName = context.read<MatchSelectionBloc>().sourceName;
    print(
        'OpenMatchesPanel: _buildMatchList called with ${matches.length} matches');
    // Debug each match
    for (var i = 0; i < matches.length; i++) {
      print(
          'OpenMatchesPanel: Match[$i] = ${matches[i].id} (${matches[i].gameTypeId})');
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Open Matches',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.source_outlined,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Source: $sourceName',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Row(
                children: [
                  Text('${matches.length} matches'),
                  const SizedBox(width: 16),
                  IconButton(
                    icon: const Icon(Icons.close),
                    tooltip: 'Close match source',
                    onPressed: () {
                      // Close this match selection source
                      context.read<MatchSelectionBloc>().add(
                            const UnsubscribeFromOpenMatchesEvent(),
                          );

                      // Show a confirmation message
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Match source closed'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
        ...matches.map((match) => _buildMatchTile(context, state, match)),
        _CreateMatchButton(),
        // ListView.builder(
        //   itemCount: matches.length,
        //   itemBuilder: (context, index) =>
        //       _buildMatchTile(context, state, matches[index]),
        // ),
      ],
    );
  }

  /// Build a single match tile
  Widget _buildMatchTile(
      BuildContext context, MatchSelectionState state, GameMatch match) {
    // Check if this match is selected in CreateMatchBloc
    final bool isSelected =
        context.watch<CreateMatchBloc>().state.matchId== match.id;

    // Get current user ID
    final userId = context.watch<UserManager>().state.user?.id;

    // Check if user has already joined this match
    final bool hasJoined = userId != null &&
        match.playerSlots.any((slot) => slot.playerId == userId);

    final matchName = match.gameName ?? 'Match ${match.id}';

    // Get player count and max players from match data
    final playerCount = match.playerSlots
        .where((slot) => slot.playerId != null && slot.playerId!.isNotEmpty)
        .length;
    final maxPlayers = match.playerSlots.length;
    // final maxHumanPlayers = match.playerSlots
    //     .where((slot) => slot.type.isHuman)
    //     .length;
    final maxHumanNetworkPlayers = match.playerSlots
        .where((slot) => !slot.type.isLocal && slot.type.isHuman)
        .length;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      elevation: isSelected ? 4 : (hasJoined ? 3 : 1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(
          color: isSelected
              ? Colors.blue
              : (hasJoined ? Colors.green : Colors.transparent),
          width: isSelected || hasJoined ? 2.0 : 1.0,
        ),
      ),
      child: Column(
        children: [
          ListTile(
            title: Text(
              matchName,
              style: TextStyle(
                fontWeight: isSelected || hasJoined
                    ? FontWeight.bold
                    : FontWeight.normal,
                color: isSelected
                    ? Colors.blue
                    : (hasJoined ? Colors.green : null),
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                Text('Game type: ${match.gameTypeId}'),
                Spacer(),
                Text(match.id),
        ]),
        Row(
                  children: [
                    Icon(
                      Icons.people,
                      size: 16,
                      color: isSelected
                          ? Colors.blue
                          : (hasJoined ? Colors.green : null),
                    ),
                    Text(' $playerCount / $maxHumanNetworkPlayers ($maxPlayers)'),
                  ],
                ),
                if (isSelected && hasJoined)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Row(
                      children: const [
                        Icon(Icons.check_circle, size: 16, color: Colors.blue),
                        SizedBox(width: 4),
                        Text('Selected and Joined',
                            style: TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  )
                else if (isSelected)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Row(
                      children: const [
                        Icon(Icons.check_circle, size: 16, color: Colors.blue),
                        SizedBox(width: 4),
                        Text('Selected',
                            style: TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  )
                else if (hasJoined)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Row(
                      children: const [
                        Icon(Icons.check_circle, size: 16, color: Colors.green),
                        SizedBox(width: 4),
                        Text('Joined',
                            style: TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
              ],
            ),
            selected: isSelected,
            onTap: () =>
                context.read<CreateMatchBloc>().add(SelectMatchEvent(match)),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(8.0, 0.0, 8.0, 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                BlocBuilder<UserManager, UserState>(
                  builder: (context, userState) {
                    final bool hasUserId = userState.user?.id != null &&
                        userState.user!.id.isNotEmpty;

                    // Always require a user ID for join/leave actions
                    final bool hasNoUserId = !hasUserId;

                    // Button text and action changes based on join status
                    final String buttonText = hasJoined ? 'LEAVE' : 'JOIN';

                    return TextButton(
                      style: ButtonStyle(
                        foregroundColor: MaterialStateProperty.all(
                          hasJoined ? Colors.red : Colors.blue,
                        ),
                      ),
                      child: Text(buttonText),
                      onPressed: hasNoUserId
                          ? null // Disable button if no user ID
                          : () {
                              if (hasJoined) {
                                // If already joined, trigger leave action
                                context
                                    .read<MatchSelectionBloc>()
                                    .add(LeaveMatchEvent(match.id));
                                    
                                // Don't manually fetch data - WebSocket events will propagate the changes
                                print('OpenMatchesPanel: Leave event sent. WebSocket will propagate updates.');
                                // Note: CreateMatchBloc is already subscribed to WebSocket events via _onOpenMatchesUpdate method
                              } else {
                                // Otherwise join the match
                                context
                                    .read<MatchSelectionBloc>()
                                    .add(JoinMatchEvent(matchId: match.id));
                                    
                                // Don't manually fetch data - WebSocket events will propagate the changes
                                print('OpenMatchesPanel: Join event sent. WebSocket will propagate updates.');
                                // Note: CreateMatchBloc is already subscribed to WebSocket events via _onOpenMatchesUpdate method
                              }
                            }
                    );
                  },
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    _confirmDeleteMatch(context, match);
                  },
                  child: const Text('DELETE'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Show a confirmation dialog and delete the match if confirmed
  void _confirmDeleteMatch(BuildContext context, GameMatch match) {
    final matchName = match.gameName ?? 'Match ${match.id}';

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Match'),
        content: Text(
            'Are you sure you want to delete "$matchName"? This action cannot be undone.'),
        actions: [
          TextButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(dialogContext).pop(),
          ),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
            onPressed: () {
              // Close the dialog
              Navigator.of(dialogContext).pop();

              // Delete the match
              context
                  .read<MatchSelectionBloc>()
                  .add(DeleteMatchEvent(matchId: match.id));

              // Show a snackbar to indicate deletion
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Deleting match "$matchName"...'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// Start match button widget
class _CreateMatchButton extends StatelessWidget {
  const _CreateMatchButton();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          backgroundColor: Colors.blue,
        ),
        onPressed: () {
          // Reset the create match panel
          context.read<CreateMatchBloc>().add(const InitializeCreateMatchEvent());
          
          // Reload the open matches panel
          // context.read<MatchSelectionBloc>().add(const LoadMatchSelectionDataEvent());
        },
        child: const Text(
          'Create Match',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
