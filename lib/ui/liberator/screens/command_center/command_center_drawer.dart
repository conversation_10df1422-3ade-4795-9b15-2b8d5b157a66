import 'package:dauntless/frameworks/game_config/game_config_manager.dart';
import 'package:dauntless/frameworks/game_config/game_config_state.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/frameworks/network/websocket_event.dart';
import 'package:dauntless/frameworks/network/websocket_manager.dart';
import 'package:dauntless/frameworks/network/websocket_state.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CommandCenterDrawer extends StatelessWidget {
  const CommandCenterDrawer({super.key});

  @override
  Widget build(BuildContext context) => Drawer(
        child: ListView(
          children: [
            ListTile(
              title: const Text('Match Management'),
              leading: const Icon(Icons.videogame_asset),
              onTap: () {
                GetIt.I<CommandCenterBloc>().add(TapMatchManagementEvent());
                // Navigator.of(context).push(
                //   MaterialPageRoute(
                //     builder: (context) => BlocProvider<MatchSelectionBloc>(
                //       create: (_) => MatchSelectionBloc(
                //         gameConfigManager: GetIt.I<GameConfigManager>(),
                //         gameMatchManager: GetIt.I<GameMatchManager>(),
                //         matchUseCase: GetIt.I<MatchUseCase>(),
                //         webSocketManager: GetIt.I<WebSocketManager>(),
                //       ),
                //       child: const MatchManagementScreen(),
                //     ),
                //   ),
                // );
              },
            ),
            const Divider(),
            const _WebSocketStatus(),
            _ServerTestPanel(),
            _GameConfigInfo(),
            _MatchConfigInfo(),
            _CommandCenterInfo()
          ],
        ),
      );
}

class _WebSocketStatus extends StatefulWidget {
  const _WebSocketStatus({super.key});

  @override
  State<_WebSocketStatus> createState() => _WebSocketStatusState();
}

class _WebSocketStatusState extends State<_WebSocketStatus> {
  late final WebSocketManager _websocketManager;

  @override
  void initState() {
    super.initState();
    _websocketManager = GetIt.I<WebSocketManager>();

    // Automatically attempt to connect when the widget is initialized
    // This ensures we don't need to manually refresh
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_websocketManager.state.connectionStatus !=
          WebSocketConnectionStatus.connected) {
        _websocketManager.add(ConnectWebSocketEvent());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WebSocketManager, WebSocketState>(
      bloc: _websocketManager,
      builder: (context, state) {
        // Check connection status
        final isConnected =
            state.connectionStatus == WebSocketConnectionStatus.connected;

        return ListTile(
          leading: Icon(
            isConnected ? Icons.cloud_done : Icons.cloud_off,
            color: isConnected ? Colors.green : Colors.red,
          ),
          title: Text(
            'WebSocket ${isConnected ? 'Connected' : 'Disconnected'}',
            style: TextStyle(
              color: isConnected ? Colors.green : Colors.red,
            ),
          ),
          trailing: !isConnected
              ? IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () =>
                      _websocketManager.add(ConnectWebSocketEvent()),
                )
              : null,
        );
      },
    );
  }
}

class _ServerTestPanel extends StatelessWidget {
  const _ServerTestPanel({super.key});

  @override
  Widget build(BuildContext context) => Column(
        children: [
          ListTile(
            title: const Text('Server Test'),
            // onTap: () => GetIt.I<CommandCenterBloc>().add(TapServerTestEvent()),
          ),
        ],
      );
}

class _GameConfigInfo extends StatelessWidget {
  const _GameConfigInfo({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<GameConfigManager, GameConfigState>(
        bloc: GetIt.I<GameConfigManager>(),
        builder: (context, state) => Column(
          children: [
            ListTile(
              title: const Text('Game Config'),
            ),
            Text(state.loadedGameConfig?.name ?? 'Unknown name'),
            Text(state.loadedGameConfig?.id ?? 'Unknown id'),
            const Divider(),
            Text('Player Classes:'),
            ...state.loadedGameConfig?.playerClasses.map((p) => Text(p.name)) ??
                [],
            const Divider(),
            Text('Processing Status:'),
            Text(state.processingStatus.toString()),
          ],
        ),
      );
}

class _MatchConfigInfo extends StatelessWidget {
  const _MatchConfigInfo({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<GameMatchManager, GameMatchState>(
        bloc: GetIt.I<GameMatchManager>(),
        builder: (context, state) => Column(
          children: [
            ListTile(
              title: const Text('Match'),
            ),
            Text(state.matchConfig.name ?? 'Unknown name'),
            Text(state.matchConfig.gameId),
            Text(
                'Selected Game Mode: ${state.matchConfig.selectedGameMode.name}'),
            const Divider(),
            Text('Processing Status:'),
            Text(state.processingStatus.toString()),
            state.matchConfig.players.isNotEmpty
                ? Column(
                    children: [
                      const Divider(),
                      Text('Players:'),
                      ...state.matchConfig.players.map((p) {
                        print('🎮 DRAWER_DISPLAY: Player ${p.id} - ${p.name} (type: ${p.type})');
                        return Text('${p.id} - ${p.name ?? 'Unknown name'}');
                      }),
                    ],
                  )
                : Container(),
          ],
        ),
      );
}

class _CommandCenterInfo extends StatelessWidget {
  const _CommandCenterInfo({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<CommandCenterBloc, CommandCenterState>(
        bloc: GetIt.I<CommandCenterBloc>(),
        builder: (context, state) => Column(
          children: [
            ListTile(
              title: const Text('Command Center'),
            ),
            Text('Game Status: ${state.gameStatus.name}'),
            Text('Opened Detailed Views: ${state.openedDetailedViews}'),
            Text('Opened Primary Views: ${state.openedPrimaryViews}'),
            Text(
                'Navigate To Card 2D Position: ${state.navigateToCard2dPosition}'),
            Text('Open Menu: ${state.openMenu}'),
          ],
        ),
      );
}
