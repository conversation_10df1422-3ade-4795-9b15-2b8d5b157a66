import 'package:collection/collection.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_state.dart';
import 'package:dauntless/frameworks/game_config/game_config_manager.dart';
import 'package:dauntless/frameworks/game_config/game_config_state.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/frameworks/network/websocket_manager.dart';
import 'package:dauntless/frameworks/network/websocket_state.dart';
import 'package:dauntless/models/base/resource_value.dart';
import 'package:dauntless/repositories/websocket_repository.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CommandCenterAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const CommandCenterAppBar({super.key});

  AppBar get activeMatchAppBar => AppBar(
        title: const _CurrentPlayerInfo(),
        centerTitle: false,
        leading: const _GameMenuButton(),
        actions: const [
          NetworkStatusIndicator(),
          SizedBox(width: 8),
          ReloadButton(),
          SizedBox(width: 8),
          TurnCounter(),
          SizedBox(width: 16),
        ],
      );

  @override
  Widget build(BuildContext context) => BlocProvider.value(
        value: GetIt.I<GameMatchManager>(),
        child: BlocBuilder<CommandCenterBloc, CommandCenterState>(
            bloc: GetIt.I<CommandCenterBloc>(),
            builder: (context, state) =>
                state.mainScreen == MainScreen.commandCenter
                    ? activeMatchAppBar
                    : AppBar(
                        // title: const _CurrentPlayerInfo(),
                        centerTitle: false,
                        leading: const _GameMenuButton(),
                        actions: const [
                          NetworkStatusIndicator(),
                          SizedBox(width: 24),
                        ],
                      )),
      );

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Indicator that shows the WebSocket connection status
class NetworkStatusIndicator extends StatelessWidget {
  const NetworkStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) => BlocBuilder<ServerEnvironmentManager, ServerEnvironmentState>(
      bloc: GetIt.I<ServerEnvironmentManager>(),
      builder: (context, serverState) {
        // Get the active profile name
        final String? activeProfile = serverState.profile?.name;
        final bool isLoading = serverState.processingStatus == ProcessingStatus.loading;

        // Then use a StreamBuilder to listen to connection status updates
        return BlocBuilder<WebSocketManager, WebSocketState>(
          bloc: GetIt.I<WebSocketManager>(),
          builder: (context, state) {
            final bool isConnected = state.connectionStatus == WebSocketConnectionStatus.connected;

            // Determine icon and color based on connection state
            Widget icon;
            String tooltip;

            if (isLoading) {
              // Show loading spinner when connecting
              icon = const SizedBox(
                width: 24, height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              );
              tooltip = 'Connecting...';
            } else if (isConnected) {
              // Connected state
              icon = const Icon(Icons.wifi, color: Colors.green);
              tooltip = 'Connected to ${activeProfile ?? "server"}';
            } else {
              // Disconnected state
              icon = const Icon(Icons.wifi_off, color: Colors.red);
              tooltip = 'Disconnected from ${activeProfile ?? "server"}';
            }

            return IconButton(
              icon: icon,
              tooltip: tooltip,
              onPressed: (isConnected || isLoading)
                  ? null  // No action when connected or loading
                  : () {
                      // Show reconnecting feedback
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Reconnecting to ${activeProfile ?? "server"}...'),
                          duration: const Duration(seconds: 2),
                        ),
                      );

                      // Use ServerEnvironmentManager for proper reconnection
                      GetIt.I<ServerEnvironmentManager>().add(ConnectToServerEvent());
                    },
            );
          },
        );
      },
    );
}

class _GameMenuButton extends StatelessWidget {
  const _GameMenuButton({super.key});

  @override
  Widget build(BuildContext context) => IconButton(
        icon: const Icon(Icons.menu),
        onPressed: () => GetIt.I<CommandCenterBloc>().add(OpenMenuEvent()),
      );
}

class _ResourceInfo extends StatelessWidget {
  final ResourceId resourceId;

  const _ResourceInfo({super.key, required this.resourceId});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<GameMatchManager, GameMatchState>(
          builder: (context, matchState) =>
              BlocBuilder<GameConfigManager, GameConfigState>(
                bloc: GetIt.I<GameConfigManager>(),
                builder: (context, state) {
                  final currentPlayerId = matchState.userPlayerId;
                  final resourceCurrentValue = matchState
                          .resources[currentPlayerId]?[resourceId]?.value ??
                      0;

                  return Row(
                    children: [
                      Text('$resourceId: $resourceCurrentValue'),
                      SizedBox(width: 16),
                      // Text('$resourceCurrentValue'),
                    ],
                  );
                },
              ));
}

class _CurrentPlayerInfo extends StatelessWidget {
  const _CurrentPlayerInfo({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<GameMatchManager, GameMatchState>(
          builder: (context, matchState) =>
              BlocBuilder<GameConfigManager, GameConfigState>(
                bloc: GetIt.I<GameConfigManager>(),
                builder: (context, state) {
                  final currentPlayerId = matchState.userPlayerId;

                  // Find the current player in the match config
                  final currentPlayer = matchState.matchConfig.players
                      .firstWhereOrNull((p) => p.id == currentPlayerId);

                  // Get the player class information based on the player's type/class
                  final playerClasses = state.loadedGameConfig?.playerClasses ?? [];
                  final playerClass = currentPlayer != null
                      ? playerClasses.firstWhereOrNull((pc) => pc.id == currentPlayer.id)
                      : null;

                  final resources =
                      (matchState.resources[currentPlayerId] ?? {})
                          .keys
                          .map((r) => _ResourceInfo(resourceId: r))
                          .toList();
                  // final resources = (matchState.resources[currentPlayerId] ?? {}).values.map((r) => Text('${r.name}: ${r.value}'));

                  return Column(
                    children: [
                      Text('Player: ${currentPlayer?.name ?? playerClass?.name}; id: $currentPlayerId'),
                      ...resources
                      // Text('Health: ${player?.health}'),
                    ],
                  );
                },
              ));
}

class ReloadButton extends StatelessWidget {
  const ReloadButton({super.key});

  // Used to prevent multiple simultaneous reconnection attempts
  static bool _isReconnecting = false;

  @override
  Widget build(BuildContext context) {
    // Use a StreamBuilder to check connection status for refresh button
    return StreamBuilder<bool>(
      stream: GetIt.I<WebSocketRepository>().connectionStatusUpdates,
      initialData: GetIt.I<WebSocketRepository>().isConnected,
      builder: (context, snapshot) {
        final bool isConnected = snapshot.data ?? false;
        
        // When disconnected, change the refresh icon to indicate it will try to reconnect
        if (!isConnected) {
          return IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Reconnect and reload',
            onPressed: _isReconnecting
                ? null // Disable button during reconnection
                : () async {
                    if (_isReconnecting) return; // Additional safety check
                    
                    _isReconnecting = true;
                    
                    // Show feedback to user
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Reconnecting and reloading...'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                    
                    try {
                      // First reconnect to WebSocket
                      // TODO: this should not be referencing a repo directly; all this logic should be in a bloc? / manager

                      await GetIt.I<WebSocketRepository>().connect();
                      
                      // Only try to reload if context is still valid and we successfully connected
                      if (context.mounted && GetIt.I<WebSocketRepository>().isConnected) {
                        // Then reload idle cards once connected
                        context.read<CommandCenterBloc>().add(OpenIdleCardsEvent());
                      }
                    } catch (e) {
                      // Handle connection errors gracefully
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Connection failed. Will try again automatically.'),
                            duration: Duration(seconds: 3),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                    } finally {
                      // Always reset the reconnecting flag
                      _isReconnecting = false;
                    }
                  },
          );
        }
        
        // Original functionality when connected
        void onPressed() =>
            context.read<CommandCenterBloc>().add(OpenIdleCardsEvent());
        return CupertinoButton(
              onPressed: onPressed,
              child: const Text('Load Idle'),
            ) ??
            IconButton(icon: const Icon(Icons.refresh), onPressed: onPressed);
      },
    );
  }
}

class TurnCounter extends StatelessWidget {
  const TurnCounter({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<GameMatchManager, GameMatchState>(builder: (context, state) {
        return Row(
          children: [
            Center(child: Text('Turn: ${state.turnCount}')),
            const EndTurnButton(),
          ],
        );
      });
}

class EndTurnButton extends StatelessWidget {
  const EndTurnButton({super.key});

  @override
  Widget build(BuildContext context) => CupertinoButton(
        child: const Text('End Turn'),
        onPressed: () => GetIt.I<CommandCenterBloc>().add(TapEndTurnEvent()),
        // onPressed: () => GetIt.I<MatchManager>().add(SubmitPlayerTurnEvent()),
      );
}
