import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/widgets/basic_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BasicTextCardButton extends StatelessWidget {
  final GameCard card;

  const BasicTextCardButton(this.card, {super.key});

  @override
  Widget build(BuildContext context) => BasicTextButton(
      onPressed: () {
        print('BasicTextCard: onTap: ${card.name}');
        context.read<CommandCenterBloc>().add(TapCardEvent(card.id));
      },
      text: card.name);
}
