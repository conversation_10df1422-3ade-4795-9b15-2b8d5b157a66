import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_bloc.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_state.dart';
import 'package:dauntless/ui/widgets/bloc_status_bar.dart';
import 'package:dauntless/ui/widgets/image_widgets.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'single_card_wrapper.dart';

class LocationViewCardWrapped extends StatelessWidget {
  final GameCard card;
  final double imageSize;

  final bool isHovered;

  const LocationViewCardWrapped({
    super.key,
    required this.card,
    this.imageSize = 128,
    this.isHovered = false,
  });

  @override
  Widget build(BuildContext context) => SingleCardWrapper(
        card: card,
        initialEvents: [LoadCardChildrenEvent()],
        child: LocationViewCard(
          baseImageSize: imageSize,
          isHovered: isHovered,
        ),
      );
}

class LocationViewCard extends StatelessWidget {
  final double baseImageSize;
  final bool isHovered;

  const LocationViewCard({
    super.key,
    this.baseImageSize = 128,
    this.isHovered = false,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        final card = state.card;
        if (card == null) {
          return const Text('No card');
        }
        return BlocBuilder<GameMatchManager, GameMatchState>(
            builder: (context, gameMatchState) {
          final isActiveActionTarget = gameMatchState.cardIsActiveActionTarget(card.id);
          final double iconSize = baseImageSize / 1.5;
          final double thisImageSize = baseImageSize*(40+(state.cardClass?.attributes['ui']?['mapRelativeSize'] ?? 0))/100;
          return DefaultTextStyle(
              //).merge(
              // data: context.theme.copyWith(
              //   textTheme: context.theme.textTheme.copyWith(
              //     bodyMedium: context.theme.textTheme.bodyMedium?.copyWith(
              //       color: Colors.pink,// context.theme.colorScheme.onSurface,
              //     ),
              //   ),
              // ),
              style: TextStyle(
                color: isActiveActionTarget
                    ? context.theme.colorScheme.surfaceTint
                    : null,
                fontWeight: isHovered ? FontWeight.bold : null,
              ),
              child: Column(children: [
                DefaultTextStyle.merge(
                    child: Container(
                        padding: EdgeInsets.symmetric(

                            /// NOTE: these should maybe come from the Positions2dBloc?
                            horizontal: context.sizings.padding,
                            vertical: context.sizings.smallPadding),
                        color: context.theme.scaffoldBackgroundColor,
                        child: Text(card.name)),
                    style: TextStyle(
                        fontSize:
                            Theme.of(context).textTheme.titleMedium!.fontSize)),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      margin: EdgeInsets.all(baseImageSize / 4),
                      // margin: EdgeInsets.all(positions2dState.locationImageSize.toDouble()/8),
                      child: CircularMediumCardImage(
                        cardId: card.id,
                        width: thisImageSize,
                        // width: positions2dState.locationImageSize.toDouble(),
                      ),
                    ),
                    _FleetsIcon(size: iconSize),
                    _MissionsIcon(size: iconSize),
                    _LocationBuildingsIcon(size: iconSize),
                    _LocationDefenseIcon(size: iconSize),
                  ],
                ),
                _LocationEnergyBar(),
                _LocationMinesBar(),
                if (isActiveActionTarget) //state.activeAction != null)
                  _ActiveActionFooter(
                    card: card,
                    cardClass: context
                        .read<GameMatchManager>()
                        .state
                        .getCardClassForId(card.classId),
                    action: gameMatchState.activeAction!,
                    actionSubjectCard: gameMatchState
                        .getCardForId(gameMatchState.activeAction!.subjectCardId)
                        .$2!,
                  ),
              ]));
        });
      });
}

class _FleetsIcon extends StatelessWidget {
  final double? size;

  const _FleetsIcon({
    super.key,
    this.size,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        if (state.vehicleChildrenPlayerIds.isEmpty) {
          return const SizedBox.shrink();
        }

        return Positioned(
          top: 0,
          right: 0,
          child: _LocationIcon(
              iconBaseId: '${state.vehicleChildrenPlayerIds.first}_fleet',
              size: size),
        );
      });
}

class _MissionsIcon extends StatelessWidget {
  final double? size;

  const _MissionsIcon({
    super.key,
    this.size,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        // if (state.vehicleChildrenPlayerIds.isEmpty) {
        //   return const SizedBox.shrink();
        // }

        return Positioned(
          bottom: 0,
          right: 0,
          child: _LocationIcon(iconBaseId: 'alliance_missions', size: size),
        );
      });
}

class _LocationBuildingsIcon extends StatelessWidget {
  final double? size;

  const _LocationBuildingsIcon({
    super.key,
    this.size,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        if (state.buildingChildrenPlayerIds.isEmpty) {
          return const SizedBox.shrink();
        }
        return Positioned(
          top: 0,
          left: 0,
          child: _LocationIcon(iconBaseId: 'imperial_building', size: size),
        );
      });
}

class _LocationDefenseIcon extends StatelessWidget {
  final double? size;

  const _LocationDefenseIcon({
    super.key,
    this.size,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        // if (state.buildingChildrenPlayerIds.isEmpty) {
        //   return const SizedBox.shrink();
        // }
        return Positioned(
          bottom: 0,
          left: 0,
          child: _LocationIcon(iconBaseId: 'imperial_battery', size: size),
        );
      });
}

class _ActiveActionFooter extends StatelessWidget {
  final GameCard card;
  final GameCardClass? cardClass;
  final TargetedAction action;
  final GameCard actionSubjectCard;

  const _ActiveActionFooter({
    super.key,
    required this.card,
    required this.cardClass,
    required this.action,
    required this.actionSubjectCard,
  });

  @override
  Widget build(BuildContext context) {
    if (cardClass != null && action.action.type == ActionType.move) {
      return _MoveActionFooter(
          card: card,
          cardClass: cardClass!,
          action: action,
          actionSubjectCard: actionSubjectCard);
    }
    return const SizedBox.shrink();
  }
}

class _MoveActionFooter extends StatelessWidget {
  final GameCard card;
  final GameCardClass cardClass;
  final TargetedAction action;
  final GameCard actionSubjectCard;

  const _MoveActionFooter({
    super.key,
    required this.card,
    required this.cardClass,
    required this.action,
    required this.actionSubjectCard,
  });

  @override
  Widget build(BuildContext context) {
    /// ****** get distance turns for action // TODO: move to use case; unify logic w/ GameMatchManager
    final locationsUseCase = GetIt.I.get<LocationsUseCase>();
    final actionBaseLocation = actionSubjectCard.locationId == null
        ? null
        : locationsUseCase.getBaseLocationCard(
            context.read<GameMatchManager>().state.hands,
            actionSubjectCard.locationId!);
    final distance = actionBaseLocation?.gridCoordinates == null
        ? null
        : cardClass.getGridDistance(actionBaseLocation!.gridCoordinates!);
    final moveSpeed = action.reconciledAttributes['move_speed']?.toDouble();
    if (moveSpeed == null || distance == null) {
      return const SizedBox.shrink();
    }
    return Column(
      children: [
        const SizedBox(height: 8),
        Text('${distance.toInt()} pc'),
        Text('${(distance / moveSpeed!).ceil()} turns'),
      ],
    );
  }
}

class _LocationEnergyBar extends StatelessWidget {
  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(
          builder: (context, state) => BlocStatusBar(
                numActive: state.usedEnergy,
                numInactive: state.remainingEnergy,
                activeColor: Colors.tealAccent,
                inactiveColor: Colors.deepPurple,
              ));
}

class _LocationMinesBar extends StatelessWidget {
  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        final int total =
            int.parse(state.cardClass?.attributes['mineSlots'] ?? '0');
        final used = 3; // state.card?.attributes['energy']?.toDouble() ?? 0;
        final remainingEnergy = total - used;
        return BlocStatusBar(
          numActive: used,
          numInactive: remainingEnergy > 0 ? remainingEnergy : 0,
          activeColor: Colors.deepOrangeAccent,
          inactiveColor: Colors.brown,
        );
      });
}

class _LocationIcon extends StatelessWidget {
  final String iconBaseId;
  final double? size;

  const _LocationIcon({
    super.key,
    required this.iconBaseId,
    this.size,
  });

  @override
  Widget build(BuildContext context) => CardIcon(
        iconBaseId: iconBaseId,
        width: size,
        height: size,
        // width: context.read<Positions2dBloc>().state.locationIconSize.toDouble(),
        // height: context.read<Positions2dBloc>().state.locationIconSize.toDouble(),
      );
}
