import 'package:dauntless/models/base/game_card.dart';
import 'package:flutter/material.dart';

class VehicleViewCard extends StatelessWidget {
  final GameCard starship;
  const VehicleViewCard(this.starship, {super.key});

  @override
  Widget build(BuildContext context) => Column(children: [
    // CircleAvatar(radius: 64, backgroundColor: _theme.planetAtId(planet.id)?.color),
    // CircleAvatar(backgroundColor: LiberatorTheme.of(context).planetAtId(planet.id)),
    Text(starship.name, style: Theme.of(context).textTheme.titleMedium),
  ]);
}