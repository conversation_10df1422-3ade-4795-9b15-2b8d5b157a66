import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_bloc.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_state.dart';
import 'package:dauntless/ui/widgets/image_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BaseViewCard extends StatelessWidget {
  const BaseViewCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        if (state.card == null) {
          return const Text('No card');
        }

        /// NOTE: this is copypasta'd w/ LocationViewCard
        return BlocBuilder<GameMatchManager, GameMatchState>(
            builder: (context, matchState) => DefaultTextStyle(
            style: TextStyle(
              color: matchState.cardIsActiveActionTarget(state.card?.id)
                  ? Colors.pink
                  : null,
              // fontWeight: isHovered ? FontWeight.bold : null,
            ),
            child: Column(spacing: 8, children: [
              // CircleAvatar(radius: 64, backgroundColor: _theme.planetAtId(planet.id)?.color),
              // CircleAvatar(backgroundColor: LiberatorTheme.of(context).planetAtId(planet.id)),
              // Text(state.card!.id),
              MediumCardImage(cardId: state.card!.id),
              DefaultTextStyle.merge(
                  child: Text(state.card!.name),
                  style: TextStyle(
                      fontSize:
                          Theme.of(context).textTheme.titleMedium!.fontSize)),
            ]),
          ));
      });
}