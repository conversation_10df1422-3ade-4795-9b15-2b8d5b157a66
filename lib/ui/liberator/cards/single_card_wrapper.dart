import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_bloc.dart';
import 'package:dauntless/use_cases/actions_use_case.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class SingleCardWrapper extends StatelessWidget {
  final GameCard card;
  final Widget child;
  final List<SingleCardEvent> initialEvents;

  const SingleCardWrapper({
    super.key,
    required this.card,
    required this.child,
    this.initialEvents = const [],
  });

  @override
  Widget build(BuildContext context) => BlocProvider<SingleCardBloc>(
        key: Value<PERSON>ey(card.id),
        create: (context) {
          // TODO: Dependency injection at the module level
          final bloc = SingleCardBloc(
              card,
              GetIt.I<GameMatchManager>(),
              GetIt.I<ActionsUseCase>(),
              GetIt.I<ActionsGroupingsUseCase>(),
              GetIt.I<PlayersUseCase>());

          for (final event in initialEvents) {
            bloc.add(event);
          }
          return bloc;
        },
        child: BlocListener<GameMatchManager, GameMatchState>(
            // listenWhen: (previous, current) =>
            //     previous.getCardForId(card.id) != current.getCardForId(card.id) ||
            //         // TODO: this seems computationally expensive ... is it worth doing? Should we have a better notifier?
            //     previous.getCardsForLocation(card.id) !=
            //         current.getCardsForLocation(card.id)
            // ,
            listener: (context, state) {
              // TODO: is it better to do it this way -- or send the card from here?
              final updatedCard = state.getCardForId(card.id).$2;
              final bloc =
              context
                  .read<SingleCardBloc>();
              bloc.add(RefreshCardEvent(card: updatedCard));
              bloc.add(LoadCardChildrenEvent());
            },
            child: child),
      );
}
