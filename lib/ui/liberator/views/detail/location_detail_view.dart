// import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
// import 'package:dauntless/frameworks/game_match/game_match_state.dart';
// import 'package:dauntless/models/base/game_card.dart';
// import 'package:dauntless/ui/liberator/cards/basic_text_card.dart';
// import 'package:dauntless/ui/liberator/cards/location_view_card.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
//
// class LocationDetailView extends StatelessWidget {
//   final GameCard card;
//
//   const LocationDetailView(this.card, {super.key});
//
//   @override
//   Widget build(BuildContext context) =>
//       BlocBuilder<MatchManager, MatchState>(builder: (context, state) {
//         final Map<PlayerId, List<GameCard>> cardsInSystem =
//             state.getCardsForLocation(card.id);
//
//         final listOfCardsByPlayerWidgets = cardsInSystem.entries.map((entry) {
//           final player = entry.key;
//           final cards = entry.value;
//           if (cards.isEmpty) {
//             return Container();
//           }
//           return Column(
//             children: [
//               // Text(systemId),
//               GestureDetector(
//                   onTap: () {
//                     print('SystemViewCard: onSecondaryTap: $player');
//                   },
//                   child: Text(player,
//                       style: Theme.of(context).textTheme.titleMedium)),
//               ...cards.map((card) => BasicTextCard(card)),
//             ],
//           );
//         }).toList();
//
//         return LocationViewCard(
//           card: card,
//           children: listOfCardsByPlayerWidgets,
//         );
//       });
// }