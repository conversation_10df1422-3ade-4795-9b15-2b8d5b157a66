import 'package:collection/collection.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_bloc.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_state.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_state.dart';
import 'package:dauntless/ui/liberator/cards/base_view_card.dart';
import 'package:dauntless/ui/liberator/cards/basic_text_card.dart';
import 'package:dauntless/ui/liberator/cards/location_view_card.dart';
import 'package:dauntless/ui/liberator/cards/single_card_wrapper.dart';
import 'package:dauntless/ui/widgets/basic_text_button.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class DetailCardsView extends StatelessWidget {
  const DetailCardsView({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<CommandCenterBloc, CommandCenterState>(
          buildWhen: (previous, current) =>
              previous.openedDetailedViews != current.openedDetailedViews,
          builder: (context, state) => state.openedDetailedViews.isEmpty
              ? const SizedBox.shrink()
              : SizedBox(
                  width: context.sizings.detailedCardWidth,
                  child: SingleChildScrollView(
                    child: Column(
                        children: state.openedDetailedViews
                            .map((card) => _DetailView(cardId: card))
                            .toList()),
                  ),
                ));
}

class _DetailView extends StatelessWidget {
  final GameCardId cardId;

  const _DetailView({super.key, required this.cardId});

  @override
  Widget build(BuildContext context) => BlocBuilder<GameMatchManager, GameMatchState>(
        builder: (context, state) {
          final card = state.getCardForId(cardId).$2;
          if (card == null) {
            return const SizedBox.shrink();
          } else {
            final baseImageInfoView = card.type == CardType.location
                ? const LocationViewCard()
                : const BaseViewCard();
            return GestureDetector(
              onTap: () {
                context.read<CommandCenterBloc>().add(TapCardEvent(cardId));
              },
              child: SingleCardWrapper(
                card: card,
                initialEvents: [
                  LoadCardActionsEvent(context
                      .read<GameMatchManager>()
                      .state
                      .getCardsForLocation(cardId)),
                  LoadCardChildrenEvent()
                ],
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.orange),
                    color: context.theme.colorScheme.primaryContainer
                        .withAlpha(200),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  padding: const EdgeInsets.all(8),
                  margin: const EdgeInsets.all(8),
                  child: Column(
                    children: [
                      const _DetailViewHeading(),
                      // const _DetailViewDebug(),
                      baseImageInfoView,
                      const _CardAvailableUnTargetedActions(),
                      const _CardAvailableTargetedActions(),
                      const _CardAttributesText(),
                      const _CardChildren(),
                      const _CardActiveActions()
                    ],
                  ),
                ),
              ),
            );
          }
        },
      );
}

class _DetailViewHeading extends StatelessWidget {
  const _DetailViewHeading({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        return Row(
          children: [
            // Text('Detail View', style: Theme.of(context).textTheme.titleMedium),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                context
                    .read<CommandCenterBloc>()
                    .add(CloseDetailViewEvent(state.id));
              },
            ),
          ],
        );
      });
}

class _CardAttributesText extends StatelessWidget {
  const _CardAttributesText({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        final card = state.card;
        final cardClass =
            GetIt.I<GameMatchManager>().state.getCardClassForId(card!.classId);
        return Container(
          constraints: const BoxConstraints(maxHeight: 200),
          child: SingleChildScrollView(
            child: Column(children: [
              const Divider(),
              Text('class: ${card.classId}'),
              Text('type: ${card.type.name}'),
              Text('location: ${card.locationId}'),
              const Text('Attributes:'),
              if (cardClass != null)
                ...cardClass.attributes.entries
                    .map((entry) => Text('${entry.key}: ${entry.value}')),
              const Divider(),
              ...state.reconciledAttributes.entries
                  .map((entry) => Text('${entry.key}: ${entry.value}')),
              const Divider(),
            ]),
          ),
        );
      });
}

class _CardUnTargetedActionRow extends StatelessWidget {
  final CardAction action;
  final GameCard card;
  final TargetedAction? selectedAction;
  final GameCard? selectedActionObjectCard;

  const _CardUnTargetedActionRow(this.action, this.card, this.selectedAction,
      this.selectedActionObjectCard,
      {super.key});

  @override
  Widget build(BuildContext context) => Container(
        decoration: BoxDecoration(
          color: context.theme.colorScheme.secondaryContainer,
          borderRadius:
              BorderRadius.circular(context.sizings.tightBorderRadius),
        ),
        padding: EdgeInsets.all(context.sizings.padding),
        margin: EdgeInsets.all(context.sizings.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(action.name,
                    style: Theme.of(context).textTheme.titleMedium),
                if (selectedAction != null)
                  Text(
                      ' => ${selectedActionObjectCard?.name} (${selectedAction?.remainingTurns})'),
              ],
            ),
            BasicTextButton(
                onPressed: () => context
                    .read<CommandCenterBloc>()
                    .add(TapActionEvent(action, card.id)),
                text: '${selectedAction == null ? 'Select' : 'Update'} Target'),
          ],
        ),
      );
}

class _CardAvailableUnTargetedActions extends StatelessWidget {
  const _CardAvailableUnTargetedActions({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(
          builder: (context, cardState) => cardState.card == null ||
                  cardState.card?.activeActions.isNotEmpty == true
              ? const SizedBox.shrink()
              : BlocBuilder<GameMatchManager, GameMatchState>(
                  builder: (context, matchState) {
                  final actions = cardState.actions;
                  final selectedActions =
                      matchState.getSelectedActionsForCard(cardState.card!);

                  return Column(
                      children: actions.map((action) {
                    final selectedAction = selectedActions.firstWhereOrNull(
                        (selectedAction) => selectedAction.action == action);
                    return _CardUnTargetedActionRow(
                        action,
                        cardState.card!,
                        selectedAction,
                        matchState
                            .getCardForId(selectedAction?.objectCardId)
                            .$2);
                  }).toList());
                }));
}

class _CardTargetedActionRow extends StatelessWidget {
  final TargetedAction targetedAction;
  final GameCardClass objectCardClass;
  final TargetedAction? selectedAction;

  const _CardTargetedActionRow(
      {super.key,
      required this.targetedAction,
      required this.objectCardClass,
      this.selectedAction});

  @override
  Widget build(BuildContext context) => Container(
        decoration: BoxDecoration(
          color: context.theme.colorScheme.secondaryContainer,
          borderRadius:
              BorderRadius.circular(context.sizings.tightBorderRadius),
        ),
        padding: EdgeInsets.all(context.sizings.padding),
        margin: EdgeInsets.all(context.sizings.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (selectedAction != null) const Text('**************'),
            BasicTextButton(
                onPressed: () => context
                    .read<CommandCenterBloc>()
                    .add(TapTargetedActionEvent(targetedAction)),
                text: targetedAction.action.name),
            Text(objectCardClass.name,
                style: Theme.of(context).textTheme.titleMedium),
            Text('turns: ${targetedAction.remainingTurns}'),
            const Divider(),
            Text(targetedAction.reconciledAttributes.toString()),
            // Text('start location: ${targetedAction.subjectLocationId}'),
            if (selectedAction != null) const Text('**************'),
          ],
        ),
      );
}

class _CardAvailableTargetedActions extends StatelessWidget {
  const _CardAvailableTargetedActions({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(
          // TODO: define this kind of visibility at the bloc level?
          builder: (context, cardState) => cardState.card == null ||
                  cardState.card?.activeActions.isNotEmpty == true
              ? const SizedBox.shrink()
              : BlocBuilder<GameMatchManager, GameMatchState>(
                  builder: (context, matchState) {
                  final targetedActions = cardState.targetedActions;
                  if (targetedActions.isEmpty) {
                    return const SizedBox.shrink();
                  }
                  return Column(
                      children: targetedActions.map((targetedAction) {
                    final objectCardClass = matchState
                        .getCardClassForId(targetedAction.objectCardClassId);

                    final selectedAction = matchState
                        .getSelectedActionsForCard(cardState.card!)
                        .firstWhereOrNull((selectedAction) =>
                            selectedAction == targetedAction);

                    return _CardTargetedActionRow(
                      targetedAction: targetedAction,
                      objectCardClass: objectCardClass!,
                      selectedAction: selectedAction,
                    );
                  }).toList());
                }));
}

class _CardChildren extends StatelessWidget {
  const _CardChildren({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(
          builder: (context, state) => Column(
                  children: state.uniqueChildren.entries.map((entry) {
                final player = entry.key;
                final cards = entry.value;
                if (cards.isEmpty) {
                  return Container();
                }
                return Column(
                  children: [
                    Text(player,
                        style: Theme.of(context).textTheme.titleMedium),
                    ...cards.map((card) => BasicTextCardButton(card.$1)),
                  ],
                );
              }).toList()));
}

class _CardActiveActions extends StatelessWidget {
  const _CardActiveActions({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        final activeActions = state.card?.activeActions ?? [];
        return Column(children: [
          const Text('Active Actions:'),
          ...activeActions.map((activeAction) => Column(
                children: [
                  Text(activeAction.action.name),
                  Text('target: ${activeAction.objectCardId ?? activeAction.objectCardClassId}'),
                  Text('start location: ${activeAction.subjectLocationId}'),
                  Text('remaining turns: ${activeAction.remainingTurns}'),
                ],
              ))
        ]);
      });
}
