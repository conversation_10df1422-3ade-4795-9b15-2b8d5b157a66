import 'package:dauntless/frameworks/game_match/game_match_event.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/widgets/basic_text_button.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class SelectedActionsView extends StatelessWidget {
  const SelectedActionsView({
    super.key,
  });

  @override
  Widget build(BuildContext context) => BlocBuilder<GameMatchManager, GameMatchState>(
        builder: (context, state) {
          final actions = state.currentTurnActions;
          if (actions.isEmpty) {
            return const SizedBox.shrink(); // Text('No actions selected');
          }
          return Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: actions
                .map((action) => _SelectedActionView(
                      selectedAction: action,
                      subjectCard: state.getCardForId(action.subjectCardId).$2!,
                      subjectLocationCard:
                          state.getCardForId(action.subjectLocationId).$2,
                      objectCard: state.getCardForId(action.objectCardId).$2,
                      objectCardClass: state.getCardClassForId(action.objectCardClassId),
                    ))
                .toList(),
          );
        },
      );
}

class _SelectedActionView extends StatelessWidget {
  final TargetedAction selectedAction;
  final GameCard subjectCard;
  final GameCard? subjectLocationCard;
  final GameCard? objectCard;
  final GameCardClass? objectCardClass;

  const _SelectedActionView({
    super.key,
    required this.selectedAction,
    required this.subjectCard,
    required this.subjectLocationCard,
    required this.objectCard,
    this.objectCardClass,
  });

  @override
  Widget build(BuildContext context) => Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: context.theme.colorScheme.secondaryContainer,
          borderRadius:
              BorderRadius.circular(context.sizings.tightBorderRadius),
        ),
        width: context.sizings.selectedActionWidth,
        child: Column(
          children: [
            _SelectedActionHeading(selectedAction, subjectCard),
            BasicTextButton(
                onPressed: () => GetIt.I<CommandCenterBloc>()
                    .add(TapCardEvent(selectedAction.subjectCardId)),
                text: subjectCard.name),
            if (subjectLocationCard != null) const Text('Location:'),
            if (subjectLocationCard != null)
              BasicTextButton(
                  onPressed: () => GetIt.I<CommandCenterBloc>()
                      .add(TapCardEvent(subjectLocationCard!.id)),
                  text: subjectLocationCard!.name),
            Text('${selectedAction.remainingTurns.toString()} Turns'),
            const SizedBox(height: 8),
            if (objectCard != null || objectCardClass != null)
              Text(
                  'Target:${objectCardClass?.name != null ? ' ${objectCardClass!.name}' : ''}'),
            if (objectCard != null)
              BasicTextButton(
                  onPressed: () => GetIt.I<CommandCenterBloc>()
                      .add(TapCardEvent(objectCard!.id)),
                  text: objectCard!.name),

            /// this "objectCard != null" conditional is really for actions that require the 'select target' phase (e.g. move)
            if (objectCard != null)
              BasicTextButton(
                  onPressed: () => context.read<CommandCenterBloc>().add(
                      TapActionEvent(selectedAction.action, subjectCard.id)),
                  text: 'Update Target'),
            const SizedBox(height: 16),
          ],
        ),
      );
}

class _SelectedActionHeading extends StatelessWidget {
  final TargetedAction action;
  final GameCard subjectCard;

  const _SelectedActionHeading(this.action, this.subjectCard, {super.key});

  @override
  Widget build(BuildContext context) => Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
              child: Text(action.action.name,
                  style: Theme.of(context).textTheme.titleMedium)),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              context
                  .read<GameMatchManager>()
                  .add(CancelPlayEvent(selectedAction: action));
            },
          ),
        ],
      );
}
