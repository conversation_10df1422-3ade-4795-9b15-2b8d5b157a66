import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_bloc.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_event.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_state.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_bloc.dart';
import 'package:dauntless/ui/blocs/single_card/single_card_state.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_state.dart';
import 'package:dauntless/ui/liberator/cards/location_view_card.dart';
import 'package:dauntless/ui/liberator/cards/single_card_wrapper.dart';
import 'package:dauntless/ui/views/list_object_view.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class PrimaryCardsView extends StatelessWidget {
  final double? cardWidth;

  const PrimaryCardsView({super.key, this.cardWidth});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<CommandCenterBloc, CommandCenterState>(
          builder: (context, state) => SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: state.openedPrimaryViews
                      .map((cardId) => BlocBuilder<GameMatchManager, GameMatchState>(
                              builder: (context, state) {
                            final card = state.getCardForId(cardId).$2;
                            if (card == null) {
                              return const SizedBox.shrink();
                            }
                            return _PrimaryViewWrapper(
                                card: card, cardWidth: cardWidth);
                          }))
                      .toList(),
                ),
              ));
}

class _PrimaryViewWrapper extends StatelessWidget {
  final double _cardWidth;
  final GameCard card;

  // final Widget child;

  const _PrimaryViewWrapper({
    super.key,
    required this.card,
    double? cardWidth,
    // required this.child
  }) : _cardWidth = cardWidth ?? 500;

  @override
  Widget build(BuildContext context) => SingleCardWrapper(
        card: card,
        initialEvents: [
          LoadCardActionsEvent(
              context.read<GameMatchManager>().state.getCardsForLocation(card.id))
        ],
        child: Container(
          width: _cardWidth,
          // height: 500,
          // width: double.infinity,
          decoration: BoxDecoration(
            border:
                Border.all(color: context.theme.colorScheme.primaryContainer),
            borderRadius: BorderRadius.circular(5),
          ),
          padding: const EdgeInsets.all(8),
          margin: const EdgeInsets.all(8),
          child: const Column(
            children: [
              _PrimaryViewHeading(),
              _PrimaryViewBody(),
            ],
          ),
        ),
      );
}

class _PrimaryViewHeading extends StatelessWidget {
  const _PrimaryViewHeading({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(builder: (context, state) {
        final double iconSize = context.sizings.iconSize.toDouble();
        return Row(
          children: [
            SizedBox(width: iconSize),
            const Spacer(),
            Text(state.card?.name ?? '',
                style: Theme.of(context).textTheme.titleLarge),
            const Spacer(),
            IconButton(
              iconSize: iconSize,
              icon: const Icon(Icons.close),
              onPressed: () {
                context
                    .read<CommandCenterBloc>()
                    .add(ClosePrimaryViewEvent(state.id));
              },
            ),
          ],
        );
      });
}

class _PrimaryViewBody extends StatelessWidget {
  const _PrimaryViewBody({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<SingleCardBloc, SingleCardState>(
          builder: (context, state) => Expanded(
                child: ListObjectView<GameCard>(
                  GetIt.I<LocationsUseCase>(),
                  (card, {bool isHovered = false, bool isSelected = false}) =>
                      LocationViewCardWrapped(card: card, isHovered: isHovered),
                  groupingsUseCase: GetIt.I<LocationGroupingsUseCase>(),
                  listCardId: state.id,
                  viewType: ListObjectViewType.grid,
                  hooks: [
                    ListObjectHook<GameCard, SelectListObjectEvent<GameCard>>(
                        (event, state) {
                      context
                          .read<CommandCenterBloc>()
                          .add(TapCardEvent(event.object.id));
                    })
                  ],
                ),
              ));
}
