import 'package:collection/collection.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/mapping/positioned_card_2d.dart';
import 'package:dauntless/models/base/mapping/positioned_grid_line_2d.dart';
import 'package:dauntless/ui/blocs/map_instance/map_instance_bloc.dart';
import 'package:dauntless/ui/blocs/map_instance/map_instance_event.dart';
import 'package:dauntless/ui/blocs/map_instance/map_instance_state.dart';
import 'package:dauntless/ui/blocs/positions_2d/positions_2d_bloc.dart';
import 'package:dauntless/ui/blocs/positions_2d/positions_2d_event.dart';
import 'package:dauntless/ui/blocs/positions_2d/positions_2d_state.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_state.dart';
import 'package:dauntless/ui/liberator/cards/location_view_card.dart';
import 'package:dauntless/ui/views/map_grid_view.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/use_cases/map_grid_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class GalaxyMapView extends StatefulWidget {
  const GalaxyMapView({super.key});

  @override
  State<GalaxyMapView> createState() => _GalaxyMapViewState();
}

class _GalaxyMapViewState extends State<GalaxyMapView>
    with TickerProviderStateMixin {
  final TransformationController _transformationController =
      TransformationController();
  late AnimationController _animationController;
  Animation<Matrix4>? _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    )..addListener(() {
        if (_animation?.value != null) {
          _transformationController.value = _animation!.value;
        }
      });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => BlocBuilder<GameMatchManager, GameMatchState>(
    //     buildWhen: (previous, current) =>
    //         current.processingStatus == ProcessingStatus.loaded
    // ,
        builder: (context, matchState) =>
        matchState.locationCards.isEmpty
        // matchState.processingStatus.isReady
            ? const Center(child: CircularProgressIndicator())
            : BlocProvider<Positions2dBloc>(
                create: (context) => Positions2dBloc(
                    GetIt.I<MapGridUseCase>(), GetIt.I<LocationsUseCase>())
                  ..add(LoadPositionItems2dEvent(matchState.locationCards)),
                child: BlocBuilder<Positions2dBloc, Positions2dState>(
                    builder: (context, positionsState) =>
                    // positionsState.processingStatus.isReady
                    positionsState.initialFocusCard?.screenCoordinates == null
                        ? const Center(child: CircularProgressIndicator())
                        : BlocProvider(
                            create: (_) => MapInstanceBloc()
                              ..add(NavigateToPositionEvent(positionsState
                                  .initialFocusCard!.screenCoordinates!)),
                            child:
                                BlocConsumer<MapInstanceBloc, MapInstanceState>(
                                    listener: (context, mapInstanceState) {
                              final navigateToPositionMatrix4 = mapInstanceState
                                  .navigateToPositionMatrix4
                                  ?.consume();
                              if (navigateToPositionMatrix4 != null) {
                                _animation = Matrix4Tween(
                                  begin: _transformationController.value,
                                  end: navigateToPositionMatrix4,
                                ).animate(CurveTween(curve: Curves.easeOut)
                                    .animate(_animationController));

                                _animationController.forward(from: 0);
                              }
                            }, builder: (context, mapInstanceState) {
                              final mapInstanceBloc =
                                  context.read<MapInstanceBloc>();
                              return BlocListener<CommandCenterBloc,
                                  CommandCenterState>(
                                listener: (context, commandCenterState) {
                                  // TODO: handle this outside the UI layer
                                  final String? navigateToCardId =
                                      commandCenterState
                                          .navigateToCard2dPosition
                                          ?.consume();
                                  if (navigateToCardId != null) {
                                    final navigateToCard = matchState
                                        .getCardForId(navigateToCardId);
                                    // TODO: get base position to navigate to; better handle the fallback ... the use case logic should probably return the card if it's a base location card?
                                    final baseLocationCard =
                                        GetIt.I<LocationsUseCase>()
                                                .getBaseLocationCard(
                                                    matchState.hands,
                                                    navigateToCard
                                                        .$2?.locationId) ??
                                            matchState.getCardClassForId(navigateToCard.$2?.id);
                                    final card = positionsState.positionedCards
                                        .firstWhereOrNull((element) =>
                                            element.card.id ==
                                            baseLocationCard?.id);
                                    if (card?.screenCoordinates != null) {
                                      mapInstanceBloc.add(
                                          NavigateToPositionEvent(
                                              card!.screenCoordinates!));
                                    }
                                  }
                                },
                                child: Expanded(
                                  child: LayoutBuilder(
                                      builder: (context, constraints) {
                                    mapInstanceBloc.add(
                                        MapInstanceOnSizeChangedEvent(
                                            constraints));
                                    return InteractiveViewer(
                                        clipBehavior: Clip.none,
                                        constrained: false,

                                        /// Managed by bloc
                                        transformationController:
                                            _transformationController,
                                        maxScale: mapInstanceState.maxScale,
                                        minScale: mapInstanceState.minScale,
                                        interactionEndFrictionCoefficient: mapInstanceState
                                            .interactionEndFrictionCoefficient,
                                        onInteractionEnd: (details) {
                                          mapInstanceBloc.add(
                                              MapInstanceOnInteractionEndEvent(
                                                  details,
                                                  _transformationController
                                                      .value));
                                        },
                                        onInteractionStart: (details) {
                                          mapInstanceBloc.add(
                                              MapInstanceOnInteractionStartEvent(
                                                  details));
                                        },
                                        onInteractionUpdate: (details) {
                                          mapInstanceBloc.add(
                                              MapInstanceOnInteractionUpdateEvent(
                                                  details,
                                                  _transformationController
                                                      .value));
                                        },
                                        // scaleFactor: state.scaleFactor,
                                        // Container(
                                        child: Stack(
                                          children: [
                                            // TODO: extract this as a 'background'? ... need this to set the size of the Stack so it's not infinite
                                            SizedBox(
                                              width: positionsState.gridPxWidth
                                                  .toDouble(),
                                              height: positionsState
                                                  .gridPxHeight
                                                  .toDouble(),
                                            ),
                                            ...positionsState.gridLines.map(
                                                (gridLine) =>
                                                    PositionedGridLine(
                                                        gridLine: gridLine)),
                                            ...positionsState.labels
                                                .map((label) => Positioned(
                                                      left: label.coordinates.x,
                                                      top: label.coordinates.y,
                                                      child: DefaultTextStyle(
                                                          style: TextStyle(
                                                            color: context
                                                                .theme
                                                                .colorScheme
                                                                .onSurface.withAlpha(50),
                                                            fontSize: 200,
                                                          ),
                                                          child: Text(
                                                              label.label)),
                                                    )),
                                            ...positionsState.positionedCards
                                                .map((PositionedCard2d card) =>
                                                    PositionedLocationViewCard(
                                                        key: Key(card.card.id),
                                                        card: card))
                                          ],
                                        ));
                                  }),
                                ),
                              );
                            }))),
              ),
      );
}

class PositionedGridLine extends StatelessWidget {
  final PositionedGridLine2d gridLine;

  const PositionedGridLine({
    super.key,
    required this.gridLine,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<Positions2dBloc, Positions2dState>(builder: (context, state) {
        if (gridLine.axis == Axis.vertical) {
          return Positioned(
            left: gridLine.position,
            top: 0,
            child: Container(
              height: state.gridPxHeight.toDouble(),
              width: gridLine.type == GridLineType.major ? 16 : 4,
              color: context.theme.dividerColor,
            ),
          );
        } else {
          return Positioned(
            left: 0,
            top: gridLine.position,
            child: Container(
              width: state.gridPxWidth.toDouble(),
              height: gridLine.type == GridLineType.major ? 16 : 4,
              color: context.theme.dividerColor,
            ),
          );
        }
      });
}

class PositionedLocationViewCard extends StatelessWidget {
  final PositionedCard2d card;

  const PositionedLocationViewCard({
    super.key,
    required this.card,
  });

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<Positions2dBloc>();
    return BlocBuilder<Positions2dBloc, Positions2dState>(
        builder: (context, state) {
      final isHovered = state.hoveredItem?.card.id == card.card.id;
      return Positioned(
        left: card.screenCoordinates!.x,
        top: card.screenCoordinates!.y,
        child: MouseRegion(
          onEnter: (event) => bloc.add(HoverPositionItem2dEvent(card)),
          onExit: (event) => bloc.add(HoverPositionItem2dEvent(null)),
          cursor: isHovered ? SystemMouseCursors.click : MouseCursor.defer,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => context
                .read<CommandCenterBloc>()
                .add(TapCardEvent(card.card.id)),
            child: LocationViewCardWrapped(
              card: card.card,
              isHovered: isHovered,
              imageSize: state.locationImageSize.toDouble(),
            ),
          ),
        ),
      );
    });
  }
}

class _GalaxyMapGridView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final double _cellWidth = context.sizings.primaryGridSquareSize * 2;
    final double _cellHeight = _cellWidth;

    return DefaultTextStyle(
        style: TextStyle(
          fontWeight: FontWeight.bold,
        ),
        child: MapGridView(
            cellWidth: _cellWidth,
            cellHeight: _cellHeight,
            gridBuilder: (BuildContext context, int row, int column) =>
                Container(
                  height: _cellHeight,
                  width: _cellWidth,
                  // color: row % 2 + column % 2 == 1
                  //     ? Colors.black
                  //     : Colors.black.withGreen(20),
                  child: _StackedViewSquare(
                    rowIndex: row,
                    columnIndex: column,
                  ),
                )));
  }
}

class _StackedViewSquare extends StatelessWidget {
  final int rowIndex;
  final int columnIndex;

  const _StackedViewSquare({
    super.key,
    required this.rowIndex,
    required this.columnIndex,
  });

  @override
  Widget build(BuildContext context) {
    final double _cellWidth = context.sizings.primaryGridSquareSize * 2;
    final double _cellHeight = _cellWidth;

    return Stack(
      children: [
        Center(child: Text('$rowIndex x $columnIndex')),
        // MediumCardImage(card: '')
      ],
    );
  }
}
