import 'package:dauntless/frameworks/game_match/game_match_event.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_state.dart';
import 'package:dauntless/ui/liberator/cards/base_view_card.dart';
import 'package:dauntless/ui/liberator/cards/single_card_wrapper.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActiveActionView extends StatelessWidget {
  const ActiveActionView({
    super.key,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<GameMatchManager, GameMatchState>(
        builder: (context, state) {
          final action = state.activeAction;
          final subjectCard = state.getCardForId(action?.subjectCardId).$2;
          if (subjectCard == null) {
            return const SizedBox.shrink();
          }
          return _ActiveActionView(
            card: subjectCard,
            action: action!.action,
          );
        },
      );
}

class _ActiveActionView extends StatelessWidget {
  final GameCard card;
  final CardAction action;

  const _ActiveActionView({
    super.key,
    required this.card,
    required this.action,
  });

  @override
  Widget build(BuildContext context) => SingleCardWrapper(
        card: card,
        child: Container(
          color: context.theme.colorScheme.secondaryContainer,
          // height: context.sizings.selectedActionWidth,
          width: context.sizings.selectedActionWidth,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const _ActiveActionHeading(),
              Text('Select Target for: ${card.name}'),
              Text(action.name),
              Text('Targets: ${action.targets.map((e) => e.name).join(', ')}'),
              Text('TargetFilters: ${action.targetFilters.map((e) => e.name).join(', ')}'),
              const BaseViewCard(),
            const _ActiveActionButtons(),
            ],
          ),
        ),
      );
}

class _ActiveActionHeading extends StatelessWidget {
  const _ActiveActionHeading({super.key});

  @override
  Widget build(BuildContext context) => BlocBuilder<CommandCenterBloc, CommandCenterState>(
    builder: (context, state) {
      return Row(
        children: [
          const Spacer(),
          IconButton(
            iconSize: context.sizings.iconSize.toDouble(),
            icon: const Icon(Icons.close),
            onPressed: () {
              context
                  .read<CommandCenterBloc>()
                  .add(CloseActiveActionEvent());
            },
          ),
          // const Text('Active Action:'),
          // const SizedBox(width: 8),
          // Text(state.activeAction?.action.name ?? ''),
        ],
      );
    },
  );
}

class _ActiveActionButtons extends StatelessWidget {
  const _ActiveActionButtons({super.key});

  @override
  Widget build(BuildContext context) => BlocBuilder<GameMatchManager, GameMatchState>(
      builder: (context, matchState) => BlocBuilder<CommandCenterBloc, CommandCenterState>(
        builder: (context, commandCenterState) {
          return Row(
            children: [
              if (matchState.activeAction?.action.type == ActionType.changeGroup)
                ElevatedButton(
                  onPressed: () {
                    // TODO: should the CommandCenter communicate this up to the GameMatchManager?
                    context
                        .read<GameMatchManager>()
                        .add(CreateNewGroupForActiveActionEvent());
                  },
                  child: const Text('New'),
                ),
              // const Spacer(),
              // ElevatedButton(
              //   onPressed: () {
              //     context
              //         .read<CommandCenterBloc>()
              //         .add(CloseActiveActionEvent());
              //   },
              //   child: const Text('Cancel'),
              // ),
              // const SizedBox(width: 8),
              // ElevatedButton(
              //   onPressed: () {
              //     // context
              //     //     .read<CommandCenterBloc>()
              //     //     .add(SubmitActiveActionEvent());
              //   },
              //   child: const Text('Submit'),
              // ),
            ],
          );
        },
      )
  );
}