import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_bloc.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_event.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_state.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/ui/views/list_object_view.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class BasicTextRow extends StatelessWidget {
  final GameCard card;
  final bool isHovered;

  const BasicTextRow(this.card, {super.key, this.isHovered = false});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<CommandCenterBloc, CommandCenterState>(
          builder: (context, state) => Container(
        padding: EdgeInsets.symmetric(
            horizontal: context.sizings.padding,
            vertical: context.sizings.smallPadding),
        decoration: BoxDecoration(
          color: isHovered
              ? context.theme.colorScheme.primaryContainer
              : Colors.transparent,
        ),
        child:
            Text(card.name, style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: state.openedPrimaryViews.contains(card)
                  ? context.theme.highlightColor
                  : context.theme.colorScheme.primary,
            )),
      ));
}

class SectorsListView extends StatelessWidget {
  const SectorsListView({super.key});

  @override
  Widget build(BuildContext context) => ListObjectView<GameCard>(
        GetIt.I<LocationsUseCase>(),
        (card, {bool isHovered = false, bool isSelected = false}) => BasicTextRow(card, isHovered: isHovered),
        groupingsUseCase: GetIt.I<LocationGroupingsUseCase>(),
        buildType: ListObjectViewBuildOption.groupings,
        hooks: [
          ListObjectHook<GameCard,
              SelectListObjectEvent<GameCard>>((event, state) {
            print('SectorView: hook: ${event.object.id}');
            final commandCenterBloc = context.read<CommandCenterBloc>();
            commandCenterBloc.add(TapPrimaryCardEvent(event.object.id)
                // (commandCenterBloc.state.selectedLocationGroupingId == event.object.id ? null : event.object)
                );
          })
        ],
      );
}
