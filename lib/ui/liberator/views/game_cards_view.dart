import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_bloc.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_event.dart';
import 'package:dauntless/use_cases/vehicles_use_case.dart';
import 'package:dauntless/ui/liberator/cards/vehicle_view_card.dart';
import 'package:dauntless/ui/views/list_object_view.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class GameCardsView extends StatelessWidget {
  const GameCardsView({super.key});

  @override
  Widget build(BuildContext context) => ListObjectView<GameCard>(
    GetIt.I<VehiclesUseCase>(),
        (card, {bool isHovered = false, bool isSelected = false}) => VehicleViewCard(card),
    hooks: [
      ListObjectHook<GameCard, SelectListObjectEvent<GameCard>>( (event, state) {
        print('GameCardView: hook: ${event.object.id}');
        // context.read<CommandCenterBloc>().add(SelectGameCardEvent(event.object));
      }
      )
    ],
  );
}
