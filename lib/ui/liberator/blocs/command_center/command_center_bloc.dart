import 'package:dauntless/frameworks/game_config/game_config_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_event.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/match_save/match_save_manager.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/ui_event.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'command_center_event.dart';
import 'command_center_state.dart';

class CommandCenterBloc extends Bloc<CommandCenterEvent, CommandCenterState> {
  final RemoteLogger _logger;
  final GameMatchManager _gameMatchManager;
  final MatchSaveManager _matchSaveManager;

  CommandCenterBloc(this._logger, this._gameMatchManager, this._matchSaveManager)
      : super(const CommandCenterState()) {
    on<TapStartMatchEvent>(_onTapStartMatchEvent);
    on<TapPrimaryCardEvent>(_onTapPrimaryCardEvent);
    on<TapCardEvent>(_onTapCardEvent);
    on<CloseDetailViewEvent>(_onCloseDetailViewEvent);
    on<ClosePrimaryViewEvent>(_onClosePrimaryViewEvent);
    on<TapActionEvent>(_onTapActionEvent);
    on<TapTargetedActionEvent>(_onTapTargetedActionEvent);
    on<CloseActiveActionEvent>(_onCloseActiveActionEvent);
    // on<TapNewGroupForActiveActionEvent>(_onTapNewGroupForActiveActionEvent);
    on<OpenIdleCardsEvent>(_onOpenUnplayedCardsEvent);
    on<TurnStartEvent>(_onTurnStartEvent);
    on<TapEndTurnEvent>(_onTapEndTurnEvent);
    on<OpenMenuEvent>(_onOpenMenuEvent);
    on<TapMatchManagementEvent>(_onTapMatchManagementEvent);
  }

  Future<void> _onTapStartMatchEvent(
      TapStartMatchEvent event, Emitter<CommandCenterState> emit) async {
    // Use the game config from the event if provided, otherwise try to get it from GameConfigManager
    var gameConfig = event.gameConfig;
    
    // Log whether the game config came from event or GameConfigManager
    if (gameConfig != null) {
      _logger.info('CommandCenterBloc: Starting match with gameConfig from event');
    } else {
      _logger.info('CommandCenterBloc: No gameConfig in event, checking GameConfigManager');
      final gameConfigManager = GetIt.instance<GameConfigManager>();
      gameConfig = gameConfigManager.state.loadedGameConfig;
      
      if (gameConfig == null) {
        _logger.error('CommandCenterBloc: No game config available from any source');
        return;
      } else {
        _logger.info('CommandCenterBloc: Using gameConfig from GameConfigManager');
      }
    }
    
    // Pass the gameConfig and players to LoadNewMatchEvent
    _matchSaveManager.add(LoadNewMatchEvent(
      gameConfig: gameConfig,
      players: event.players,
    ));
    _logger.info('CommandCenterBloc: Dispatched LoadNewMatchEvent with ${event.players.length} players');
  }

  void _onTapPrimaryCardEvent(
      TapPrimaryCardEvent event, Emitter<CommandCenterState> emit) {
    emit(state.copyWith(
        openedPrimaryViews: state.openedPrimaryViews.union({event.cardId})));
  }

  void _onTapActionEvent(
      TapActionEvent event, Emitter<CommandCenterState> emit) {
    _gameMatchManager.add(StartSelectActiveActionTargetEvent(
        action: event.action, cardId: event.cardId));
  }

  void _onTapTargetedActionEvent(
      TapTargetedActionEvent event, Emitter<CommandCenterState> emit) {
    _gameMatchManager.add(SelectTargetedActionEvent(event.targetedAction));
  }

  void _onCloseActiveActionEvent(
      CloseActiveActionEvent event, Emitter<CommandCenterState> emit) {
    _gameMatchManager.add(ClearActiveActionEvent());
  }

  // void _onTapNewGroupForActiveActionEvent(
  //     TapNewGroupForActiveActionEvent event, Emitter<CommandCenterState> emit) {
  //   _matchManager.add(CreateNewGroupForActiveActionEvent());
  //   emit(state.copyWith(activeAction: null));
  // }

  void _onClosePrimaryViewEvent(
      ClosePrimaryViewEvent event, Emitter<CommandCenterState> emit) {
    var updatedViews = Set<GameCardId>.from(state.openedPrimaryViews);
    updatedViews.removeWhere((cardId) => cardId == event.id);
    emit(state.copyWith(openedPrimaryViews: updatedViews));
  }

  void _onTapCardEvent(TapCardEvent event, Emitter<CommandCenterState> emit) {
    if (_gameMatchManager.state.activeAction != null) {
      if (_gameMatchManager.state.cardIsActiveActionTarget(event.cardId)) {
        _gameMatchManager.add(SelectActiveActionTargetEvent(cardId: event.cardId));
      }
      return;
    }
    emit(state.copyWith(
        navigateToCard2dPosition: UiEvent(event.cardId),
        openedDetailedViews:
            // state.openedDetailedViews.union({event.card})
            /// add this one to top of list // TODO: probably update this ... counterintuitive UX?
            {event.cardId}.union(state.openedDetailedViews)));
  }

  void _onCloseDetailViewEvent(
      CloseDetailViewEvent event, Emitter<CommandCenterState> emit) {
    var updatedViews = Set<GameCardId>.from(state.openedDetailedViews);
    updatedViews.removeWhere((cardId) => cardId == event.id);
    // final updatedViews = state.openedDetailedViews
    // .removeWhere((card) => card.id == event.id);

    emit(state.copyWith(openedDetailedViews: updatedViews));
  }

  void _onOpenUnplayedCardsEvent(
      OpenIdleCardsEvent event, Emitter<CommandCenterState> emit) {
    for (var card in _gameMatchManager.state.currentPlayerUnplayedGroupings) {
      emit(state.copyWith(
          openedDetailedViews: {card.id}.union(state.openedDetailedViews)));
    }
    // emit(state.copyWith(openedUnplayedCards: true));
  }

  void _onTurnStartEvent(
      TurnStartEvent event, Emitter<CommandCenterState> emit) async {
    // TODO: figure out correct dependency loop for this
    GetIt.I<MatchSaveManager>().add(SaveMatchStateEvent());

    /// ********** this is a hack to force the views to update ********** // TODO: fix more properly
    final openedDetailedViews = Set<GameCardId>.from(state.openedDetailedViews);

    emit(state.copyWith(openedDetailedViews: {}));

    /// w/o this, views don't update until user interacts with Primary View? // TODO: fix more properly
    await Future.delayed(const Duration(milliseconds: 50));

    final updatedCardsForViews = openedDetailedViews
        .map((cardId) {
          final card = _gameMatchManager.state.getCardForId(cardId).$2;
          if (card?.type == CardType.location) {
            return card?.id;
            // return _matchManager.locationsUseCase.getLocationById(card.id);
          }
          return _gameMatchManager.state.getCardForId(card?.id).$2?.id;
        })
        .whereType<GameCardId>()
        .toSet();

    emit(state.copyWith(
        gameStatus: CommandGameStatus.activeMainTurn,
        openedDetailedViews: updatedCardsForViews));

    /// ********** end hack **********
  }

  void _onTapEndTurnEvent(
      TapEndTurnEvent event, Emitter<CommandCenterState> emit) {
    _gameMatchManager.add(SubmitPlayerTurnEvent());
    emit(state.copyWith(gameStatus: CommandGameStatus.postTurnSubmission));
  }

  void _onOpenMenuEvent(OpenMenuEvent event, Emitter<CommandCenterState> emit) {
    emit(state.copyWith(openMenu: UiEvent(Nothing())));
  }

  void _onTapMatchManagementEvent(
      TapMatchManagementEvent event, Emitter<CommandCenterState> emit) {
    if (state.mainScreen == MainScreen.matchManagement) {
      emit(state.copyWith(mainScreen: MainScreen.commandCenter));
      return;
    }
    emit(state.copyWith(mainScreen: MainScreen.matchManagement));
  }
}
