import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/ui_event.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'command_center_state.freezed.dart';

enum CommandGameStatus {
  activeMainTurn,
  activePostTurn,
  postTurnSubmission
}

enum MainScreen {
  matchManagement,
  commandCenter,
}

@freezed
abstract class CommandCenterState with _$CommandCenterState {
  const factory CommandCenterState({
    @Default(CommandGameStatus.activeMainTurn) CommandGameStatus gameStatus,
    // GameCardId? selectedLocationGroupingId,
    // TODO: figure out typing this
    @Default({}) Set<GameCardId> openedDetailedViews,
    @Default({}) Set<GameCardId> openedPrimaryViews,
    UiEvent<GameCardId>? navigateToCard2dPosition,
    UiEvent<Nothing>? openMenu,
    /// NOTE: this is probably a temporary stand in to a more flushed out router; doesn't really belong here
    @Default(MainScreen.matchManagement) MainScreen mainScreen,
    // @Default(null) ActiveAction? activeAction,
    // System? selectedSystem,
    // @Default('') String selectedPlanetId,
    // @Default('') String selectedRegionId,
    // @Default('') String selectedSystemId,
    // @Default('') String selectedGridSquare,
  }) = _CommandCenterState;
}