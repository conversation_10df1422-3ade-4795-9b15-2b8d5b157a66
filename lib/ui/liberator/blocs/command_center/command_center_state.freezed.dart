// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'command_center_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CommandCenterState {
  CommandGameStatus get gameStatus; // GameCardId? selectedLocationGroupingId,
// TODO: figure out typing this
  Set<GameCardId> get openedDetailedViews;
  Set<GameCardId> get openedPrimaryViews;
  UiEvent<GameCardId>? get navigateToCard2dPosition;
  UiEvent<Nothing>? get openMenu;

  /// NOTE: this is probably a temporary stand in to a more flushed out router; doesn't really belong here
  MainScreen get mainScreen;

  /// Create a copy of CommandCenterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CommandCenterStateCopyWith<CommandCenterState> get copyWith =>
      _$CommandCenterStateCopyWithImpl<CommandCenterState>(
          this as CommandCenterState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CommandCenterState &&
            (identical(other.gameStatus, gameStatus) ||
                other.gameStatus == gameStatus) &&
            const DeepCollectionEquality()
                .equals(other.openedDetailedViews, openedDetailedViews) &&
            const DeepCollectionEquality()
                .equals(other.openedPrimaryViews, openedPrimaryViews) &&
            (identical(
                    other.navigateToCard2dPosition, navigateToCard2dPosition) ||
                other.navigateToCard2dPosition == navigateToCard2dPosition) &&
            (identical(other.openMenu, openMenu) ||
                other.openMenu == openMenu) &&
            (identical(other.mainScreen, mainScreen) ||
                other.mainScreen == mainScreen));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameStatus,
      const DeepCollectionEquality().hash(openedDetailedViews),
      const DeepCollectionEquality().hash(openedPrimaryViews),
      navigateToCard2dPosition,
      openMenu,
      mainScreen);

  @override
  String toString() {
    return 'CommandCenterState(gameStatus: $gameStatus, openedDetailedViews: $openedDetailedViews, openedPrimaryViews: $openedPrimaryViews, navigateToCard2dPosition: $navigateToCard2dPosition, openMenu: $openMenu, mainScreen: $mainScreen)';
  }
}

/// @nodoc
abstract mixin class $CommandCenterStateCopyWith<$Res> {
  factory $CommandCenterStateCopyWith(
          CommandCenterState value, $Res Function(CommandCenterState) _then) =
      _$CommandCenterStateCopyWithImpl;
  @useResult
  $Res call(
      {CommandGameStatus gameStatus,
      Set<GameCardId> openedDetailedViews,
      Set<GameCardId> openedPrimaryViews,
      UiEvent<GameCardId>? navigateToCard2dPosition,
      UiEvent<Nothing>? openMenu,
      MainScreen mainScreen});
}

/// @nodoc
class _$CommandCenterStateCopyWithImpl<$Res>
    implements $CommandCenterStateCopyWith<$Res> {
  _$CommandCenterStateCopyWithImpl(this._self, this._then);

  final CommandCenterState _self;
  final $Res Function(CommandCenterState) _then;

  /// Create a copy of CommandCenterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameStatus = null,
    Object? openedDetailedViews = null,
    Object? openedPrimaryViews = null,
    Object? navigateToCard2dPosition = freezed,
    Object? openMenu = freezed,
    Object? mainScreen = null,
  }) {
    return _then(_self.copyWith(
      gameStatus: null == gameStatus
          ? _self.gameStatus
          : gameStatus // ignore: cast_nullable_to_non_nullable
              as CommandGameStatus,
      openedDetailedViews: null == openedDetailedViews
          ? _self.openedDetailedViews
          : openedDetailedViews // ignore: cast_nullable_to_non_nullable
              as Set<GameCardId>,
      openedPrimaryViews: null == openedPrimaryViews
          ? _self.openedPrimaryViews
          : openedPrimaryViews // ignore: cast_nullable_to_non_nullable
              as Set<GameCardId>,
      navigateToCard2dPosition: freezed == navigateToCard2dPosition
          ? _self.navigateToCard2dPosition
          : navigateToCard2dPosition // ignore: cast_nullable_to_non_nullable
              as UiEvent<GameCardId>?,
      openMenu: freezed == openMenu
          ? _self.openMenu
          : openMenu // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      mainScreen: null == mainScreen
          ? _self.mainScreen
          : mainScreen // ignore: cast_nullable_to_non_nullable
              as MainScreen,
    ));
  }
}

/// @nodoc

class _CommandCenterState implements CommandCenterState {
  const _CommandCenterState(
      {this.gameStatus = CommandGameStatus.activeMainTurn,
      final Set<GameCardId> openedDetailedViews = const {},
      final Set<GameCardId> openedPrimaryViews = const {},
      this.navigateToCard2dPosition,
      this.openMenu,
      this.mainScreen = MainScreen.matchManagement})
      : _openedDetailedViews = openedDetailedViews,
        _openedPrimaryViews = openedPrimaryViews;

  @override
  @JsonKey()
  final CommandGameStatus gameStatus;
// GameCardId? selectedLocationGroupingId,
// TODO: figure out typing this
  final Set<GameCardId> _openedDetailedViews;
// GameCardId? selectedLocationGroupingId,
// TODO: figure out typing this
  @override
  @JsonKey()
  Set<GameCardId> get openedDetailedViews {
    if (_openedDetailedViews is EqualUnmodifiableSetView)
      return _openedDetailedViews;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_openedDetailedViews);
  }

  final Set<GameCardId> _openedPrimaryViews;
  @override
  @JsonKey()
  Set<GameCardId> get openedPrimaryViews {
    if (_openedPrimaryViews is EqualUnmodifiableSetView)
      return _openedPrimaryViews;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_openedPrimaryViews);
  }

  @override
  final UiEvent<GameCardId>? navigateToCard2dPosition;
  @override
  final UiEvent<Nothing>? openMenu;

  /// NOTE: this is probably a temporary stand in to a more flushed out router; doesn't really belong here
  @override
  @JsonKey()
  final MainScreen mainScreen;

  /// Create a copy of CommandCenterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CommandCenterStateCopyWith<_CommandCenterState> get copyWith =>
      __$CommandCenterStateCopyWithImpl<_CommandCenterState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CommandCenterState &&
            (identical(other.gameStatus, gameStatus) ||
                other.gameStatus == gameStatus) &&
            const DeepCollectionEquality()
                .equals(other._openedDetailedViews, _openedDetailedViews) &&
            const DeepCollectionEquality()
                .equals(other._openedPrimaryViews, _openedPrimaryViews) &&
            (identical(
                    other.navigateToCard2dPosition, navigateToCard2dPosition) ||
                other.navigateToCard2dPosition == navigateToCard2dPosition) &&
            (identical(other.openMenu, openMenu) ||
                other.openMenu == openMenu) &&
            (identical(other.mainScreen, mainScreen) ||
                other.mainScreen == mainScreen));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameStatus,
      const DeepCollectionEquality().hash(_openedDetailedViews),
      const DeepCollectionEquality().hash(_openedPrimaryViews),
      navigateToCard2dPosition,
      openMenu,
      mainScreen);

  @override
  String toString() {
    return 'CommandCenterState(gameStatus: $gameStatus, openedDetailedViews: $openedDetailedViews, openedPrimaryViews: $openedPrimaryViews, navigateToCard2dPosition: $navigateToCard2dPosition, openMenu: $openMenu, mainScreen: $mainScreen)';
  }
}

/// @nodoc
abstract mixin class _$CommandCenterStateCopyWith<$Res>
    implements $CommandCenterStateCopyWith<$Res> {
  factory _$CommandCenterStateCopyWith(
          _CommandCenterState value, $Res Function(_CommandCenterState) _then) =
      __$CommandCenterStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {CommandGameStatus gameStatus,
      Set<GameCardId> openedDetailedViews,
      Set<GameCardId> openedPrimaryViews,
      UiEvent<GameCardId>? navigateToCard2dPosition,
      UiEvent<Nothing>? openMenu,
      MainScreen mainScreen});
}

/// @nodoc
class __$CommandCenterStateCopyWithImpl<$Res>
    implements _$CommandCenterStateCopyWith<$Res> {
  __$CommandCenterStateCopyWithImpl(this._self, this._then);

  final _CommandCenterState _self;
  final $Res Function(_CommandCenterState) _then;

  /// Create a copy of CommandCenterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameStatus = null,
    Object? openedDetailedViews = null,
    Object? openedPrimaryViews = null,
    Object? navigateToCard2dPosition = freezed,
    Object? openMenu = freezed,
    Object? mainScreen = null,
  }) {
    return _then(_CommandCenterState(
      gameStatus: null == gameStatus
          ? _self.gameStatus
          : gameStatus // ignore: cast_nullable_to_non_nullable
              as CommandGameStatus,
      openedDetailedViews: null == openedDetailedViews
          ? _self._openedDetailedViews
          : openedDetailedViews // ignore: cast_nullable_to_non_nullable
              as Set<GameCardId>,
      openedPrimaryViews: null == openedPrimaryViews
          ? _self._openedPrimaryViews
          : openedPrimaryViews // ignore: cast_nullable_to_non_nullable
              as Set<GameCardId>,
      navigateToCard2dPosition: freezed == navigateToCard2dPosition
          ? _self.navigateToCard2dPosition
          : navigateToCard2dPosition // ignore: cast_nullable_to_non_nullable
              as UiEvent<GameCardId>?,
      openMenu: freezed == openMenu
          ? _self.openMenu
          : openMenu // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      mainScreen: null == mainScreen
          ? _self.mainScreen
          : mainScreen // ignore: cast_nullable_to_non_nullable
              as MainScreen,
    ));
  }
}

// dart format on
