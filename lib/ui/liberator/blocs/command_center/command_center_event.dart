import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/targeted_action.dart';

abstract class CommandCenterEvent {}

class TapPrimaryCardEvent extends CommandCenterEvent {
  final GameCardId cardId;

  TapPrimaryCardEvent(this.cardId);
}

class TapCardEvent extends CommandCenterEvent {
  final GameCardId cardId;

  TapCardEvent(this.cardId);
}

class CloseDetailViewEvent extends CommandCenterEvent {
  final GameCardId id;

  CloseDetailViewEvent(this.id);
}

class ClosePrimaryViewEvent extends CommandCenterEvent {
  final GameCardId id;

  ClosePrimaryViewEvent(this.id);
}

class TapActionEvent extends CommandCenterEvent {
  final CardAction action;
  final GameCardId cardId;

  TapActionEvent(this.action, this.cardId);
}

class TapTargetedActionEvent extends CommandCenterEvent {
  final TargetedAction targetedAction;

  TapTargetedActionEvent(this.targetedAction);
}

class CloseActiveActionEvent extends CommandCenterEvent {}
class OpenIdleCardsEvent extends CommandCenterEvent {}
class TurnStartEvent extends CommandCenterEvent {}
class TapEndTurnEvent extends CommandCenterEvent {}
class OpenMenuEvent extends CommandCenterEvent {}
class TapStartMatchEvent extends CommandCenterEvent {
  final dynamic gameConfig;
  final List<dynamic> players;

  TapStartMatchEvent({this.gameConfig, this.players = const []});
}
class TapMatchManagementEvent extends CommandCenterEvent {}
// class TapNewGroupForActiveActionEvent extends CommandCenterEvent {}