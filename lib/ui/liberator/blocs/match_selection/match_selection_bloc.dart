import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:common/models/game_match.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_state.dart';
import 'package:dauntless/ui/liberator/blocs/events/match_deleted_ui_event.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// BLoC that manages match selection, fetching, and creation
class MatchSelectionBloc
    extends Bloc<MatchSelectionEvent, MatchSelectionState> {
  final GameConfigUseCase _gameConfigUseCase;
  final ServerNotificationsUseCase _serverNotificationsUseCase;
  // final WebSocketManager _webSocketManager;
  
  /// Used for dispatching UI events to other blocs
  final _matchDeletedUIEventController = StreamController<MatchDeletedUiEvent>.broadcast();
  
  /// Stream of match deleted UI events
  Stream<MatchDeletedUiEvent> get matchDeletedEvents => _matchDeletedUIEventController.stream;

  // Used for triggering match events after selection
  // Currently not directly used in this class but kept for future integration
  // with game match functionality after match selection
  final MatchSelectionUseCase _matchSelectionUseCase;

  // TODO: is there a CLEANer/BLOCier way to do this?
  String get sourceName => _matchSelectionUseCase.name;

  MatchSelectionUseCase get useCase => _matchSelectionUseCase;

  // Subscription to use case updates for open matches
  StreamSubscription? _openMatchesSubscription;

  MatchSelectionBloc(this._gameConfigUseCase, this._matchSelectionUseCase, this._serverNotificationsUseCase)
      : super(const MatchSelectionState()) {
    on<ToggleServerProfileSelectionExpandedMatchEvent>(
        _onToggleServerProfileSelectionExpandedMatchEvent);
    on<LoadMatchSelectionDataEvent>(_onLoadData);
    // on<SelectMatchEvent>(_onSelectMatch);
    on<JoinSelectedMatchEvent>(_onJoinSelectedMatch);
    on<JoinMatchEvent>(_onJoinMatch);
    on<LeaveMatchEvent>(_onLeaveMatch);
    // on<CreateMatchFromTemplateEvent>(_onCreateMatchFromTemplate);
    // on<OpenMatchForJoiningEvent>(_onOpenMatchForJoining);
    on<SubscribeToOpenMatchesEvent>(_onSubscribeToOpenMatches);
    on<UnsubscribeFromOpenMatchesEvent>(_onUnsubscribeFromOpenMatches);
    on<ReceiveOpenMatchesUpdateEvent>(_onReceiveOpenMatchesUpdate);
    on<DeleteMatchEvent>(_onDeleteMatch);
  }

  void _onToggleServerProfileSelectionExpandedMatchEvent(
      ToggleServerProfileSelectionExpandedMatchEvent event,
      Emitter<MatchSelectionState> emit) {
    emit(state.copyWith(
        serverProfileSelectorExpanded:
            event.expanded ?? !state.serverProfileSelectorExpanded));
  }

  /// Load available matches and templates
  void _onLoadData(
    LoadMatchSelectionDataEvent event,
    Emitter<MatchSelectionState> emit,
  ) async {
    print('MatchSelectionBloc (${_matchSelectionUseCase.name}): _onLoadData called');
    emit(state.setLoading());

    try {
      // Step 1: Load game config (needed for fetching matches)
      final gameConfig =
          (await _gameConfigUseCase.loadLocalGameConfigs()).first;
      print('MatchSelectionBloc (${_matchSelectionUseCase.name}): Loaded game config: ${gameConfig.id}');

      // Step 2: Fetch open matches from the use case (agnostic to local/network)
      final openMatches = await _matchSelectionUseCase.fetchOpenMatches(gameConfig.id);
      print('MatchSelectionBloc (${_matchSelectionUseCase.name}): Fetched ${openMatches.length} matches');

      // Step 3: Subscribe to real-time updates if not already subscribed
      if (!state.isSubscribedToUpdates && _matchSelectionUseCase.isServerSource) {
        print('MatchSelectionBloc (${_matchSelectionUseCase.name}): Not subscribed yet, adding subscription event');
        add(SubscribeToOpenMatchesEvent());
      } else {
        print('MatchSelectionBloc (${_matchSelectionUseCase.name}): Already subscribed, skipping subscription event');
      }

      // Step 4: Update the state with the open matches
      emit(state
          .setLoaded()
          .copyWith(openMatches: openMatches)
          .clearError());
      print('MatchSelectionBloc (${_matchSelectionUseCase.name}): State updated with ${openMatches.length} matches');
    } catch (e) {
      print('MatchSelectionBloc (${_matchSelectionUseCase.name}): Error loading data: $e');
      emit(state.setError('Failed to load match data: $e'));
    }
  }

  /// Join the selected match
  void _onJoinSelectedMatch(
    JoinSelectedMatchEvent event,
    Emitter<MatchSelectionState> emit,
  ) async {
    emit(state.setLoading());

    try {
      final selectedMatch = null; // state.selectedMatch;

      if (selectedMatch == null) {
        emit(state.setError("No match selected"));
        return;
      }

      // Join match via the MatchSelectionUseCase
      final success = await _matchSelectionUseCase.joinMatch(selectedMatch.id);

      if (success) {
        // Dispatch event to the game match manager to load the match
        // Note: Use a simpler approach since LoadGameMatchEvent doesn't exist
        // We'll need to rely on game_match_manager's existing events
        print(
            'MatchSelectionBloc: Successfully joined match ${selectedMatch.id}');
        emit(state.setLoaded().clearError());
      } else {
        emit(state.setError("Failed to join match"));
      }
    } catch (e) {
      emit(state.setError("Error joining match: $e"));
    }
  }

  /// Create a new match from a template
  // void _onCreateMatchFromTemplate(
  //   CreateMatchFromTemplateEvent event,
  //   Emitter<MatchSelectionState> emit,
  // ) async {
  //   emit(state.setLoading());
  //
  //   try {
  //     print(
  //         'MatchSelectionBloc: Creating match from template: ${event.templateConfig.name}');
  //
  //     // Modify the template with the provided details
  //     final modifiedTemplate = event.templateConfig.copyWith(
  //       selectedGameMode: event.gameMode,
  //     );
  //
  //     // Call the match selection use case to create the match
  //     final createdMatch = await _matchSelectionUseCase.createMatchFromTemplate(
  //       matchConfig: modifiedTemplate,
  //       maxPlayers: event.maxPlayers,
  //       isOpenForJoining: event.openForJoining,
  //     );
  //
  //     if (createdMatch != null) {
  //       print('MatchSelectionBloc: Match created with ID: ${createdMatch.id}');
  //
  //       // Update state with the created match
  //       emit(state
  //           .copyWith(
  //             selectedMatch: createdMatch,
  //             processingStatus: ProcessingStatus.loaded,
  //           )
  //           .clearError());
  //
  //       // Open the match for joining if requested
  //       if (event.openForJoining) {
  //         add(OpenMatchForJoiningEvent(createdMatch.id));
  //       }
  //     } else {
  //       emit(state.setError("Failed to create match"));
  //     }
  //   } catch (e) {
  //     emit(state.setError("Error creating match: $e"));
  //   }
  // }

  /// Open the match for joining after creation
  // void _onOpenMatchForJoining(
  //   OpenMatchForJoiningEvent event,
  //   Emitter<MatchSelectionState> emit,
  // ) async {
  //   try {
  //     print('MatchSelectionBloc: Opening match for joining: ${event.matchId}');
  //
  //     // Call the use case to open the match
  //     final success =
  //         await _matchSelectionUseCase.openMatchForJoining(event.matchId);
  //
  //     if (success) {
  //       print('MatchSelectionBloc: Match successfully opened for joining');
  //
  //       // Refresh the available matches to show the newly opened match
  //       add(const RefreshAvailableMatchesEvent());
  //     } else {
  //       print('MatchSelectionBloc: Failed to open match for joining');
  //       emit(state.setError("Failed to open match for joining"));
  //     }
  //   } catch (e) {
  //     print('MatchSelectionBloc: Error opening match: $e');
  //     emit(state.setError("Error opening match: $e"));
  //   }
  // }

  /// Subscribe to open matches updates via websocket
  void _onSubscribeToOpenMatches(
    SubscribeToOpenMatchesEvent event,
    Emitter<MatchSelectionState> emit,
  ) async {
    if (state.isSubscribedToUpdates) return;

    try {
      // Subscribe to open matches updates via WebSocket
      await _serverNotificationsUseCase.subscribeToOpenMatches();
      
      // Listen for open matches updates from the WebSocket
      _openMatchesSubscription = _serverNotificationsUseCase.openMatchesUpdates.listen(
        (matches) {
          print('MatchSelectionBloc: Received update with ${matches.length} matches');
          add(ReceiveOpenMatchesUpdateEvent(matches));
        },
        onError: (error) {
          print('MatchSelectionBloc: Error in open matches stream: $error');
          add(ReceiveOpenMatchesUpdateEvent([]));
        },
      );

      // Update state to indicate we're subscribed
      emit(state.copyWith(
        isSubscribedToUpdates: true,
        processingStatus: ProcessingStatus.loaded,
      ));
      
      print('MatchSelectionBloc: Successfully subscribed to open matches updates');
    } catch (e) {
      print('MatchSelectionBloc: Failed to subscribe to open matches updates: $e');
      emit(state.setError("Failed to subscribe to open matches updates: $e"));
    }
  }

  /// Handle open matches updates from websocket
  void _onReceiveOpenMatchesUpdate(
    ReceiveOpenMatchesUpdateEvent event,
    Emitter<MatchSelectionState> emit,
  ) {
    // Get a set of match IDs from the new update
    final receivedMatchIds = {for (var match in event.matches) match.id};
    
    // Start with a map of the current matches
    final updatedMatchesMap = <String, GameMatch>{};
    
    // First, add all existing matches to the map
    for (final match in state.openMatches) {
      updatedMatchesMap[match.id] = match;
    }
    
    // Then add or update with matches from the new event
    for (final match in event.matches) {
      updatedMatchesMap[match.id] = match;
    }
    
    // Log information about new matches
    final currentIds = {for (var match in state.openMatches) match.id};
    final addedIds = receivedMatchIds.difference(currentIds);
    
    if (addedIds.isNotEmpty) {
      print('MatchSelectionBloc: New matches added: ${addedIds.join(', ')}');
    }
    
    // Convert back to a list
    final updatedMatches = updatedMatchesMap.values.toList();
    
    print('MatchSelectionBloc: Updated matches list - now contains ${updatedMatches.length} matches');
    emit(state.copyWith(openMatches: updatedMatches).clearError());
  }

  /// Unsubscribe from open matches updates
  void _onUnsubscribeFromOpenMatches(
    UnsubscribeFromOpenMatchesEvent event,
    Emitter<MatchSelectionState> emit,
  ) {
    // Cancel subscription if it exists
    _openMatchesSubscription?.cancel();
    _openMatchesSubscription = null;

    // Unsubscribe from WebSocket updates
    _serverNotificationsUseCase.unsubscribeFromOpenMatches();

    // Update state to reflect unsubscribed status
    emit(state.copyWith(
      isSubscribedToUpdates: false,
      processingStatus: ProcessingStatus.loaded,
      // We're keeping selectedMatchSelectionUseCase so the source name remains visible
      // until the panel is completely removed
      // Clear available matches since we're no longer subscribed
      openMatches: [],
    ));

    GetIt.I<ServerEnvironmentManager>().add(
      SetSelectedServerProfileEvent(null),
    );

    // Notify the MatchSelectionEnvironmentManager to remove this source
    final environmentManager = GetIt.I<MatchSelectionEnvironmentManager>();
    environmentManager.add(RemoveMatchSelectionUseCase(_matchSelectionUseCase.name));
  }

  /// Handle leaving a match event
  Future<void> _onLeaveMatch(
    LeaveMatchEvent event,
    Emitter<MatchSelectionState> emit,
  ) async {
    try {
      final matchId = event.matchId;
      print('MatchSelectionBloc: Processing leave match event for $matchId');

      // Get user ID from user manager
      final userManager = GetIt.instance<UserManager>();
      final userId = userManager.state.user?.id;

      if (userId == null || userId.isEmpty) {
        emit(state.setError('Cannot leave match: No valid user ID provided'));
        return;
      }

      // Call the use case to leave the match
      print('MatchSelectionBloc: Leaving match $matchId with user $userId');
      final result = await _matchSelectionUseCase.leaveMatch(matchId, playerId: userId);

      if (result) {
        print('MatchSelectionBloc: Successfully left match $matchId with user $userId');

        // IMPORTANT: We are now relying solely on WebSocket events to update the UI after join/leave
        // No explicit fetch will be performed here - UI will update when WebSocket events arrive
        print('MatchSelectionBloc: Waiting for WebSocket events to update UI after leave');
        
        // Just mark the processing as complete - actual data will come via WebSocket
        emit(state.copyWith(
          processingStatus: ProcessingStatus.loaded,
          errorMessage: null,
        ));
        
        print('MatchSelectionBloc: 🔔 Leave operation complete, waiting for WebSocket events for match data');
      } else {
        print('MatchSelectionBloc: Failed to leave match $matchId with user $userId');
        emit(state.setError('Failed to leave match $matchId'));
      }
    } catch (e, stack) {
      print('Error leaving match: $e');
      print(stack);
      emit(state.setError('Error leaving match: $e'));
    }
  }

  /// Join a match by ID
  void _onJoinMatch(
    JoinMatchEvent event,
    Emitter<MatchSelectionState> emit,
  ) async {
    // Update state to indicate we're processing
    emit(state.setLoading());

    try {
      // Get the match ID from the event
      final matchId = event.matchId;

      if (matchId.isEmpty) {
        emit(state.setError('Cannot join match with empty ID'));
        return;
      }

      // Get user ID from user manager
      final userManager = GetIt.instance<UserManager>();
      final userId = userManager.state.user?.id;

      if (userId == null || userId.isEmpty) {
        emit(state.setError('Cannot join match: No valid user ID provided'));
        return;
      }

      // Call the use case to join the match
      print('MatchSelectionBloc: Joining match $matchId with user $userId');
      final result = await _matchSelectionUseCase.joinMatch(matchId, playerId: userId);

      if (result) {
        print('MatchSelectionBloc: Successfully joined match $matchId with user $userId');
        
        // IMPORTANT: We are now relying solely on WebSocket events to update the UI after join/leave
        // No explicit fetch will be performed here - UI will update when WebSocket events arrive
        print('MatchSelectionBloc: Waiting for WebSocket events to update UI after join');
        
        // Just mark the processing as complete - actual data will come via WebSocket
        emit(state.copyWith(
          processingStatus: ProcessingStatus.loaded,
          errorMessage: null,
        ));
        
        print('MatchSelectionBloc: 🔔 Join operation complete, waiting for WebSocket events for match data');
        // Removed explicit fetching and logging of player slots - will get data from WebSocket
      } else {
        print('MatchSelectionBloc: Failed to join match $matchId with user $userId');
        emit(state.setError('Failed to join match $matchId'));
      }
    } catch (e, stack) {
      print('Error leaving match: $e');
      print(stack);
      emit(state.setError('Error leaving match: $e'));
    }
  }

  void _onDeleteMatch(
    DeleteMatchEvent event,
    Emitter<MatchSelectionState> emit,
  ) async {
    emit(state.setLoading());

    try {
      final matchId = event.matchId;

      if (matchId.isEmpty) {
        emit(state.setError('Cannot delete match with empty ID'));
        return;
      }

      // Call the use case to delete the match
      final success = await _matchSelectionUseCase.deleteMatch(matchId);

      if (success) {
        print('MatchSelectionBloc: Successfully deleted match $matchId');

        // Filter out the deleted match from the open matches list
        final updatedMatches = state.openMatches.where((match) => match.id != matchId).toList();
        print('MatchSelectionBloc: Removed match $matchId from UI list, ${updatedMatches.length} matches remaining');
        
        // Dispatch a UI event to notify other blocs that this match was deleted
        print('MatchSelectionBloc: Dispatching MatchDeletedUiEvent for match $matchId');
        _matchDeletedUIEventController.add(MatchDeletedUiEvent(matchId));
        
        // Update the state with the filtered list and clear loading status
        emit(state.copyWith(
          openMatches: updatedMatches,
          processingStatus: ProcessingStatus.loaded,
          errorMessage: null,
        ));
      } else {
        emit(state.setError('Failed to delete match $matchId'));
      }
    } catch (e) {
      emit(state.setError('Error deleting match: $e'));
    }
  }

  @override
  Future<void> close() {
    _openMatchesSubscription?.cancel();
    _matchDeletedUIEventController.close();
    return super.close();
  }
}
