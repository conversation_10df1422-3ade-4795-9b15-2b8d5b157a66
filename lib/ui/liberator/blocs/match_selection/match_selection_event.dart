import 'package:dauntless/models/base/game_match.dart';

/// Base event class for MatchSelectionBloc
abstract class MatchSelectionEvent {
  const MatchSelectionEvent();
}

class LoadAvailableMatchSelectionUseCases extends MatchSelectionEvent {}

/// Load available matches and match config templates
class LoadMatchSelectionDataEvent extends MatchSelectionEvent {
  const LoadMatchSelectionDataEvent();
}

/// Join the selected match
class JoinSelectedMatchEvent extends MatchSelectionEvent {
  const JoinSelectedMatchEvent();
}

class ToggleServerProfileSelectionExpandedMatchEvent extends MatchSelectionEvent {
  final bool? expanded;
  const ToggleServerProfileSelectionExpandedMatchEvent(this.expanded);
}

/// Create a new match from a template
// class CreateMatchFromTemplateEvent extends MatchSelectionEvent {
//   final MatchConfig templateConfig;
//   final GameMode gameMode;
//   final bool openForJoining;
//   final int maxPlayers;
//
//   const CreateMatchFromTemplateEvent({
//     required this.templateConfig,
//     required this.gameMode,
//     this.openForJoining = false,
//     this.maxPlayers = 2, // Default to 2 players if not specified
//   });
//
//   @override
//   List<Object?> get props => [templateConfig, gameMode, openForJoining, maxPlayers];
// }

/// Open the currently created match for joining (after creation)
// class OpenMatchForJoiningEvent extends MatchSelectionEvent {
//   final String matchId;
//
//   const OpenMatchForJoiningEvent(this.matchId);
//
//   @override
//   List<Object?> get props => [matchId];
// }

/// Subscribe to open matches updates via websocket
class SubscribeToOpenMatchesEvent extends MatchSelectionEvent {
  const SubscribeToOpenMatchesEvent();
}

/// Unsubscribe from open matches updates via websocket
class UnsubscribeFromOpenMatchesEvent extends MatchSelectionEvent {
  const UnsubscribeFromOpenMatchesEvent();
}

/// Receive open matches update from websocket
class ReceiveOpenMatchesUpdateEvent extends MatchSelectionEvent {
  final List<GameMatch> matches;
  
  const ReceiveOpenMatchesUpdateEvent(this.matches);
  
  // Note: No @override annotation needed since MatchSelectionEvent doesn't define props
  List<Object?> get props => [matches];
}

/// Join a specific match by ID
class JoinMatchEvent extends MatchSelectionEvent {
  final String matchId;

  const JoinMatchEvent({required this.matchId});
  
  // Note: No @override annotation needed since MatchSelectionEvent doesn't define props
  List<Object?> get props => [matchId];
}

class LeaveMatchEvent extends MatchSelectionEvent {
  final String matchId;

  const LeaveMatchEvent(this.matchId);

  // Note: No @override annotation needed since MatchSelectionEvent doesn't define props
  List<Object> get props => [matchId];
}

/// Fetch open matches from server (direct API call)
class FetchOpenMatchesEvent extends MatchSelectionEvent {
  const FetchOpenMatchesEvent();
}

/// Delete a match by ID
class DeleteMatchEvent extends MatchSelectionEvent {
  final String matchId;
  
  const DeleteMatchEvent({required this.matchId});
  
  // Note: No @override annotation needed since MatchSelectionEvent doesn't define props
  List<Object?> get props => [matchId];
}
