import 'package:dauntless/models/base/game_match.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_selection_state.freezed.dart';
part 'match_selection_state.g.dart';

/// State for the MatchSelectionBloc
@freezed
abstract class MatchSelectionState with _$MatchSelectionState {
  const factory MatchSelectionState({
    /// Processing status of the state
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
    
    /// Open matches that can be joined (from any source - local or network)
    @Default([]) List<GameMatch> openMatches,

    /// Whether we are subscribed to open matches updates via websocket
    @Default(false) bool isSubscribedToUpdates,
    @Default(false) bool serverProfileSelectorExpanded,

    /// Flag to indicate this source should be removed by parent
    @Default(false) bool shouldRemoveSource,

    /// Optional error message
    String? errorMessage,
  }) = _MatchSelectionState;

  factory MatchSelectionState.fromJson(Map<String, dynamic> json) =>
      _$MatchSelectionStateFromJson(json);
}

/// Helper functions to work with MatchSelectionState // TODO: determine if this is the structure we want to use; or if we should just use the copyWiths directly in the bloc
extension MatchSelectionStateHelpers on MatchSelectionState {
  /// Clear the error message
  MatchSelectionState clearError() {
    return (this as _$MatchSelectionState).copyWith(errorMessage: null);
  }
  /// Set to loading state
  MatchSelectionState setLoading() {
    return (this as _$MatchSelectionState).copyWith(processingStatus: ProcessingStatus.loading);
  }
  
  /// Set to loaded state
  MatchSelectionState setLoaded() {
    return (this as _$MatchSelectionState).copyWith(processingStatus: ProcessingStatus.loaded);
  }
  
  /// Set to error state
  MatchSelectionState setError(String message) {
    return (this as _$MatchSelectionState).copyWith(
      processingStatus: ProcessingStatus.error,
      errorMessage: message
    );
  }
}
