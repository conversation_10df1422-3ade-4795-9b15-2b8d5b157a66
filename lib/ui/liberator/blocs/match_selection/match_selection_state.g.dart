// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_selection_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchSelectionState _$MatchSelectionStateFromJson(Map<String, dynamic> json) =>
    _MatchSelectionState(
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      openMatches: (json['openMatches'] as List<dynamic>?)
              ?.map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isSubscribedToUpdates: json['isSubscribedToUpdates'] as bool? ?? false,
      serverProfileSelectorExpanded:
          json['serverProfileSelectorExpanded'] as bool? ?? false,
      shouldRemoveSource: json['shouldRemoveSource'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$MatchSelectionStateToJson(
        _MatchSelectionState instance) =>
    <String, dynamic>{
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'openMatches': instance.openMatches,
      'isSubscribedToUpdates': instance.isSubscribedToUpdates,
      'serverProfileSelectorExpanded': instance.serverProfileSelectorExpanded,
      'shouldRemoveSource': instance.shouldRemoveSource,
      'errorMessage': instance.errorMessage,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
