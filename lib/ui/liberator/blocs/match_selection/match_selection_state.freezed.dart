// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_selection_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchSelectionState {
  /// Processing status of the state
  ProcessingStatus get processingStatus;

  /// Open matches that can be joined (from any source - local or network)
  List<GameMatch> get openMatches;

  /// Whether we are subscribed to open matches updates via websocket
  bool get isSubscribedToUpdates;
  bool get serverProfileSelectorExpanded;

  /// Flag to indicate this source should be removed by parent
  bool get shouldRemoveSource;

  /// Optional error message
  String? get errorMessage;

  /// Create a copy of MatchSelectionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchSelectionStateCopyWith<MatchSelectionState> get copyWith =>
      _$MatchSelectionStateCopyWithImpl<MatchSelectionState>(
          this as MatchSelectionState, _$identity);

  /// Serializes this MatchSelectionState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchSelectionState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality()
                .equals(other.openMatches, openMatches) &&
            (identical(other.isSubscribedToUpdates, isSubscribedToUpdates) ||
                other.isSubscribedToUpdates == isSubscribedToUpdates) &&
            (identical(other.serverProfileSelectorExpanded,
                    serverProfileSelectorExpanded) ||
                other.serverProfileSelectorExpanded ==
                    serverProfileSelectorExpanded) &&
            (identical(other.shouldRemoveSource, shouldRemoveSource) ||
                other.shouldRemoveSource == shouldRemoveSource) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      const DeepCollectionEquality().hash(openMatches),
      isSubscribedToUpdates,
      serverProfileSelectorExpanded,
      shouldRemoveSource,
      errorMessage);

  @override
  String toString() {
    return 'MatchSelectionState(processingStatus: $processingStatus, openMatches: $openMatches, isSubscribedToUpdates: $isSubscribedToUpdates, serverProfileSelectorExpanded: $serverProfileSelectorExpanded, shouldRemoveSource: $shouldRemoveSource, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class $MatchSelectionStateCopyWith<$Res> {
  factory $MatchSelectionStateCopyWith(
          MatchSelectionState value, $Res Function(MatchSelectionState) _then) =
      _$MatchSelectionStateCopyWithImpl;
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      List<GameMatch> openMatches,
      bool isSubscribedToUpdates,
      bool serverProfileSelectorExpanded,
      bool shouldRemoveSource,
      String? errorMessage});
}

/// @nodoc
class _$MatchSelectionStateCopyWithImpl<$Res>
    implements $MatchSelectionStateCopyWith<$Res> {
  _$MatchSelectionStateCopyWithImpl(this._self, this._then);

  final MatchSelectionState _self;
  final $Res Function(MatchSelectionState) _then;

  /// Create a copy of MatchSelectionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? processingStatus = null,
    Object? openMatches = null,
    Object? isSubscribedToUpdates = null,
    Object? serverProfileSelectorExpanded = null,
    Object? shouldRemoveSource = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_self.copyWith(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      openMatches: null == openMatches
          ? _self.openMatches
          : openMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      isSubscribedToUpdates: null == isSubscribedToUpdates
          ? _self.isSubscribedToUpdates
          : isSubscribedToUpdates // ignore: cast_nullable_to_non_nullable
              as bool,
      serverProfileSelectorExpanded: null == serverProfileSelectorExpanded
          ? _self.serverProfileSelectorExpanded
          : serverProfileSelectorExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldRemoveSource: null == shouldRemoveSource
          ? _self.shouldRemoveSource
          : shouldRemoveSource // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchSelectionState implements MatchSelectionState {
  const _MatchSelectionState(
      {this.processingStatus = ProcessingStatus.start,
      final List<GameMatch> openMatches = const [],
      this.isSubscribedToUpdates = false,
      this.serverProfileSelectorExpanded = false,
      this.shouldRemoveSource = false,
      this.errorMessage})
      : _openMatches = openMatches;
  factory _MatchSelectionState.fromJson(Map<String, dynamic> json) =>
      _$MatchSelectionStateFromJson(json);

  /// Processing status of the state
  @override
  @JsonKey()
  final ProcessingStatus processingStatus;

  /// Open matches that can be joined (from any source - local or network)
  final List<GameMatch> _openMatches;

  /// Open matches that can be joined (from any source - local or network)
  @override
  @JsonKey()
  List<GameMatch> get openMatches {
    if (_openMatches is EqualUnmodifiableListView) return _openMatches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_openMatches);
  }

  /// Whether we are subscribed to open matches updates via websocket
  @override
  @JsonKey()
  final bool isSubscribedToUpdates;
  @override
  @JsonKey()
  final bool serverProfileSelectorExpanded;

  /// Flag to indicate this source should be removed by parent
  @override
  @JsonKey()
  final bool shouldRemoveSource;

  /// Optional error message
  @override
  final String? errorMessage;

  /// Create a copy of MatchSelectionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchSelectionStateCopyWith<_MatchSelectionState> get copyWith =>
      __$MatchSelectionStateCopyWithImpl<_MatchSelectionState>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchSelectionStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchSelectionState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality()
                .equals(other._openMatches, _openMatches) &&
            (identical(other.isSubscribedToUpdates, isSubscribedToUpdates) ||
                other.isSubscribedToUpdates == isSubscribedToUpdates) &&
            (identical(other.serverProfileSelectorExpanded,
                    serverProfileSelectorExpanded) ||
                other.serverProfileSelectorExpanded ==
                    serverProfileSelectorExpanded) &&
            (identical(other.shouldRemoveSource, shouldRemoveSource) ||
                other.shouldRemoveSource == shouldRemoveSource) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      const DeepCollectionEquality().hash(_openMatches),
      isSubscribedToUpdates,
      serverProfileSelectorExpanded,
      shouldRemoveSource,
      errorMessage);

  @override
  String toString() {
    return 'MatchSelectionState(processingStatus: $processingStatus, openMatches: $openMatches, isSubscribedToUpdates: $isSubscribedToUpdates, serverProfileSelectorExpanded: $serverProfileSelectorExpanded, shouldRemoveSource: $shouldRemoveSource, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class _$MatchSelectionStateCopyWith<$Res>
    implements $MatchSelectionStateCopyWith<$Res> {
  factory _$MatchSelectionStateCopyWith(_MatchSelectionState value,
          $Res Function(_MatchSelectionState) _then) =
      __$MatchSelectionStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      List<GameMatch> openMatches,
      bool isSubscribedToUpdates,
      bool serverProfileSelectorExpanded,
      bool shouldRemoveSource,
      String? errorMessage});
}

/// @nodoc
class __$MatchSelectionStateCopyWithImpl<$Res>
    implements _$MatchSelectionStateCopyWith<$Res> {
  __$MatchSelectionStateCopyWithImpl(this._self, this._then);

  final _MatchSelectionState _self;
  final $Res Function(_MatchSelectionState) _then;

  /// Create a copy of MatchSelectionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? processingStatus = null,
    Object? openMatches = null,
    Object? isSubscribedToUpdates = null,
    Object? serverProfileSelectorExpanded = null,
    Object? shouldRemoveSource = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_MatchSelectionState(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      openMatches: null == openMatches
          ? _self._openMatches
          : openMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      isSubscribedToUpdates: null == isSubscribedToUpdates
          ? _self.isSubscribedToUpdates
          : isSubscribedToUpdates // ignore: cast_nullable_to_non_nullable
              as bool,
      serverProfileSelectorExpanded: null == serverProfileSelectorExpanded
          ? _self.serverProfileSelectorExpanded
          : serverProfileSelectorExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldRemoveSource: null == shouldRemoveSource
          ? _self.shouldRemoveSource
          : shouldRemoveSource // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
