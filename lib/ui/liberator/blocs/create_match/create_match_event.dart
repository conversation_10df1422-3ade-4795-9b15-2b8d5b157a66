import 'package:common/models/game_match.dart';
import 'package:common/models/player_type.dart';
import 'package:equatable/equatable.dart';

/// Events for the CreateMatchBloc
abstract class CreateMatchEvent extends Equatable {
  const CreateMatchEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize the bloc with configuration data
class InitializeCreateMatchEvent extends CreateMatchEvent {
  const InitializeCreateMatchEvent();
}

/// Update the selected match configuration
class SelectMatchConfigEvent extends CreateMatchEvent {
  final String matchConfigId;

  const SelectMatchConfigEvent(this.matchConfigId);

  @override
  List<Object?> get props => [matchConfigId];
}

/// Add a new player slot
class AddPlayerSlotEvent extends CreateMatchEvent {
  const AddPlayerSlotEvent();
}

/// Remove a player slot at the specified index
class RemovePlayerSlotEvent extends CreateMatchEvent {
  final int slotIndex;

  const RemovePlayerSlotEvent(this.slotIndex);

  @override
  List<Object?> get props => [slotIndex];
}

/// Update a player slot's type to a specific value
class UpdatePlayerTypeEvent extends CreateMatchEvent {
  final int slotIndex;
  final PlayerType playerType;

  const UpdatePlayerTypeEvent(this.slotIndex, this.playerType);

  @override
  List<Object?> get props => [slotIndex, playerType];
}

/// Update a player's name
class UpdatePlayerNameEvent extends CreateMatchEvent {
  final int slotIndex;
  final String name;

  const UpdatePlayerNameEvent(this.slotIndex, this.name);

  @override
  List<Object?> get props => [slotIndex, name];
}

/// Reset the matchCreated flag after handling it
class ResetMatchCreatedFlagEvent extends CreateMatchEvent {
  const ResetMatchCreatedFlagEvent();
}

/// Update the game name
class UpdateGameNameEvent extends CreateMatchEvent {
  final String gameName;

  const UpdateGameNameEvent(this.gameName);

  @override
  List<Object?> get props => [gameName];
}

/// Set a player slot as the host player
class SetHostPlayerEvent extends CreateMatchEvent {
  final String slotId;

  const SetHostPlayerEvent(this.slotId);

  @override
  List<Object?> get props => [slotId];
}

class SelectMatchEvent extends CreateMatchEvent {
  final GameMatch match;

  const SelectMatchEvent(this.match);

  @override
  List<Object?> get props => [match];
}

/// Handle changes in network capability availability
class NetworkCapabilityChangedEvent extends CreateMatchEvent {
  final bool hasNetworkCapability;

  const NetworkCapabilityChangedEvent(this.hasNetworkCapability);

  @override
  List<Object?> get props => [hasNetworkCapability];
}

/// Clear the selected match when it's deleted
class ClearSelectedMatchEvent extends CreateMatchEvent {
  final String matchId;

  const ClearSelectedMatchEvent(this.matchId);

  @override
  List<Object?> get props => [matchId];
}

/// Event to start a match with the current configuration
class StartMatchEvent extends CreateMatchEvent {
  const StartMatchEvent();
}

/// Event to join the current user to a player slot
class JoinPlayerEvent extends CreateMatchEvent {
  final int slotIndex;

  const JoinPlayerEvent(this.slotIndex);

  @override
  List<Object?> get props => [slotIndex];
}
