import 'dart:async';

import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_state.dart';
import 'package:dauntless/frameworks/game_config/game_config_event.dart';
import 'package:dauntless/frameworks/game_config/game_config_manager.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/base/game_match.dart';
import 'package:dauntless/ui/liberator/blocs/events/match_deleted_ui_event.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_event.dart';
import 'package:dauntless/ui/liberator/blocs/create_match/create_match_state.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_bloc.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/repositories/server_repository.dart';

/// BLoC that manages the state of the Create Match Screen
class CreateMatchBloc extends Bloc<CreateMatchEvent, CreateMatchState> {
  final GameConfigUseCase _gameConfigUseCase;
  final MatchSelectionEnvironmentManager _matchSelectionEnvironmentManager;
  final RemoteLogger _logger;
  StreamSubscription<MatchSelectionEnvironmentState>? _environmentSubscription;
  
  /// Subscription for match deleted UI events
  StreamSubscription<MatchDeletedUiEvent>? _matchDeletedSubscription;
  
  /// Subscription for open matches updates from websocket
  StreamSubscription<List<GameMatch>>? _openMatchesUpdatesSubscription;
  
  /// Stream controller for match started events
  final _matchStartedController = StreamController<String>.broadcast();
  
  /// Stream of match started events (match ID)
  Stream<String> get matchStartedEvents => _matchStartedController.stream;

  CreateMatchBloc(
      this._gameConfigUseCase, this._matchSelectionEnvironmentManager, this._logger)
      : super(const CreateMatchState()) {
    on<InitializeCreateMatchEvent>(_onInitialize);
    on<SelectMatchConfigEvent>(_onSelectMatchConfig);
    on<AddPlayerSlotEvent>(_onAddPlayerSlot);
    on<RemovePlayerSlotEvent>(_onRemovePlayerSlot);
    on<UpdatePlayerTypeEvent>(_onUpdatePlayerType);
    on<UpdatePlayerNameEvent>(_onUpdatePlayerName);
    on<SetHostPlayerEvent>(_onSetHostPlayer);
    on<SelectMatchEvent>(_onSelectMatchEvent);
    on<NetworkCapabilityChangedEvent>(_onNetworkCapabilityChanged);
    on<ClearSelectedMatchEvent>(_onClearSelectedMatch);
    on<UpdateGameNameEvent>(_onUpdateGameName);
    on<StartMatchEvent>(_onStartMatch);
    on<JoinPlayerEvent>(_onJoinPlayer);

    // Listen to match selection environment changes
    _environmentSubscription = _matchSelectionEnvironmentManager.stream.listen((envState) {
      final hasNetworkCapability = _hasNetworkCapability;
      if (state.hasNetworkCapability != hasNetworkCapability) {
        add(NetworkCapabilityChangedEvent(hasNetworkCapability));
      }
    });
    
    // Subscribe to match deleted UI events from any network match selection bloc
    try {
      final MatchSelectionBloc? networkBloc = _matchSelectionEnvironmentManager.getMatchSelectionBloc('Network');
      if (networkBloc != null) {
        _logger.info('Subscribing to match deleted events from Network match selection bloc');
        _matchDeletedSubscription = networkBloc.matchDeletedEvents.listen(_onMatchDeleted);
      }
    } catch (e) {
      _logger.error('Error subscribing to match deleted events: $e');
    }
    
    // Subscribe to open matches updates from ServerNotificationsUseCase
    try {
      final serverNotificationsUseCase = GetIt.I<ServerNotificationsUseCase>();
      _logger.info('CreateMatchBloc: ⚠️ Subscribing to open matches updates from ServerNotificationsUseCase');
      
      // Log subscription information
      _logger.info('CreateMatchBloc: 🔍 Checking open_matches subscription status');
      // Note: Direct WebSocketRepository reference removed due to import issues
      
      // Subscribe with detailed callback tracing
      _openMatchesUpdatesSubscription = serverNotificationsUseCase.openMatchesUpdates.listen(
        (matches) {
          _logger.info('CreateMatchBloc: 🎯 WebSocket callback received with ${matches.length} matches');
          if (matches.isEmpty && state.matchId != null) {
            _logger.info('CreateMatchBloc: 🎯 WebSocket callback received with no matches');
              add(ClearSelectedMatchEvent(state.matchId!));
          } else {
            _logger.info('CreateMatchBloc: 🎯 WebSocket callback received with ${matches.length} matches');
            _onOpenMatchesUpdate(matches);
          }
        },
        onError: (error) {
          _logger.error('CreateMatchBloc: 🛑 WebSocket stream error: $error');
        },
        onDone: () {
          _logger.info('CreateMatchBloc: 🏁 WebSocket stream closed');
        },
      );
      _logger.info('CreateMatchBloc: ✅ Successfully subscribed to openMatchesUpdates stream');
    } catch (e) {
      _logger.error('CreateMatchBloc: 🛑 Error subscribing to open matches updates: $e');
    }
  }

  @override
  Future<void> close() {
    _environmentSubscription?.cancel();
    _matchDeletedSubscription?.cancel();
    _openMatchesUpdatesSubscription?.cancel();
    _matchStartedController.close();
    return super.close();
  }
  
  /// Handle match deleted UI event
  void _onMatchDeleted(MatchDeletedUiEvent event) {
    final deletedMatchId = event.matchId;
    _logger.info('Match deleted event received for match $deletedMatchId');
    
    // If this is the selected match, clear it
    if (state.matchId == deletedMatchId) {
      add(ClearSelectedMatchEvent(deletedMatchId));
      _logger.info('Selected match was deleted, clearing selection');
    }
  }
  
  /// Handle open matches updates from websocket
  void _onOpenMatchesUpdate(List<GameMatch> matches) {
    _logger.info('CreateMatchBloc: 📥 Open matches update received with ${matches.length} matches');
    print('CreateMatchBloc: 📥 Open matches update received with ${matches.length} matches'); // Console log for immediate visibility
    
    // Dump all received matches for debugging
    for (var i = 0; i < matches.length; i++) {
      final match = matches[i];
      _logger.info('CreateMatchBloc: Match[$i] = ${match.id} (${match.gameTypeId})');
      _logger.info('CreateMatchBloc: Match[$i] has ${match.playerSlots.length} player slots');
      for (var j = 0; j < match.playerSlots.length; j++) {
        final slot = match.playerSlots[j];
        _logger.info('CreateMatchBloc: Match[$i] Slot[$j] = ${slot.id}, playerId=${slot.playerId ?? "null"}');
      }
    }
    
    // If we have a selected match, check if it's in the updated matches and update our state
    final selectedMatchId = state.matchId;
    if (selectedMatchId != null && selectedMatchId.isNotEmpty) {
      _logger.info('CreateMatchBloc: Currently selected match: $selectedMatchId');
      
      // Try to find the selected match in the updated matches
      GameMatch? updatedMatch;
      try {
        updatedMatch = matches.firstWhere(
          (match) => match.id == selectedMatchId,
          orElse: () => GameMatch(
            id: '',
            status: MatchStatus.open,
            playerSlots: [],
            gameTypeId: '',
            creatorId: '',
            createdAt: 0,
            updatedAt: 0
          ),
        );
      } catch (e) {
        _logger.error('CreateMatchBloc: Error finding match: $e');
        return;
      }
      
      // If found, update our state with the latest match data
      if (updatedMatch.id.isNotEmpty) {
        _logger.info('CreateMatchBloc: Found updated data for selected match $selectedMatchId');
        
        // Log the current state player slots
        _logger.info('CreateMatchBloc: Current state player slots: ${state.playerSlots.length}');
        for (var i = 0; i < state.playerSlots.length; i++) {
          final slot = state.playerSlots[i];
          _logger.info('CreateMatchBloc: Current Slot[$i] = ${slot.id}, playerId=${slot.playerId ?? "null"}');
        }
        
        // Log the updated match player slots
        _logger.info('CreateMatchBloc: Updated match player slots: ${updatedMatch.playerSlots.length}');
        for (var i = 0; i < updatedMatch.playerSlots.length; i++) {
          final slot = updatedMatch.playerSlots[i];
          _logger.info('CreateMatchBloc: Updated Slot[$i] = ${slot.id}, playerId=${slot.playerId ?? "null"}');
        }
        
        // Always update on WebSocket updates - this is more reliable than checking for changes
        _logger.info('CreateMatchBloc: Updating selected match with latest data');
        add(SelectMatchEvent(updatedMatch));
      } else {
        _logger.warn('CreateMatchBloc: Selected match $selectedMatchId not found in updated matches');
      }
    } else {
      _logger.info('CreateMatchBloc: No selected match to update');
    }
  }
  
  // Removed _havePlayerSlotsChanged method since we now always update on WebSocket events

  /// Check if network match functionality is available
  bool get _hasNetworkCapability => 
    _matchSelectionEnvironmentManager.getMatchSelectionBloc('Network') != null;

  /// Get the NetworkMatchSelectionUseCase if available
  NetworkMatchSelectionUseCase? get _networkMatchSelectionUseCase => 
    (_matchSelectionEnvironmentManager.getMatchSelectionBloc('Network'))?.useCase as NetworkMatchSelectionUseCase?;
  /// Fetch open matches for a given game ID
  Future<List<GameMatch>> getOpenMatches(String gameId) async {
    if (!_hasNetworkCapability) {
      _logger.warn('Network match selection not available - cannot fetch open matches');
      return [];
    }

    try {
      final matches = await _networkMatchSelectionUseCase!.fetchOpenMatches(gameId);
      return matches.where((match) => match.id == gameId).toList();
    } catch (e) {
      _logger.error('Error fetching open matches: $e');
      return [];
    }
  }

  /// Initialize the bloc with available match configurations
  void _onInitialize(
    InitializeCreateMatchEvent event,
    Emitter<CreateMatchState> emit,
  ) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));

    try {
      // Load available match configurations
      final availableConfigs = await _gameConfigUseCase.loadLocalGameConfigs();
      emit(state.copyWith(
        availableConfigs: availableConfigs,
        processingStatus: ProcessingStatus.loaded,
        hasNetworkCapability: _hasNetworkCapability,
        matchId: null,
        selectedConfigId: null,
        gameName: null,
        playerSlots: [],
        players: [],
      ));
      if (availableConfigs.isNotEmpty) {
        add(SelectMatchConfigEvent(availableConfigs.first.id));
      }
    } catch (e) {
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
      ));
    }
  }

  /// Select a specific match configuration
  void _onSelectMatchConfig(
    SelectMatchConfigEvent event,
    Emitter<CreateMatchState> emit,
  ) async {
    if (state.processingStatus == ProcessingStatus.loading) return;

    try {
      emit(state.copyWith(
        processingStatus: ProcessingStatus.loading,
        selectedConfigId: event.matchConfigId,
      ));

      final selectedConfig =
          await _gameConfigUseCase.getGameConfigById(event.matchConfigId);
      final playerSlots = selectedConfig?.playerClasses
              .map((playerClass) => PlayerSlot.fromPlayerClass(playerClass))
              .toList() ??
          [];

      // Check if we can fetch open matches (requires network capability)
      List<GameMatch> availableMatches = [];
      bool hasOpenMatches = false;
      if (_hasNetworkCapability) {
        try {
          availableMatches = await getOpenMatches(event.matchConfigId);
          hasOpenMatches = availableMatches.isNotEmpty;
          if (hasOpenMatches) {
            _logger.info('Found existing open matches for ${event.matchConfigId}');
          }
        } catch (e) {
          _logger.warn('Failed to fetch open matches: $e');
        }
      }

      emit(state.copyWith(
        selectedConfigId: event.matchConfigId,
        processingStatus: ProcessingStatus.loaded,
        playerSlots: playerSlots
      ));
    } catch (e) {
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
      ));
    }
  }

  /// Add a new player slot
  void _onAddPlayerSlot(
    AddPlayerSlotEvent event,
    Emitter<CreateMatchState> emit,
  ) {
    if (state.canAddSlot) {
      final newSlots = List<PlayerSlot>.from(state.playerSlots ?? []);
      // newSlots.add(PlayerSlot.empty('player${state.playerSlots.length + 1}'));
      emit(state.copyWith(playerSlots:  newSlots));
    }
  }

  /// Remove the last player slot
  void _onRemovePlayerSlot(
    RemovePlayerSlotEvent event,
    Emitter<CreateMatchState> emit,
  ) {
    if (state.canRemoveSlot) {
      final newSlots = List<PlayerSlot>.from(state.playerSlots ?? []);
      newSlots.removeLast();
      emit(state.copyWith(playerSlots:  newSlots));
    }
  }

  /// Set a player slot's type to a specific value
  Future<void> _onUpdatePlayerType(
    UpdatePlayerTypeEvent event,
    Emitter<CreateMatchState> emit,
  ) async {
    // Don't allow changing the host if this is slotIndex 0
    // if (event.slotIndex == 0 && state.playerSlots[0].isHost) return;

    // Ensure the index is valid
    // if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length)
    //   return;

    final slots = List<PlayerSlot>.from(state.playerSlots ?? []);
    final slot = slots[event.slotIndex];
    final currentUserId = GetIt.I<UserManager>().state.user?.id;

    // Update the slot with the new type and name
    // Important: Updating player type should not automatically assign a player ID
    // Player ID assignment happens through the join player functionality
    slots[event.slotIndex] = slot.copyWith(
      type: event.playerType,
      // Keep the existing player ID - don't change it when updating type
      // This preserves the separation between type updates and joining
    );

    _logger.info('Updated player ${event.slotIndex} type to ${event.playerType}');
  print('PLAYER_TYPE_DEBUG: Updating slot ${event.slotIndex} from ${slot.type} to ${event.playerType}');

    // Emit initial state change to show updated type immediately
    emit(state.copyWith(
      playerSlots: slots,
      processingStatus: ProcessingStatus.loading,
    ));

    // If player type is humanNetwork and we haven't created a server match yet,
    // we need to create one
    bool needsServerMatch = event.playerType == PlayerType.humanNetwork &&
        (state.matchId == null ||
            !(await getOpenMatches(state
                .matchId!)).isNotEmpty);

    if (needsServerMatch) {
      try {
        final result = await _createServerMatch();
        // After creating the server match, update state with new match ID
        emit(state.copyWith(
          matchId: result.matchId,
          processingStatus: ProcessingStatus.loaded,
        ));
      } catch (e) {
        _logger.error('Failed to create server match: $e');
        emit(state.copyWith(
          processingStatus: ProcessingStatus.error,
        ));
        return;
      }
    } else if (state.hasNetworkCapability && state.matchId != null) {
      // We already have a server match, so just update the player slots
      try {
        final serverRepository = GetIt.I<ServerRepository>();
        
        // Update the player slots on the server
        print('PLAYER_TYPE_DEBUG: Sending player slots to server:');
        for (int i = 0; i < slots.length; i++) {
          print('PLAYER_TYPE_DEBUG: Slot $i - Type: ${slots[i].type}, ID: ${slots[i].id}, PlayerID: ${slots[i].playerId}');
        }
        
        await serverRepository.updateMatchPlayerSlots(
          state.matchId!,
          slots,
          currentUserId ?? '',
        );
        
        _logger.info('Successfully updated player type on server for match ${state.matchId}');
        print('PLAYER_TYPE_DEBUG: Server update completed successfully');
        emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
      } catch (e) {
        _logger.error('Failed to update player type on server: $e');
        emit(state.copyWith(processingStatus: ProcessingStatus.error));
        // Note: We keep the local state changes even if server update fails
      }
    } else {
      // For local-only matches or no network capability
      emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
    }
  }

  /// Update a player's name
  void _onUpdatePlayerName(
    UpdatePlayerNameEvent event,
    Emitter<CreateMatchState> emit,
  ) {
    // Ensure the index is valid
    if (event.slotIndex < 0 || event.slotIndex >= (state.playerSlots.length ?? 0)) {
      return;
    }

    // Don't update if the name is empty or only whitespace
    if (event.name.trim().isEmpty) return;

    final slots = List<PlayerSlot>.from(state.playerSlots ?? []);
    final slot = slots[event.slotIndex];

    // Update the player name
    slots[event.slotIndex] = slot.copyWith(
      name: event.name,
    );

    _logger.info('Updated player ${event.slotIndex} name to ${event.name}');
    emit(state.copyWith(playerSlots:  slots));
  }

  /// Helper method to create a server match
  Future<({bool created, String? matchId})> _createServerMatch() async {
    try {
      final gameConfig = state.selectedConfig;

      if (gameConfig == null) {
        throw Exception('No game configuration selected');
      }

      final user = GetIt.I<UserManager>().state.user;
      if (user == null) {
        throw Exception('No user logged in');
      }

      // Create the match using the enhanced DTO
      final createdMatch = await _networkMatchSelectionUseCase!.openNewMatch(
        GameMatch(
          id: GenerateIdIfNeededConverter().fromJson(),
          gameTypeId: gameConfig.id,
          playerSlots: state.playerSlots ?? [],
          status: MatchStatus.open,
          creatorId: user.id,
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        ),
        gameConfig,
      );

      if (createdMatch != null) {
        _logger.info('Server match created with ID: ${createdMatch.id}');
        return (created: true, matchId: createdMatch.id);
      } else {
        _logger.warn('Failed to create match on server');
        return (created: false, matchId: null);
      }
    } catch (e) {
      _logger.error('Error creating server match: $e');
      rethrow;
    }
  }

  void _onSetHostPlayer(
    SetHostPlayerEvent event,
    Emitter<CreateMatchState> emit,
  ) {
    _logger.info('Setting host player ID to ${event.slotId}'); // TODO: figure out/ impelement
    // emit(state.copyWith(hostId: event.slotId));
  }

  void _onSelectMatchEvent(
    SelectMatchEvent event,
    Emitter<CreateMatchState> emit,
  ) async {
    _logger.info('Match selected: ${event.match.id}');
    
    // Update state with the selected match details
    emit(state.copyWith(
      selectedConfigId: event.match.gameTypeId,
      gameName: event.match.gameName ?? 'Match ${event.match.id}',
      playerSlots: event.match.playerSlots,
      matchId: event.match.id,
      processingStatus: ProcessingStatus.loaded,
    ));
  }

  void _onNetworkCapabilityChanged(
    NetworkCapabilityChangedEvent event,
    Emitter<CreateMatchState> emit,
  ) {
    List<PlayerSlot> updatedSlots = state.playerSlots ?? [];
    
    // If network capability is lost, convert network player types to local equivalents
    if (!event.hasNetworkCapability) {
      updatedSlots = (state.playerSlots ?? []).map((slot) {
        switch (slot.type) {
          case PlayerType.humanNetwork:
            return slot.copyWith(type: PlayerType.humanLocal);
          case PlayerType.botNetwork:
            return slot.copyWith(type: PlayerType.botLocal);
          default:
            return slot;
        }
      }).toList();
    }
    
    emit(state.copyWith(
      hasNetworkCapability: event.hasNetworkCapability,
      // playerSlots: updatedSlots,
      playerSlots:  updatedSlots),
    );
  }
  
  /// Clear the selected match if it's deleted
  void _onClearSelectedMatch(
    ClearSelectedMatchEvent event,
    Emitter<CreateMatchState> emit,
  ) {
    // Check if the deleted match ID matches our currently selected match
    if (state.matchId == event.matchId) {
      _logger.info('Selected match ${event.matchId} was deleted, clearing selection');
      
      // Clear the match selection by resetting relevant fields
      emit(CreateMatchState().copyWith(
        processingStatus: ProcessingStatus.loaded,
      ));
      
      // Re-select the current game config to reset player slots to defaults
      if (state.selectedConfigId != null) {
        add(SelectMatchConfigEvent(state.selectedConfigId!));
      }
    }
  }
  
  /// Update the game name
  void _onUpdateGameName(
    UpdateGameNameEvent event,
    Emitter<CreateMatchState> emit,
  ) {
    _logger.info('Updating game name to: ${event.gameName}');
    emit(state.copyWith(gameName: event.gameName));
  }
  
  // / Start a match with the current configuration
  Future<void> _onStartMatch(
    StartMatchEvent event,
    Emitter<CreateMatchState> emit,
  ) async {
    _logger.info('Starting match with current configuration');
    
    // Validate that we have a valid configuration and player slots
    if (state.selectedConfig == null) {
      _logger.warn('Cannot start match: No game configuration selected');
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
      ));
      return;
    }
    
    if (!state.areAllRequiredSlotsValid) {
      _logger.warn('Cannot start match: Not all required player slots are valid');
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
      ));
      return;
    }
    
    try {
      emit(state.copyWith(
        processingStatus: ProcessingStatus.loading,
      ));
      
      // Load the selected game config into GameConfigManager before starting the match
      final gameConfigManager = GetIt.instance<GameConfigManager>();
      _logger.info('Loading game config before starting match: ${state.selectedConfig!.id}');
      final basePath = 'games/${state.selectedConfig!.id}';
      gameConfigManager.add(LoadGameConfigEvent(basePath));
      
      // Create the match on the server if network capability is available
      if (state.hasNetworkCapability && _networkMatchSelectionUseCase != null) {
        final result = await _createServerMatch();
        if (result.created && result.matchId != null) {
          _logger.info('Network match created with ID: ${result.matchId}');
          emit(state.copyWith(
            processingStatus: ProcessingStatus.loaded,
          ));
          
          // Notify listeners that a match has been started
          _matchStartedController.add(result.matchId!);
          return;
        }
      } else {
        // For local matches, we can just mark it as created
        _logger.info('Local match created');
        emit(state.copyWith(
          processingStatus: ProcessingStatus.loaded,
        ));
      }
    } catch (e) {
      _logger.error('Error starting match: $e');
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
      ));
    }
  }
  
  /// Join the current user to a player slot
  Future<void> _onJoinPlayer(
    JoinPlayerEvent event,
    Emitter<CreateMatchState> emit,
  ) async {
    _logger.info('Joining player to slot at index: ${event.slotIndex}');
    
    // Get the current user ID from UserManager
    final userManager = GetIt.I<UserManager>();
    final currentUserId = userManager.state.user?.id;
    
    if (currentUserId == null || currentUserId.isEmpty) {
      _logger.warn('Cannot join player: No current user ID available');
      return;
    }
    
    if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length) {
      _logger.warn('Cannot join player: Invalid slot index ${event.slotIndex}');
      return;
    }
    
    // Start with current slots and look for the user in any existing slot
    final updatedSlots = List<PlayerSlot>.from(state.playerSlots);
    
    // Set processingStatus to loading to indicate work in progress
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    
    // Check if the user is already in any match and leave it first
    if (state.hasNetworkCapability) {
      try {
        final serverRepository = GetIt.I<ServerRepository>();
        final networkMatchSelectionUseCase = _networkMatchSelectionUseCase;
        
        if (networkMatchSelectionUseCase != null) {
          // Fetch all open matches to check if user is in any
          final openMatches = await networkMatchSelectionUseCase.fetchOpenMatches(
            state.selectedConfigId ?? ''
          );
          
          // Check each match to see if the user is already in it
          for (final match in openMatches) {
            // Skip the current match if we're already in it
            if (match.id == state.matchId) continue;
            
            // Detailed logging of player slots for debugging
            print('USER_MATCH_STATUS: Checking match ${match.id} for user $currentUserId');
            print('USER_MATCH_STATUS: Match has ${match.playerSlots.length} player slots');
            
            // Check each slot individually and log it
            bool foundUserInMatch = false;
            String slotId = '';
            for (int i = 0; i < match.playerSlots.length; i++) {
              final slot = match.playerSlots[i];
              print('USER_MATCH_STATUS: Slot $i (${slot.id}) has playerId: "${slot.playerId}"');
              
              if (slot.playerId == currentUserId) {
                foundUserInMatch = true;
                slotId = slot.id;
                print('USER_MATCH_STATUS: Found user $currentUserId in slot ${slot.id}');
                break;
              }
            }
            
            // If user is in another match, leave it first
            if (foundUserInMatch) {
              _logger.info('User $currentUserId is already in match ${match.id} in slot $slotId, leaving before joining new match');
              print('USER_MATCH_STATUS: User $currentUserId is leaving match ${match.id} from slot $slotId before joining a new one');
              
              // Leave the match on the server
              try {
                // Log which slot we're removing the user from
                print('USER_MATCH_STATUS: About to call leaveMatch for user $currentUserId from slot $slotId in match ${match.id}');
                
                // Call leaveMatch on the server
                await serverRepository.leaveMatch(match.id, currentUserId);
                _logger.info('User $currentUserId successfully left match ${match.id}');
                print('USER_MATCH_STATUS: User $currentUserId successfully left match ${match.id}');
              } catch (e) {
                _logger.error('Error leaving match: $e');
                print('USER_MATCH_STATUS: Error leaving match: $e');
              }
            }
          }
        }
      } catch (e) {
        _logger.error('Error checking/leaving current matches: $e');
        print('USER_MATCH_STATUS: Error checking/leaving current matches: $e');
        // Continue with the join operation even if there was an error leaving other matches
      }
    }
    
    // First, remove user from any other slots they're already in (within this match)
    for (int i = 0; i < updatedSlots.length; i++) {
      if (i != event.slotIndex && updatedSlots[i].playerId == currentUserId) {
        _logger.info('Removing user $currentUserId from slot $i before joining new slot');
        updatedSlots[i] = updatedSlots[i].copyWith(playerId: '');
      }
    }
    
    // Then, add user to the requested slot
    final slot = updatedSlots[event.slotIndex];
    updatedSlots[event.slotIndex] = slot.copyWith(playerId: currentUserId);
    
    // Update local state with the new player assignments
    emit(state.copyWith(
      playerSlots: updatedSlots,
      processingStatus: ProcessingStatus.loading
    ));
    
    // Update server if we have a matchId and network capability
    if (state.hasNetworkCapability && state.matchId != null) {
      try {
        final serverRepository = GetIt.I<ServerRepository>();
        
        // Update the match with the new player slots configuration
        await serverRepository.updateMatchPlayerSlots(
          state.matchId!,
          updatedSlots,
          currentUserId
        );
        
        _logger.info('Successfully updated match ${state.matchId} on server with new player assignments');
        emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
      } catch (e) {
        _logger.error('Failed to update match on server: $e');
        emit(state.copyWith(processingStatus: ProcessingStatus.error));
        // Note: We don't revert the local state as it would be confusing to users
      }
    } else {
      // For local matches, just update the state
      emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
    }
  }
}
