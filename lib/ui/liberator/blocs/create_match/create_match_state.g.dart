// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_match_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CreateMatchState _$CreateMatchStateFromJson(Map<String, dynamic> json) =>
    _CreateMatchState(
      availableConfigs: (json['availableConfigs'] as List<dynamic>?)
              ?.map((e) => GameConfig.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      selectedConfigId: json['selectedConfigId'] as String?,
      gameName: json['gameName'] as String?,
      matchId: json['matchId'] as String?,
      playerSlots: (json['playerSlots'] as List<dynamic>?)
              ?.map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      players: (json['players'] as List<dynamic>?)
              ?.map((e) => Player.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      hasNetworkCapability: json['hasNetworkCapability'] as bool? ?? false,
    );

Map<String, dynamic> _$CreateMatchStateToJson(_CreateMatchState instance) =>
    <String, dynamic>{
      'availableConfigs': instance.availableConfigs,
      'selectedConfigId': instance.selectedConfigId,
      'gameName': instance.gameName,
      'matchId': instance.matchId,
      'playerSlots': instance.playerSlots,
      'players': instance.players,
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'hasNetworkCapability': instance.hasNetworkCapability,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
