// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_match_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreateMatchState {
  /// Available game configurations to choose from
  List<GameConfig> get availableConfigs;

  /// Currently selected match configuration ID
  String? get selectedConfigId;
  String? get gameName;
  GameMatchId? get matchId;
  List<PlayerSlot>
      get playerSlots; // TODO: do we need this too or just the slots?
  List<Player> get players;

  /// Processing status of the state
  ProcessingStatus get processingStatus;

  /// Whether network match functionality is available
  bool get hasNetworkCapability;

  /// Create a copy of CreateMatchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateMatchStateCopyWith<CreateMatchState> get copyWith =>
      _$CreateMatchStateCopyWithImpl<CreateMatchState>(
          this as CreateMatchState, _$identity);

  /// Serializes this CreateMatchState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateMatchState &&
            const DeepCollectionEquality()
                .equals(other.availableConfigs, availableConfigs) &&
            (identical(other.selectedConfigId, selectedConfigId) ||
                other.selectedConfigId == selectedConfigId) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality()
                .equals(other.playerSlots, playerSlots) &&
            const DeepCollectionEquality().equals(other.players, players) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(availableConfigs),
      selectedConfigId,
      gameName,
      matchId,
      const DeepCollectionEquality().hash(playerSlots),
      const DeepCollectionEquality().hash(players),
      processingStatus,
      hasNetworkCapability);

  @override
  String toString() {
    return 'CreateMatchState(availableConfigs: $availableConfigs, selectedConfigId: $selectedConfigId, gameName: $gameName, matchId: $matchId, playerSlots: $playerSlots, players: $players, processingStatus: $processingStatus, hasNetworkCapability: $hasNetworkCapability)';
  }
}

/// @nodoc
abstract mixin class $CreateMatchStateCopyWith<$Res> {
  factory $CreateMatchStateCopyWith(
          CreateMatchState value, $Res Function(CreateMatchState) _then) =
      _$CreateMatchStateCopyWithImpl;
  @useResult
  $Res call(
      {List<GameConfig> availableConfigs,
      String? selectedConfigId,
      String? gameName,
      GameMatchId? matchId,
      List<PlayerSlot> playerSlots,
      List<Player> players,
      ProcessingStatus processingStatus,
      bool hasNetworkCapability});
}

/// @nodoc
class _$CreateMatchStateCopyWithImpl<$Res>
    implements $CreateMatchStateCopyWith<$Res> {
  _$CreateMatchStateCopyWithImpl(this._self, this._then);

  final CreateMatchState _self;
  final $Res Function(CreateMatchState) _then;

  /// Create a copy of CreateMatchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? availableConfigs = null,
    Object? selectedConfigId = freezed,
    Object? gameName = freezed,
    Object? matchId = freezed,
    Object? playerSlots = null,
    Object? players = null,
    Object? processingStatus = null,
    Object? hasNetworkCapability = null,
  }) {
    return _then(_self.copyWith(
      availableConfigs: null == availableConfigs
          ? _self.availableConfigs
          : availableConfigs // ignore: cast_nullable_to_non_nullable
              as List<GameConfig>,
      selectedConfigId: freezed == selectedConfigId
          ? _self.selectedConfigId
          : selectedConfigId // ignore: cast_nullable_to_non_nullable
              as String?,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as GameMatchId?,
      playerSlots: null == playerSlots
          ? _self.playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      players: null == players
          ? _self.players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CreateMatchState extends CreateMatchState {
  const _CreateMatchState(
      {final List<GameConfig> availableConfigs = const [],
      this.selectedConfigId,
      this.gameName,
      this.matchId,
      final List<PlayerSlot> playerSlots = const [],
      final List<Player> players = const [],
      this.processingStatus = ProcessingStatus.start,
      this.hasNetworkCapability = false})
      : _availableConfigs = availableConfigs,
        _playerSlots = playerSlots,
        _players = players,
        super._();
  factory _CreateMatchState.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchStateFromJson(json);

  /// Available game configurations to choose from
  final List<GameConfig> _availableConfigs;

  /// Available game configurations to choose from
  @override
  @JsonKey()
  List<GameConfig> get availableConfigs {
    if (_availableConfigs is EqualUnmodifiableListView)
      return _availableConfigs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableConfigs);
  }

  /// Currently selected match configuration ID
  @override
  final String? selectedConfigId;
  @override
  final String? gameName;
  @override
  final GameMatchId? matchId;
  final List<PlayerSlot> _playerSlots;
  @override
  @JsonKey()
  List<PlayerSlot> get playerSlots {
    if (_playerSlots is EqualUnmodifiableListView) return _playerSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_playerSlots);
  }

// TODO: do we need this too or just the slots?
  final List<Player> _players;
// TODO: do we need this too or just the slots?
  @override
  @JsonKey()
  List<Player> get players {
    if (_players is EqualUnmodifiableListView) return _players;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_players);
  }

  /// Processing status of the state
  @override
  @JsonKey()
  final ProcessingStatus processingStatus;

  /// Whether network match functionality is available
  @override
  @JsonKey()
  final bool hasNetworkCapability;

  /// Create a copy of CreateMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateMatchStateCopyWith<_CreateMatchState> get copyWith =>
      __$CreateMatchStateCopyWithImpl<_CreateMatchState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateMatchStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateMatchState &&
            const DeepCollectionEquality()
                .equals(other._availableConfigs, _availableConfigs) &&
            (identical(other.selectedConfigId, selectedConfigId) ||
                other.selectedConfigId == selectedConfigId) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality()
                .equals(other._playerSlots, _playerSlots) &&
            const DeepCollectionEquality().equals(other._players, _players) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_availableConfigs),
      selectedConfigId,
      gameName,
      matchId,
      const DeepCollectionEquality().hash(_playerSlots),
      const DeepCollectionEquality().hash(_players),
      processingStatus,
      hasNetworkCapability);

  @override
  String toString() {
    return 'CreateMatchState(availableConfigs: $availableConfigs, selectedConfigId: $selectedConfigId, gameName: $gameName, matchId: $matchId, playerSlots: $playerSlots, players: $players, processingStatus: $processingStatus, hasNetworkCapability: $hasNetworkCapability)';
  }
}

/// @nodoc
abstract mixin class _$CreateMatchStateCopyWith<$Res>
    implements $CreateMatchStateCopyWith<$Res> {
  factory _$CreateMatchStateCopyWith(
          _CreateMatchState value, $Res Function(_CreateMatchState) _then) =
      __$CreateMatchStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<GameConfig> availableConfigs,
      String? selectedConfigId,
      String? gameName,
      GameMatchId? matchId,
      List<PlayerSlot> playerSlots,
      List<Player> players,
      ProcessingStatus processingStatus,
      bool hasNetworkCapability});
}

/// @nodoc
class __$CreateMatchStateCopyWithImpl<$Res>
    implements _$CreateMatchStateCopyWith<$Res> {
  __$CreateMatchStateCopyWithImpl(this._self, this._then);

  final _CreateMatchState _self;
  final $Res Function(_CreateMatchState) _then;

  /// Create a copy of CreateMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? availableConfigs = null,
    Object? selectedConfigId = freezed,
    Object? gameName = freezed,
    Object? matchId = freezed,
    Object? playerSlots = null,
    Object? players = null,
    Object? processingStatus = null,
    Object? hasNetworkCapability = null,
  }) {
    return _then(_CreateMatchState(
      availableConfigs: null == availableConfigs
          ? _self._availableConfigs
          : availableConfigs // ignore: cast_nullable_to_non_nullable
              as List<GameConfig>,
      selectedConfigId: freezed == selectedConfigId
          ? _self.selectedConfigId
          : selectedConfigId // ignore: cast_nullable_to_non_nullable
              as String?,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as GameMatchId?,
      playerSlots: null == playerSlots
          ? _self._playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      players: null == players
          ? _self._players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
