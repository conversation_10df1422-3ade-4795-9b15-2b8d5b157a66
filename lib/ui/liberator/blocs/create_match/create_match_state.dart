import 'package:common/models/game.dart';
import 'package:common/models/player.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_match_state.freezed.dart';

part 'create_match_state.g.dart';

/// State for the CreateMatchBloc
@freezed
abstract class CreateMatchState with _$CreateMatchState {
  // Private constructor required for freezed when using custom getters
  const CreateMatchState._();

  /// Create a new CreateMatchState
  const factory CreateMatchState({
    /// Available game configurations to choose from
    @Default([]) List<GameConfig> availableConfigs,

    /// Currently selected match configuration ID
    String? selectedConfigId,

    String? gameName,

    GameMatchId? matchId,

    @Default([]) List<PlayerSlot> playerSlots,

// TODO: do we need this too or just the slots?
    @Default([]) List<Player> players,

    /// Processing status of the state
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,

    /// Whether network match functionality is available
    @Default(false) bool hasNetworkCapability,
  }) = _CreateMatchState;

  /// Custom getter methods
  // Custom toString is not needed as freezed will generate one

  /// Get the currently selected match configuration
  GameConfig? get selectedConfig => selectedConfigId != null
      ? availableConfigs.firstWhere(
          (config) => config.id == selectedConfigId,
          orElse: () => availableConfigs.isNotEmpty
              ? availableConfigs.first
              : throw Exception('No match configurations available'),
        )
      : null;
  
  GameMatchState? get initialMatchState => selectedConfig != null
      ? GameMatchState(
          matchConfig: _createMatchConfig(),
          userPlayerId: playerSlots.isNotEmpty
              ? (playerSlots.first.playerId ?? '')
              : '',
          turnCount: 0,
          hands: const {},
          resources: const {},
          loadedCardClasses: const {},
        )
      : null;

  /// Create a MatchConfig from the available state
  MatchConfig _createMatchConfig() {
    // Determine host player ID from first player slot or default to empty string
    final hostId = playerSlots.isNotEmpty ? (playerSlots.first.playerId ?? '') : '';

    // Convert player slots to players
    final convertedPlayers = _convertPlayerSlotsToPlayers(playerSlots, hostId);

    return MatchConfig(
      gameId: selectedConfig!.id,
      selectedGameMode: playerSlots.any((slot) => slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork)
          ? GameMode.server
          : GameMode.hotSeat,
      hostId: hostId,
      matchId: matchId ?? const GenerateIdIfNeededConverter().fromJson(null),
      players: convertedPlayers,
      name: gameName,
    );
  }

  /// Helper function to convert player slots to players
  List<Player> _convertPlayerSlotsToPlayers(List<PlayerSlot> playerSlots, String currentUserId) {
    print('🔧 PLAYER_CONVERSION: Converting ${playerSlots.length} slots for user: $currentUserId');

    final players = playerSlots.map((slot) {
      // Determine the player ID and type based on whether a player is assigned to this slot
      String playerId;
      PlayerType playerType;

      print('🔧 SLOT_DEBUG: ${slot.id} - type: ${slot.type}, playerId: ${slot.playerId}');

      if (slot.playerId?.isNotEmpty == true) {
        // If a player is already assigned to this slot, use that player ID and keep the type
        playerId = slot.playerId!;
        playerType = slot.type;
        print('🔧 USING_ASSIGNED: $playerId for slot ${slot.id}');
      } else {
        // For unassigned slots, make them bots with unique IDs
        playerId = 'bot_${slot.id}';
        playerType = PlayerType.botLocal;
        print('🔧 GENERATED_BOT: $playerId for slot ${slot.id}');
      }

      final player = Player(
        id: playerId,
        name: slot.name ?? 'Unnamed Player',
        type: playerType,
      );

      print('🔧 CREATED_PLAYER: ${player.id} - ${player.name} (type: ${player.type})');
      return player;
    }).toList();

    print('🔧 FINAL_PLAYERS: ${players.map((p) => '${p.id}:${p.name}').join(', ')}');
    return players;
  }

  /// Check if we can remove a player slot
  bool get canRemoveSlot => playerSlots.length > 1;

  /// Check if we can add a player slot
  bool get canAddSlot => selectedConfig != null && playerSlots.length < 8;

  /// Get the number of active (non-empty) player slots
  int get activePlayerCount => playerSlots.length;

  /// Check if we can start a match based on player slots being valid
  bool get startMatchButtonEnabled {
    return areAllRequiredSlotsValid && availableConfigs.isNotEmpty;
  }

  /// Checks if all required player slots have valid configurations
  bool get areAllRequiredSlotsValid {
    if (playerSlots.isEmpty) {
      return false;
    }

    return playerSlots.every((slot) {
      switch (slot.type) {
        case PlayerType.humanNetwork:
          return slot.playerId?.isNotEmpty ?? false;
        case PlayerType.humanLocal:
        case PlayerType.botLocal:
        case PlayerType.botNetwork:
          return true;
        }
    });
  }

  factory CreateMatchState.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchStateFromJson(json);
}
