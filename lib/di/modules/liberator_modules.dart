import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/match_save/match_save_manager.dart';
import 'package:dauntless/repositories/locations_repository.dart';
import 'package:dauntless/repositories/vehicles_repository.dart';
import 'package:dauntless/repositories/actions_repository.dart';
import 'package:dauntless/use_cases/actions_use_case.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/vehicles_use_case.dart';
import 'package:injectable/injectable.dart';

@module
abstract class LiberatorModules {
  @preResolve
  @Singleton()
  Future<LocationsRepository> locationsRepositoryFactory() async {
    return LocationsRepository();
  }

  @preResolve
  @Singleton()
  Future<VehiclesRepository> vehiclesRepositoryFactory() async {
    return VehiclesRepository();
  }

  @preResolve
  Future<ActionsRepository> actionsRepositoryFactory() async {
    return ActionsRepository();
  }

  // @preResolve
  // @Singleton()
  // Future<CardChildrenUseCase> cardChildrenUseCaseFactory(LocationsRepository locationsRepository, VehiclesRepository vehiclesRepository) async {
  //   return CardChildrenUseCase(locationsRepository, vehiclesRepository);
  // }

  // @preResolve
  // @singleton
  // Future<ListObjectRepository<T>> listObjectRepositoryFactory<T>() async {
  //   return ListObjectRepository<T>();
  // }

  // @preResolve
  // @Singleton()
  // Future<ListObjectUseCase<T>> listObjectUseCaseFactory<T>(ListObjectRepository<T> listObjectRepository) async {
  //   return ListObjectUseCase<T>();
  // }

  @preResolve
  Future<LocationsUseCase> locationsUseCaseFactory(
      LocationsRepository locationsRepository) async {
    return LocationsUseCase(locationsRepository)..init();
  }

  @preResolve
  Future<LocationGroupingsUseCase> locationGroupingsUseCaseFactory(
      LocationsRepository locationsRepository) async {
    return LocationGroupingsUseCase(locationsRepository)..init();
  }

  @preResolve
  Future<ActionsGroupingsUseCase> actionGroupingsUseCaseFactory(
      ActionsRepository actionsRepository) async {
    return ActionsGroupingsUseCase(actionsRepository)..init();
  }

  // @preResolve
  // @Singleton()
  // Future<SectorsUseCase> sectorsUseCaseFactory(LocationsRepository planetsRepository) async {
  //   return SectorsUseCase(planetsRepository)..init();
  // }

  @preResolve
  Future<VehiclesUseCase> vehiclesUseCaseFactory(
      VehiclesRepository starshipsRepository) async {
    return VehiclesUseCase(starshipsRepository)..init();
  }

  @preResolve
  Future<ActionsUseCase> actionsUseCaseFactory(
      ActionsRepository actionsRepository) async {
    return ActionsUseCase(actionsRepository)..init();
  }

  @Singleton()
  @Injectable()
  CommandCenterBloc commandCenterBlocFactory(
    LoggingUseCase loggingUseCase,
    GameMatchManager matchManager,
    MatchSaveManager matchSaveManager,
  ) {
    return CommandCenterBloc(
        loggingUseCase.getRemoteLogger('CommandCenterBloc'),
        matchManager,
        matchSaveManager);
  }
}
