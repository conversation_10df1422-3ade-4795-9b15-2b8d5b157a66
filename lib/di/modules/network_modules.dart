import 'package:dauntless/api/dauntless_api.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/network/websocket_manager.dart';
import 'package:dauntless/repositories/match_repository.dart';
import 'package:dauntless/repositories/players_repository.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/repositories/websocket_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match/network_match_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/server_environment_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

/// Extension for re-registering the DauntlessApi
// extension GetItReRegisterDauntlessApi on GetIt {
//   /// Re-register the Dauntless API client in the GetIt container
//   // TODO: should we use scopes to handle this? or does this belong in a service?
//   Future<void> reRegisterDauntlessApi({String? selectedProfileId}) async {
//     // Using the bloc to update the API based on the selected profile
//     // GetIt.I<ServerProfileBloc>()
//     //     .add(UpdateServerProfileEvent(profileId: selectedProfileId));
//
//     // No need to manually update repositories anymore - they listen to the bloc
//     // Just trigger the WebSocket reconnection and test connection event
//     GetIt.I<WebSocketManager>().add(ConnectWebSocketEvent());
//     GetIt.I<ServerEnvironmentManager>().add(ConnectToServerEvent());
//   }
// }

/// Creates a new DauntlessApi instance with the current server profile
Future<DauntlessApi> _buildDauntlessApi(ServerEnvironmentManager envManager,
    {String? selectedProfileId}) async {
  final profileId = selectedProfileId ?? envManager.state.selectedProfileId;
  final profile = envManager.state.getServerProfile(profileId);
  final baseUrl = profile?.httpUrl ?? '';

  final Dio dio = Dio(
    BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 5),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ),
  );

  // Add interceptors for proper JSON handling
  dio.interceptors.add(InterceptorsWrapper(
    onRequest: (options, handler) {
      // Ensure JSON content-type for requests with data
      if (options.data != null && 
          (options.method == 'POST' || options.method == 'PUT' || options.method == 'PATCH')) {
        options.headers['Content-Type'] = 'application/json';
      }
      print('🔔 Request: ${options.method} ${options.path}');
      if (options.data != null) {
        print('🔔 Request body: ${options.data}');
      }
      handler.next(options);
    },
    onResponse: (response, handler) {
      print('✅ Response: ${response.statusCode} ${response.requestOptions.path}');
      handler.next(response);
    },
    onError: (error, handler) {
      print('❌ Error: ${error.message}');
      if (error.response != null) {
        print('❌ Error body: ${error.response?.data}');
      }
      handler.next(error);
    },
  ));

  return DauntlessApi(dio);
}

/// Registers all network-related dependencies
// Future<void> registerNetworkDependencies() async {
//   // Build the initial API
//   final initialApi =
//       await _buildDauntlessApi(GetIt.I<ServerEnvironmentManager>());
//
//   // Register the initial DauntlessApi
//   GetIt.I.registerSingleton<DauntlessApi>(initialApi);
//
//   // Register the ServerProfileBloc with the API builder function
//   final serverProfileBloc = ServerProfileBloc(
//     GetIt.I<ServerEnvironmentManager>(),
//     _buildDauntlessApi,
//     initialApi,
//     GetIt.I<LoggingUseCase>().getRemoteLogger('ServerProfileBloc'),
//   );
//   GetIt.I.registerSingleton<ServerProfileBloc>(serverProfileBloc);
//
//   // For backward compatibility, also register a regular ServerRepository
//   // that will be used by components that haven't been updated yet
//   final regularServerRepository = ServerRepository(
//     GetIt.I<LoggingUseCase>().getRemoteLogger('ServerRepository'),
//     initialApi,
//   );
//   GetIt.I.registerSingleton<ServerRepository>(regularServerRepository);
//
//   // Register MatchSelectionUseCase with the reactive repository
//   final matchSelectionUseCase = MatchSelectionUseCase(
//     regularServerRepository,
//     GetIt.I<LoggingUseCase>().getRemoteLogger('MatchSelectionUseCase'),
//   );
//   GetIt.I.registerSingleton<MatchSelectionUseCase>(matchSelectionUseCase);
//
//   // Initialize the bloc
//   serverProfileBloc.add(InitializeServerProfileEvent());
// }

const String staticServerConnected = 'serverConnected';

@module
abstract class NetworkModules {
  /// Websocket -- note: these are not scoped; needed for the ServerEnvironmentManager (could be refactored)
  /// Factory for WebSocketRepository
  @preResolve
  @singleton
  Future<WebSocketRepository> webSocketRepositoryFactory() async {
    return WebSocketRepository();
  }

  /// Factory for serverNotificationsUseCase
  @preResolve
  @singleton
  Future<ServerNotificationsUseCase> serverNotificationsUseCaseFactory(
          WebSocketRepository webSocketRepository) async =>
      ServerNotificationsUseCase(webSocketRepository);

  /// Factory for WebSocketManager
  @preResolve
  @singleton
  Future<WebSocketManager> webSocketManagerFactory(
    ServerNotificationsUseCase serverNotificationsUseCase,
  ) async {
    return WebSocketManager(serverNotificationsUseCase);
  }

  @preResolve
  @singleton
  @Scope(staticServerConnected)
  Future<DauntlessApi> buildDauntlessApi(ServerEnvironmentManager envManager) =>
      _buildDauntlessApi(envManager,
          selectedProfileId: envManager.state.selectedProfileId);

  @preResolve
  @singleton
  @Scope(staticServerConnected)
  Future<ServerRepository> serverRepositoryFactory(DauntlessApi api) async =>
      ServerRepository(
          GetIt.I<LoggingUseCase>().getRemoteLogger('ServerRepository'), api);

  /// Factory for ServerEnvironmentManager
  @preResolve
  @singleton
  Future<ServerEnvironmentUseCase> serverEnvironmentUseCaseFactory() async =>
      ServerEnvironmentUseCase();

  @preResolve
  @singleton
  Future<ServerEnvironmentManager> serverEnvironmentManagerFactory(
    ServerEnvironmentUseCase serverEnvironmentUseCase,
    ServerNotificationsUseCase serverNotificationsUseCase,
  ) async {
    // Create the manager with the server environment use case
    final manager =
        ServerEnvironmentManager(serverEnvironmentUseCase, serverNotificationsUseCase);
    // Initialize the manager
    manager.add(InitializeEnvironmentEvent());

    return manager;
  }

  @preResolve
  @Singleton()
  @Scope(staticServerConnected)
  Future<MatchSelectionUseCase> networkMatchSelectionUseCaseFactory(
    LoggingUseCase loggingUseCase,
    ServerRepository serverRepository,
  ) async {
    final matchSelectionUseCase = NetworkMatchSelectionUseCase(
        serverRepository, loggingUseCase.getRemoteLogger('NetworkMatchSelectionUseCase'));
    GetIt.I<MatchSelectionEnvironmentManager>()
        .add(AddMatchSelectionUseCase(matchSelectionUseCase));
    return matchSelectionUseCase;
  }

  @preResolve
  @Singleton()
  @Scope(staticServerConnected)
  Future<NetworkMatchUseCase> networkMatchUseCaseFactory(
      LoggingUseCase loggingUseCase,
      PlayersRepository playersRepository,
      MatchRepository matchRepository,
      ServerRepository serverRepository) async {
    return NetworkMatchUseCase(loggingUseCase.getRemoteLogger('MatchUseCase'),
        playersRepository, matchRepository, serverRepository);
  }
}
