import 'package:dauntless/debug/global_bloc_observer.dart';
import 'package:dauntless/repositories/logging/logging_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

@module
abstract class DebugModules {
  @preResolve
  @Singleton()
  Future<LoggingRepository> loggingRepositoryFactory(
      // LocalStorageRepository localStorage
      ) async {
    return LoggingRepository();
  }

  @preResolve
  @Singleton()
  Future<LoggingUseCase> loggingUseCaseFactory(
      LoggingRepository repo,
      // LocalStorageRepository localStorage,
      // AppLifeCycleRepository appLifeCycleRepository,
      // AppConfig appConfig,
      // EnvironmentInfoRepository environmentInfoRepository,
      ) async {
    LoggingUseCase useCase = LoggingUseCase(repo
        // , localStorage, appLifeCycleRepository, appConfig, environmentInfoRepository
    );
    useCase.init();
    return useCase;
  }

  @preResolve
  @Singleton()
  Future<GlobalBlocObserver> globalBlocObserverFactory(
      LoggingUseCase loggingUseCase) async {
    final obs = GlobalBlocObserver(loggingUseCase.getRemoteLogger('BlocObs'));
    Bloc.observer = obs;
    return obs;
  }
}