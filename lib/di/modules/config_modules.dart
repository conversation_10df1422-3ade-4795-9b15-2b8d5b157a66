import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/dev/dev_initial_state.dart';
import 'package:dauntless/frameworks/assets/assets_manager.dart';
import 'package:dauntless/frameworks/game_config/game_config_event.dart';
import 'package:dauntless/frameworks/game_config/game_config_manager.dart';
import 'package:dauntless/repositories/app_life_cycle_repository.dart';
import 'package:dauntless/repositories/assets_repository.dart';
import 'package:dauntless/repositories/file_selector_repository.dart';
import 'package:dauntless/repositories/game_config_repository.dart';
import 'package:dauntless/repositories/theme_repository.dart';
import 'package:dauntless/ui/blocs/theme/theme_bloc.dart';
import 'package:dauntless/use_cases/assets_use_case.dart';
import 'package:dauntless/use_cases/file_selector_use_case.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/theme_use_case.dart';
import 'package:dauntless/use_cases/user_use_case.dart';
import 'package:injectable/injectable.dart';

@module
abstract class ConfigModules {
  /// ****************************************************
  /// App Lifecycle

  @preResolve
  @Singleton()
  Future<AppLifeCycleRepository> appLifeCycleRepositoryFactory(
      LoggingUseCase loggingUseCase,
      ) async {
    final repo = AppLifeCycleRepository(
        loggingUseCase.getRemoteLogger('AppLifeCycleRepository'));
    await repo.init();
    return repo;
  }

  /// ****************************************************
  /// Theming
  @preResolve
  @Singleton()
  Future<ThemeRepository> themeRepositoryFactory() async {
    return ThemeRepository();
  }

  @preResolve
  @Singleton()
  Future<ThemeUseCase> themeUseCaseFactory(
      ThemeRepository themeRepository) async {
    return ThemeUseCase(themeRepository)..init();
  }

  @preResolve
  @Singleton()
  Future<ThemeBloc> themeBlocFactory(ThemeUseCase themeUseCase) async {
    return ThemeBloc(themeUseCase);
  }


  /// ****************************************************
  /// File Handling
  @preResolve
  @Singleton()
  Future<JsonReadWriteDataService> jsonReadWriteRepositoryFactory() async {
    return JsonReadWriteDataService();
  }

  @preResolve
  @Singleton()
  Future<FileSelectorRepository> fileSelectorRepositoryFactory() async {
    return FileSelectorRepository();
  }

  @preResolve
  @Singleton()
  Future<FileSelectorUseCase> fileSelectorUseCaseFactory(
      FileSelectorRepository fileSelectorRepository) async {
    return FileSelectorUseCase(fileSelectorRepository);
  }

  /// ****************************************************
  /// Game Config
  @preResolve
  @Singleton()
  Future<GameConfigRepository> gameConfigRepositoryFactory() async {
    return GameConfigRepository();
  }

  @preResolve
  @Singleton()
  Future<GameConfigUseCase> gameConfigUseCaseFactory(
      GameConfigRepository gameConfigRepository) async {
    return GameConfigUseCase(gameConfigRepository);
  }

  @preResolve
  @Singleton()
  Future<GameConfigManager> gameConfigManagerFactory(
      GameConfigUseCase useCase) async {
    return GameConfigManager(useCase)..add(LoadAvailableGameConfigsEvent());
  }

  /// ****************************************************
  /// Assets
  @preResolve
  @Singleton()
  Future<AssetsRepository> assetsRepositoryFactory() async {
    return AssetsRepository();
  }

  @preResolve
  @Singleton()
  Future<AssetsUseCase> assetsUseCaseFactory(
      AssetsRepository repository) async {
    return AssetsUseCase(repository);
  }

  @preResolve
  @Singleton()
  Future<AssetsManager> assetsManagerFactory(
      AssetsUseCase useCase,
      LocationsUseCase locationsUseCase,
      ) async {
    return AssetsManager(useCase, locationsUseCase)
    // TODO: set this based on the GameConfigManager ... or data therein
      ..add(LoadAssetsEvent(DevInitialState.basePath));
  }

  /// ****************************************************
  /// User Management
  @preResolve
  @Singleton()
  Future<UserUseCase> userUseCaseFactory() async {
    return UserUseCase();
  }
}
