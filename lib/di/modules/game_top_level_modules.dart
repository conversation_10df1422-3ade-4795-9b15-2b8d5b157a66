import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/frameworks/game_config/game_config_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/match_save/match_save_manager.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/repositories/generic_card_class_repository.dart';
import 'package:dauntless/repositories/match_repository.dart';
import 'package:dauntless/repositories/players_repository.dart';
import 'package:dauntless/repositories/save_state_repository.dart';
import 'package:dauntless/use_cases/generic_card_class_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/map_grid_use_case.dart';
import 'package:dauntless/use_cases/match/match_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:dauntless/use_cases/save_state_use_case.dart';
import 'package:dauntless/use_cases/user_use_case.dart';
import 'package:dauntless/use_cases/vehicles_use_case.dart';
import 'package:injectable/injectable.dart';

@module
abstract class GameTopLevelModules {
  // TODO: this and the UseCase probably belong in the same module
  @preResolve
  @Singleton()
  Future<UserManager> userManagerFactory(UserUseCase userUseCase) async => UserManager(userUseCase);

  /// ****************************************************
  /// Save State
  @preResolve
  @Singleton()
  Future<SaveStateRepository> saveStateRepositoryFactory() async {
    return SaveStateRepository();
  }

  @preResolve
  @Singleton()
  Future<MatchSaveUseCase> saveStateUseCaseFactory(
      SaveStateRepository saveStateRepository) async {
    return MatchSaveUseCase(saveStateRepository);
  }

  @preResolve
  @Singleton()
  Future<MatchSaveManager> matchStateManagerFactory(
    GameMatchManager matchManager,
    GameConfigManager gameConfigManager,
    // No longer needed: MatchSaveUseCase saveStateUseCase,
    // AppHistoryUseCase appHistoryUseCase,
  ) async {
    return MatchSaveManager(matchManager, gameConfigManager);
  }

  /// ****************************************************
  /// Match
  @preResolve
  @Singleton()
  Future<GenericCardClassRepository> genericCardClassRepositoryFactory() async {
    return GenericCardClassRepository();
  }

  @preResolve
  @Singleton()
  Future<GenericCardClassUseCase> genericCardClassUseCaseFactory(
      GenericCardClassRepository repository) async {
    return GenericCardClassUseCase(repository);
  }

  @preResolve
  Future<PlayersRepository> playersRepositoryFactory() async {
    return PlayersRepository();
  }

  @preResolve
  @Singleton()
  Future<PlayersUseCase> playersUseCaseFactory(
      PlayersRepository repository) async {
    return PlayersUseCase(repository);
  }

  @preResolve
  @Singleton()
  Future<MatchRepository> matchRepositoryFactory() async {
    return MatchRepository();
  }

  @preResolve
  @Singleton()
  Future<MatchUseCase> matchUseCaseFactory(
    LoggingUseCase loggingUseCase,
    PlayersRepository playersRepository,
    MatchRepository matchRepository,
  ) async {
    return MatchUseCase(loggingUseCase.getRemoteLogger('MatchUseCase'),
        playersRepository, matchRepository);
  }

  @preResolve
  @Singleton()
  Future<MatchSelectionUseCase> matchSelectionUseCaseFactory(LoggingUseCase loggingUseCase) async {
    return LocalMatchSelectionUseCase(loggingUseCase.getRemoteLogger('LocalMatchSelectionUseCase'));
  }

  @preResolve
  @Singleton()
  Future<MatchSelectionEnvironmentManager> matchSelectionEnvironmentManagerFactory(MatchSelectionUseCase matchUseCase) async {
    return MatchSelectionEnvironmentManager()
    ..add(AddMatchSelectionUseCase(matchUseCase));
  }

  @preResolve
  @Singleton()
  Future<GameMatchManager> matchManagerFactory(
    MatchUseCase matchUseCase,
    PlayersUseCase playersUseCase,
    GenericCardClassUseCase genericCardClassUseCase,
    LocationsUseCase locationsUseCase,
    VehiclesUseCase vehiclesUseCase,
    LoggingUseCase loggingUseCase,
  ) async {
    return GameMatchManager(
      matchUseCase,
      playersUseCase,
      genericCardClassUseCase,
      locationsUseCase,
      vehiclesUseCase,
    );
  }

  @Singleton()
  @preResolve
  Future<MapGridUseCase> mapGridUseCaseFactory() async {
    return MapGridUseCase();
  }
}
