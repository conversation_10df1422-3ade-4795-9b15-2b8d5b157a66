import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import 'di.config.dart';

@InjectableInit(
  generateForDir: ['lib/di'],
)
Future<GetIt> configureDependencies() async {
  // First, initialize with the generated dependencies
  final getIt = await GetIt.instance.init();

  /// to support multiple MatchSelectionUseCases
  getIt.enableRegisteringMultipleInstancesOfOneType();
  
  // Then register our reactive network components
  // await registerNetworkDependencies();
  
  return getIt;
}
