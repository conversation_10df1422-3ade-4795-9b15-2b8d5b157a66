// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assets_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AssetsState _$AssetsStateFromJson(Map<String, dynamic> json) => _AssetsState(
      basePath: json['basePath'] as String,
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      assetRefs: (json['assetRefs'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
      cardTypeAssetFallbacks:
          (json['cardTypeAssetFallbacks'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    k,
                    (e as Map<String, dynamic>).map(
                      (k, e) => MapEntry(
                          $enumDecode(_$CardTypeEnumMap, k), e as String),
                    )),
              ) ??
              const {},
    );

Map<String, dynamic> _$AssetsStateToJson(_AssetsState instance) =>
    <String, dynamic>{
      'basePath': instance.basePath,
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'assetRefs': instance.assetRefs,
      'cardTypeAssetFallbacks': instance.cardTypeAssetFallbacks.map((k, e) =>
          MapEntry(k, e.map((k, e) => MapEntry(_$CardTypeEnumMap[k]!, e)))),
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};

const _$CardTypeEnumMap = {
  CardType.character: 'character',
  CardType.location: 'location',
  CardType.building: 'building',
  CardType.vehicle: 'vehicle',
  CardType.grouping: 'grouping',
};
