// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'assets_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AssetsState {
  String get basePath;
  ProcessingStatus get processingStatus;
  Map<String, String> get assetRefs;
  Map<PlayerId, Map<CardType, String>> get cardTypeAssetFallbacks;

  /// Create a copy of AssetsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AssetsStateCopyWith<AssetsState> get copyWith =>
      _$AssetsStateCopyWithImpl<AssetsState>(this as AssetsState, _$identity);

  /// Serializes this AssetsState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AssetsState &&
            (identical(other.basePath, basePath) ||
                other.basePath == basePath) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality().equals(other.assetRefs, assetRefs) &&
            const DeepCollectionEquality()
                .equals(other.cardTypeAssetFallbacks, cardTypeAssetFallbacks));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      basePath,
      processingStatus,
      const DeepCollectionEquality().hash(assetRefs),
      const DeepCollectionEquality().hash(cardTypeAssetFallbacks));

  @override
  String toString() {
    return 'AssetsState(basePath: $basePath, processingStatus: $processingStatus, assetRefs: $assetRefs, cardTypeAssetFallbacks: $cardTypeAssetFallbacks)';
  }
}

/// @nodoc
abstract mixin class $AssetsStateCopyWith<$Res> {
  factory $AssetsStateCopyWith(
          AssetsState value, $Res Function(AssetsState) _then) =
      _$AssetsStateCopyWithImpl;
  @useResult
  $Res call(
      {String basePath,
      ProcessingStatus processingStatus,
      Map<String, String> assetRefs,
      Map<PlayerId, Map<CardType, String>> cardTypeAssetFallbacks});
}

/// @nodoc
class _$AssetsStateCopyWithImpl<$Res> implements $AssetsStateCopyWith<$Res> {
  _$AssetsStateCopyWithImpl(this._self, this._then);

  final AssetsState _self;
  final $Res Function(AssetsState) _then;

  /// Create a copy of AssetsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? basePath = null,
    Object? processingStatus = null,
    Object? assetRefs = null,
    Object? cardTypeAssetFallbacks = null,
  }) {
    return _then(_self.copyWith(
      basePath: null == basePath
          ? _self.basePath
          : basePath // ignore: cast_nullable_to_non_nullable
              as String,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      assetRefs: null == assetRefs
          ? _self.assetRefs
          : assetRefs // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      cardTypeAssetFallbacks: null == cardTypeAssetFallbacks
          ? _self.cardTypeAssetFallbacks
          : cardTypeAssetFallbacks // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, Map<CardType, String>>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AssetsState extends AssetsState {
  const _AssetsState(
      {required this.basePath,
      this.processingStatus = ProcessingStatus.start,
      final Map<String, String> assetRefs = const {},
      final Map<PlayerId, Map<CardType, String>> cardTypeAssetFallbacks =
          const {}})
      : _assetRefs = assetRefs,
        _cardTypeAssetFallbacks = cardTypeAssetFallbacks,
        super._();
  factory _AssetsState.fromJson(Map<String, dynamic> json) =>
      _$AssetsStateFromJson(json);

  @override
  final String basePath;
  @override
  @JsonKey()
  final ProcessingStatus processingStatus;
  final Map<String, String> _assetRefs;
  @override
  @JsonKey()
  Map<String, String> get assetRefs {
    if (_assetRefs is EqualUnmodifiableMapView) return _assetRefs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_assetRefs);
  }

  final Map<PlayerId, Map<CardType, String>> _cardTypeAssetFallbacks;
  @override
  @JsonKey()
  Map<PlayerId, Map<CardType, String>> get cardTypeAssetFallbacks {
    if (_cardTypeAssetFallbacks is EqualUnmodifiableMapView)
      return _cardTypeAssetFallbacks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_cardTypeAssetFallbacks);
  }

  /// Create a copy of AssetsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AssetsStateCopyWith<_AssetsState> get copyWith =>
      __$AssetsStateCopyWithImpl<_AssetsState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AssetsStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AssetsState &&
            (identical(other.basePath, basePath) ||
                other.basePath == basePath) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality()
                .equals(other._assetRefs, _assetRefs) &&
            const DeepCollectionEquality().equals(
                other._cardTypeAssetFallbacks, _cardTypeAssetFallbacks));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      basePath,
      processingStatus,
      const DeepCollectionEquality().hash(_assetRefs),
      const DeepCollectionEquality().hash(_cardTypeAssetFallbacks));

  @override
  String toString() {
    return 'AssetsState(basePath: $basePath, processingStatus: $processingStatus, assetRefs: $assetRefs, cardTypeAssetFallbacks: $cardTypeAssetFallbacks)';
  }
}

/// @nodoc
abstract mixin class _$AssetsStateCopyWith<$Res>
    implements $AssetsStateCopyWith<$Res> {
  factory _$AssetsStateCopyWith(
          _AssetsState value, $Res Function(_AssetsState) _then) =
      __$AssetsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String basePath,
      ProcessingStatus processingStatus,
      Map<String, String> assetRefs,
      Map<PlayerId, Map<CardType, String>> cardTypeAssetFallbacks});
}

/// @nodoc
class __$AssetsStateCopyWithImpl<$Res> implements _$AssetsStateCopyWith<$Res> {
  __$AssetsStateCopyWithImpl(this._self, this._then);

  final _AssetsState _self;
  final $Res Function(_AssetsState) _then;

  /// Create a copy of AssetsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? basePath = null,
    Object? processingStatus = null,
    Object? assetRefs = null,
    Object? cardTypeAssetFallbacks = null,
  }) {
    return _then(_AssetsState(
      basePath: null == basePath
          ? _self.basePath
          : basePath // ignore: cast_nullable_to_non_nullable
              as String,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      assetRefs: null == assetRefs
          ? _self._assetRefs
          : assetRefs // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      cardTypeAssetFallbacks: null == cardTypeAssetFallbacks
          ? _self._cardTypeAssetFallbacks
          : cardTypeAssetFallbacks // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, Map<CardType, String>>,
    ));
  }
}

// dart format on
