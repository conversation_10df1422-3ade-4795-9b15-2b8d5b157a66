import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assets_state.freezed.dart';

part 'assets_state.g.dart';

@freezed
abstract class AssetsState with _$AssetsState {
  String? _refForAsset(String assetKey) => assetRefs[assetKey];

  String? refForAsset(String assetKey) {
    final relativePath = _refForAsset(assetKey);
    if (relativePath == null) {
      return null;
    }
    return '$basePath/images/$relativePath';
  }

  String? fallbackRefForCardType(PlayerId playerId, CardType cardType) {
    final fallback = cardTypeAssetFallbacks[playerId];
    if (fallback == null) {
      return null;
    }
    final path = fallback[cardType];
    if (path == null) {
      return null;
    }
    return '$basePath/images/$path';
  }

  const AssetsState._();

  const factory AssetsState(
      {required String basePath,
      @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
      @Default({}) Map<String, String> assetRefs,
      @Default({}) Map<PlayerId, Map<CardType, String>> cardTypeAssetFallbacks,
      }) = _AssetsState;

  factory AssetsState.fromJson(Map<String, dynamic> json) =>
      _$AssetsStateFromJson(json);
}
