import 'package:dauntless/frameworks/assets/assets_state.dart';
import 'package:dauntless/use_cases/assets_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

abstract class AssetsEvent {}

class LoadAssetsEvent extends AssetsEvent {
  final String basePath;

  LoadAssetsEvent(this.basePath);
}

// TODO: note 'AssetManager' is a dart web_ui element ... maybe change this name to something more different?
class AssetsManager extends Bloc<AssetsEvent, AssetsState> {
  final AssetsUseCase _assetsUseCase;
  @Deprecated('this should probably be handled by the assets use case')
  final LocationsUseCase _locationsUseCase;

  AssetsManager(this._assetsUseCase, this._locationsUseCase)
      : super(const AssetsState(basePath: '')) {
    on<LoadAssetsEvent>(_onLoadAssets);
  }

  void _onLoadAssets(LoadAssetsEvent event, Emitter<AssetsState> emit) async {
    if (event.basePath.isEmpty) {
      return;
    }
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    final assetRefs = _assetsUseCase.loadAssetRefs(event.basePath);
    await _locationsUseCase.ready;
    final locationAssetRefs = _locationsUseCase.objects
        .map((location) {
          final primaryImgRef = location.imgRefs['primary'];
          if (primaryImgRef != null) {
            return MapEntry(location.id, primaryImgRef);
          }
          return null;
        })
        .whereType<MapEntry<String, String>>()
        .toList();

    final Map<String, String> locationAssetRefsMap = Map.fromEntries(locationAssetRefs);
    assetRefs.addAll(locationAssetRefsMap);

    final cardTypeAssetFallbacks = _assetsUseCase.loadCardTypeAssetFallbacks(event.basePath);
    emit(state.copyWith(
        basePath: event.basePath,
        processingStatus: ProcessingStatus.loaded,
        assetRefs: assetRefs,
        cardTypeAssetFallbacks: cardTypeAssetFallbacks
    ));
  }
}
