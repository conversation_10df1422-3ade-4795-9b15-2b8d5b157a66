// import 'package:dauntless/use_cases/app_history_use_case.dart';
//
// import 'game_config/game_config_manager.dart';
// import 'game_match/game_match_manager.dart';
// import 'save_state/match_save_manager.dart';
//
// @Deprecated('')
// class GameSaveService {
//   final MatchManager _matchManager;
//   final MatchStateManager _saveStateManager;
//   final AppHistoryUseCase _appHistoryUseCase;
//   // final AppHistoryManager _appHistoryManager;
//   final GameConfigManager _gameConfigManager;
//
//   GameSaveService(this._matchManager, this._saveStateManager, this._appHistoryUseCase, this._gameConfigManager);
//
//   Future<void> init() async {
//     _gameConfigManager.stream.listen((state) async {
//       if (state.loadedGameConfig != null) {
//         // final mostRecentGameSave = await _appHistoryUseCase.getMostRecentSaveForGame(state.loadedGameConfig!);
//         // _saveStateManager.add(LoadSaveStateEvent());
//       }
//     });
//   }
// }