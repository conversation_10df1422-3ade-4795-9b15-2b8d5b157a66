// import 'package:dauntless/models/base/game_config.dart';
// import 'package:dauntless/use_cases/list_object_use_case.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';

// @freezed
// class GameEditorState with _$GameEditorState {
//   const factory GameEditorState({
//     @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
//     @Default(null) GameConfig? loadedGameConfig,
//     @Default(null) String? basePath,
//   }) = _GameEditorState;

//   factory GameEditorState.fromJson(Map<String, dynamic> json) =>
//       _$GameEditorStateFromJson(json);

//   const GameEditorState._();
// }
