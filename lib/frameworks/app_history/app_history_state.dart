// import 'package:dauntless/models/base/game_config.dart';
// import 'package:dauntless/use_cases/list_object_use_case.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';
//
// @freezed
// class AppHistoryState with _$AppHistoryState {
//   const factory AppHistoryState({
//     @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
//   }) = _AppHistoryState;
//
//   factory AppHistoryState.fromJson(Map<String, dynamic> json) =>
//       _$AppHistoryStateFromJson(json);
// }