// import 'package:dauntless/models/base/game_config.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
//
// import 'app_history_state.dart';
//
// abstract class AppHistoryEvent {}
//
// class AppHistoryManager extends Bloc<AppHistoryEvent, AppHistoryState> {
//   final GameConfigManager _gameConfigManager;
//   final SaveStateUseCase _saveStateUseCase;
//
//   AppHistoryManager(this._gameConfigManager, this._saveStateUseCase)
//       : super(DevInitialState.initialAppHistoryState) {
//     on<LoadGameConfigEvent>(_onLoadGameConfigEvent);
//     on<SaveGameConfigEvent>(_onSaveGameConfigEvent);
//   }
//
//   void _onLoadGameConfigEvent(
//       LoadGameConfigEvent event, Emitter<AppHistoryState> emit) async {
//     emit(state.copyWith(processingStatus: ProcessingStatus.loading));
//     final gameConfig = await _gameConfigManager.getGameConfig();
//     emit(state.copyWith(gameConfig: gameConfig, processingStatus: ProcessingStatus.loaded));
//   }
//
//   void _onSaveGameConfigEvent(
//       SaveGameConfigEvent event, Emitter<AppHistoryState> emit) async {
//     emit(state.copyWith(processingStatus: ProcessingStatus.saving));
//     await _saveStateUseCase.saveGameConfig(event.gameConfig);
//     emit(state.copyWith(processingStatus: ProcessingStatus.saved));
//   }
// }