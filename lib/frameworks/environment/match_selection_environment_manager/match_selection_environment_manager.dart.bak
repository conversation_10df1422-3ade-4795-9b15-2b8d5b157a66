import 'dart:async';

import 'package:collection/collection.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_state.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_selection/match_selection_state.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/websocket_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

abstract class MatchSelectionEnvironmentEvent {}

class AddMatchSelectionUseCase extends MatchSelectionEnvironmentEvent {
  final MatchSelectionUseCase useCase;

  AddMatchSelectionUseCase(this.useCase);
}

class RemoveMatchSelectionUseCase extends MatchSelectionEnvironmentEvent {
  final String useCaseName;

  RemoveMatchSelectionUseCase(this.useCaseName);
}

class SubscribeToOpenMatchesEvent extends MatchSelectionEnvironmentEvent {
  final String useCaseName;

  SubscribeToOpenMatchesEvent(this.useCaseName);
}

/// Event to trigger match loading for all available use cases
class TriggerMatchLoadingEvent extends MatchSelectionEnvironmentEvent {}

/// Event to reset the match loading trigger flag
class ResetMatchLoadingTriggerEvent extends MatchSelectionEnvironmentEvent {}

class MatchSelectionEnvironmentManager extends Bloc<
    MatchSelectionEnvironmentEvent, MatchSelectionEnvironmentState> {
  MatchSelectionEnvironmentManager() : super(MatchSelectionEnvironmentState()) {
    on<AddMatchSelectionUseCase>(_onAddMatchSelectionUseCase);
    on<RemoveMatchSelectionUseCase>(_onRemoveMatchSelectionUseCase);
    on<TriggerMatchLoadingEvent>(_onTriggerMatchLoadingEvent);
    on<ResetMatchLoadingTriggerEvent>(_onResetMatchLoadingTriggerEvent);
    on<SubscribeToOpenMatchesEvent>(_onSubscribeToOpenMatchesEvent);
  }

  // final Set<MatchSelectionUseCase> _useCases = {};
  final Map<String, MatchSelectionUseCase> _useCases = {};
  // StreamSubscription? _openMatchesSubscription;
  final Map<String, StreamSubscription> _openMatchesSubscriptions = {}; // <String, MatchSelectionBloc>

  // final Set<MatchSelectionBloc> _selectionBlocs = {};

  // MatchSelectionUseCase? getMatchSelectionUseCase(String name) => _useCases.firstWhereOrNull((useCase) => useCase.name == name);

  // MatchSelectionBloc? getMatchSelectionBloc(String name) => _selectionBlocs.firstWhereOrNull((bloc) => bloc.sourceName == name);

  void _onAddMatchSelectionUseCase(AddMatchSelectionUseCase event,
      Emitter<MatchSelectionEnvironmentState> emit) {
    // _useCases.add(event.useCase);
    // _useCases.map((useCase) =>
    //   MatchSelectionBloc(
    //     GetIt.I<GameConfigUseCase>(),
    //     useCase,
    //     GetIt.I<serverNotificationsUseCase>(),
    //   )
    // ).toSet();
    // _selectionBlocs.add(MatchSelectionBloc(
    //   GetIt.I<GameConfigUseCase>(),
    //   event.useCase,
    //   GetIt.I<serverNotificationsUseCase>(),
    // ));
    _useCases[event.useCase.name] = event.useCase;
    final selectionSources = Map<String, MatchSelectionState>.from(state.matchSelectionSources);
    selectionSources[event.useCase.name] = MatchSelectionState();
    emit(state.copyWith(
        matchSelectionSources: selectionSources,
        availableMatchSelectionUseCases: List.from(state.availableMatchSelectionUseCases)
          ..add(event.useCase.name)));
    add(SubscribeToOpenMatchesEvent(event.useCase.name));
  }

  void _onRemoveMatchSelectionUseCase(RemoveMatchSelectionUseCase event,
      Emitter<MatchSelectionEnvironmentState> emit) {
    // _useCases.removeWhere((useCase) => useCase.name == event.useCaseName);
    _selectionBlocs.removeWhere((bloc) => bloc.sourceName == event.useCaseName);

    final updatedUseCases = List<String>.from(state.availableMatchSelectionUseCases)
      ..remove(event.useCaseName);
    emit(state.copyWith(
        availableMatchSelectionUseCases: updatedUseCases));
  }

  void _onTriggerMatchLoadingEvent(TriggerMatchLoadingEvent event,
      Emitter<MatchSelectionEnvironmentState> emit) {
    emit(state.copyWith(shouldTriggerMatchLoading: true));
  }

  void _onResetMatchLoadingTriggerEvent(ResetMatchLoadingTriggerEvent event,
      Emitter<MatchSelectionEnvironmentState> emit) {
    emit(state.copyWith(shouldTriggerMatchLoading: false));
  }

  void _onSubscribeToOpenMatchesEvent(SubscribeToOpenMatchesEvent event,
      Emitter<MatchSelectionEnvironmentState> emit) {
    _openMatchesSubscriptions[event.useCaseName] = _useCases[event.useCaseName]!.openMatchesStream.listen((matches) {
      emit(state.copyWith(
          matchSelectionSources: Map<String, MatchSelectionState>.from(state.matchSelectionSources)
            ..update(event.useCaseName, (value) => value.copyWith(openMatches: matches))
      ));
    }
