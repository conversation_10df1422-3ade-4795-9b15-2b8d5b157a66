// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_selection_environment_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchSelectionEnvironmentState _$MatchSelectionEnvironmentStateFromJson(
        Map<String, dynamic> json) =>
    _MatchSelectionEnvironmentState(
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      availableMatchSelectionUseCases:
          (json['availableMatchSelectionUseCases'] as List<dynamic>?)
                  ?.map((e) => e as String)
                  .toList() ??
              const [],
      shouldTriggerMatchLoading:
          json['shouldTriggerMatchLoading'] as bool? ?? false,
    );

Map<String, dynamic> _$MatchSelectionEnvironmentStateToJson(
        _MatchSelectionEnvironmentState instance) =>
    <String, dynamic>{
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'availableMatchSelectionUseCases':
          instance.availableMatchSelectionUseCases,
      'shouldTriggerMatchLoading': instance.shouldTriggerMatchLoading,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
