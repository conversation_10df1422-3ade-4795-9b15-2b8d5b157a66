import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';

part 'match_selection_environment_state.freezed.dart';
part 'match_selection_environment_state.g.dart';

@freezed
abstract class MatchSelectionEnvironmentState with _$MatchSelectionEnvironmentState {
  const factory MatchSelectionEnvironmentState({
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
    @Default([]) List<String> availableMatchSelectionUseCases,
    @Default(false) bool shouldTriggerMatchLoading,
  }) = _MatchSelectionEnvironmentState;

  factory MatchSelectionEnvironmentState.fromJson(Map<String, dynamic> json) => _$MatchSelectionEnvironmentStateFromJson(json);
}