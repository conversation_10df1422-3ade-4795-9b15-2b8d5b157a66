// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_selection_environment_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchSelectionEnvironmentState {
  ProcessingStatus get processingStatus;
  List<String> get availableMatchSelectionUseCases;
  bool get shouldTriggerMatchLoading;

  /// Create a copy of MatchSelectionEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchSelectionEnvironmentStateCopyWith<MatchSelectionEnvironmentState>
      get copyWith => _$MatchSelectionEnvironmentStateCopyWithImpl<
              MatchSelectionEnvironmentState>(
          this as MatchSelectionEnvironmentState, _$identity);

  /// Serializes this MatchSelectionEnvironmentState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchSelectionEnvironmentState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality().equals(
                other.availableMatchSelectionUseCases,
                availableMatchSelectionUseCases) &&
            (identical(other.shouldTriggerMatchLoading,
                    shouldTriggerMatchLoading) ||
                other.shouldTriggerMatchLoading == shouldTriggerMatchLoading));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      const DeepCollectionEquality().hash(availableMatchSelectionUseCases),
      shouldTriggerMatchLoading);

  @override
  String toString() {
    return 'MatchSelectionEnvironmentState(processingStatus: $processingStatus, availableMatchSelectionUseCases: $availableMatchSelectionUseCases, shouldTriggerMatchLoading: $shouldTriggerMatchLoading)';
  }
}

/// @nodoc
abstract mixin class $MatchSelectionEnvironmentStateCopyWith<$Res> {
  factory $MatchSelectionEnvironmentStateCopyWith(
          MatchSelectionEnvironmentState value,
          $Res Function(MatchSelectionEnvironmentState) _then) =
      _$MatchSelectionEnvironmentStateCopyWithImpl;
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      List<String> availableMatchSelectionUseCases,
      bool shouldTriggerMatchLoading});
}

/// @nodoc
class _$MatchSelectionEnvironmentStateCopyWithImpl<$Res>
    implements $MatchSelectionEnvironmentStateCopyWith<$Res> {
  _$MatchSelectionEnvironmentStateCopyWithImpl(this._self, this._then);

  final MatchSelectionEnvironmentState _self;
  final $Res Function(MatchSelectionEnvironmentState) _then;

  /// Create a copy of MatchSelectionEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? processingStatus = null,
    Object? availableMatchSelectionUseCases = null,
    Object? shouldTriggerMatchLoading = null,
  }) {
    return _then(_self.copyWith(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      availableMatchSelectionUseCases: null == availableMatchSelectionUseCases
          ? _self.availableMatchSelectionUseCases
          : availableMatchSelectionUseCases // ignore: cast_nullable_to_non_nullable
              as List<String>,
      shouldTriggerMatchLoading: null == shouldTriggerMatchLoading
          ? _self.shouldTriggerMatchLoading
          : shouldTriggerMatchLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchSelectionEnvironmentState
    implements MatchSelectionEnvironmentState {
  const _MatchSelectionEnvironmentState(
      {this.processingStatus = ProcessingStatus.start,
      final List<String> availableMatchSelectionUseCases = const [],
      this.shouldTriggerMatchLoading = false})
      : _availableMatchSelectionUseCases = availableMatchSelectionUseCases;
  factory _MatchSelectionEnvironmentState.fromJson(Map<String, dynamic> json) =>
      _$MatchSelectionEnvironmentStateFromJson(json);

  @override
  @JsonKey()
  final ProcessingStatus processingStatus;
  final List<String> _availableMatchSelectionUseCases;
  @override
  @JsonKey()
  List<String> get availableMatchSelectionUseCases {
    if (_availableMatchSelectionUseCases is EqualUnmodifiableListView)
      return _availableMatchSelectionUseCases;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableMatchSelectionUseCases);
  }

  @override
  @JsonKey()
  final bool shouldTriggerMatchLoading;

  /// Create a copy of MatchSelectionEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchSelectionEnvironmentStateCopyWith<_MatchSelectionEnvironmentState>
      get copyWith => __$MatchSelectionEnvironmentStateCopyWithImpl<
          _MatchSelectionEnvironmentState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchSelectionEnvironmentStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchSelectionEnvironmentState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality().equals(
                other._availableMatchSelectionUseCases,
                _availableMatchSelectionUseCases) &&
            (identical(other.shouldTriggerMatchLoading,
                    shouldTriggerMatchLoading) ||
                other.shouldTriggerMatchLoading == shouldTriggerMatchLoading));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      const DeepCollectionEquality().hash(_availableMatchSelectionUseCases),
      shouldTriggerMatchLoading);

  @override
  String toString() {
    return 'MatchSelectionEnvironmentState(processingStatus: $processingStatus, availableMatchSelectionUseCases: $availableMatchSelectionUseCases, shouldTriggerMatchLoading: $shouldTriggerMatchLoading)';
  }
}

/// @nodoc
abstract mixin class _$MatchSelectionEnvironmentStateCopyWith<$Res>
    implements $MatchSelectionEnvironmentStateCopyWith<$Res> {
  factory _$MatchSelectionEnvironmentStateCopyWith(
          _MatchSelectionEnvironmentState value,
          $Res Function(_MatchSelectionEnvironmentState) _then) =
      __$MatchSelectionEnvironmentStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      List<String> availableMatchSelectionUseCases,
      bool shouldTriggerMatchLoading});
}

/// @nodoc
class __$MatchSelectionEnvironmentStateCopyWithImpl<$Res>
    implements _$MatchSelectionEnvironmentStateCopyWith<$Res> {
  __$MatchSelectionEnvironmentStateCopyWithImpl(this._self, this._then);

  final _MatchSelectionEnvironmentState _self;
  final $Res Function(_MatchSelectionEnvironmentState) _then;

  /// Create a copy of MatchSelectionEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? processingStatus = null,
    Object? availableMatchSelectionUseCases = null,
    Object? shouldTriggerMatchLoading = null,
  }) {
    return _then(_MatchSelectionEnvironmentState(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      availableMatchSelectionUseCases: null == availableMatchSelectionUseCases
          ? _self._availableMatchSelectionUseCases
          : availableMatchSelectionUseCases // ignore: cast_nullable_to_non_nullable
              as List<String>,
      shouldTriggerMatchLoading: null == shouldTriggerMatchLoading
          ? _self.shouldTriggerMatchLoading
          : shouldTriggerMatchLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
