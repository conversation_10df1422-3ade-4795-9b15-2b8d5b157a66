// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'server_environment_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ServerEnvironmentState _$ServerEnvironmentStateFromJson(
        Map<String, dynamic> json) =>
    _ServerEnvironmentState(
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      selectedProfileConnectionStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap,
              json['selectedProfileConnectionStatus']) ??
          ProcessingStatus.start,
      profiles: (json['profiles'] as List<dynamic>?)
              ?.map((e) => ServerProfile.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      selectedProfileId: json['selectedProfileId'] as String?,
      useDebugUrls: json['useDebugUrls'] as bool? ?? false,
      lastConnectionAttempt: json['lastConnectionAttempt'] == null
          ? null
          : DateTime.parse(json['lastConnectionAttempt'] as String),
      lastSuccessfulConnection: json['lastSuccessfulConnection'] == null
          ? null
          : DateTime.parse(json['lastSuccessfulConnection'] as String),
    );

Map<String, dynamic> _$ServerEnvironmentStateToJson(
        _ServerEnvironmentState instance) =>
    <String, dynamic>{
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'selectedProfileConnectionStatus':
          _$ProcessingStatusEnumMap[instance.selectedProfileConnectionStatus]!,
      'profiles': instance.profiles,
      'selectedProfileId': instance.selectedProfileId,
      'useDebugUrls': instance.useDebugUrls,
      'lastConnectionAttempt':
          instance.lastConnectionAttempt?.toIso8601String(),
      'lastSuccessfulConnection':
          instance.lastSuccessfulConnection?.toIso8601String(),
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
