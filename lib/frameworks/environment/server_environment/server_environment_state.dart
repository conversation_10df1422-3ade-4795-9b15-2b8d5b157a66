// lib/frameworks/server_environment/server_environment_state.dart
import 'package:collection/collection.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'server_environment_config.dart';
import 'server_profile.dart';

part 'server_environment_state.freezed.dart';
part 'server_environment_state.g.dart';

/// State for ServerEnvironmentManager
@freezed
abstract class ServerEnvironmentState with _$ServerEnvironmentState {
  /// Private constructor for custom getters
  const ServerEnvironmentState._() : super();

  ServerProfile? get profile => profiles.firstWhereOrNull((config) => config.name == selectedProfileId);

  ServerEnvironmentConfig get config => ServerEnvironmentConfig(
        profiles: profiles,
        selectedProfileId: selectedProfileId,
      );
  
  /// Create a new server environment state
  const factory ServerEnvironmentState({
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
    @Default(ProcessingStatus.start) ProcessingStatus selectedProfileConnectionStatus,
    @Default([]) List<ServerProfile> profiles,
    String? selectedProfileId,
    @Default(false) bool useDebugUrls,
    DateTime? lastConnectionAttempt,
    DateTime? lastSuccessfulConnection,
  }) = _ServerEnvironmentState;
  
  /// JSON serialization
  factory ServerEnvironmentState.fromJson(Map<String, dynamic> json) =>
      _$ServerEnvironmentStateFromJson(json);

  ServerProfile? getServerProfile(String? profileId) => profiles.firstWhereOrNull((p) => p.name == profileId);
}
