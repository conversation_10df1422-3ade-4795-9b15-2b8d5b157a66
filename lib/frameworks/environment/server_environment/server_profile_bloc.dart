// import 'package:bloc/bloc.dart';
// import 'package:dauntless/api/dauntless_api.dart';
// import 'package:dauntless/frameworks/server_environment/server_environment_manager.dart';
// import 'package:dauntless/frameworks/server_environment/server_profile_state.dart';
// import 'package:dauntless/use_cases/logging_use_case.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';
//
// // Connection Status enum
// enum ApiConnectionStatus {
//   connected,
//   disconnected,
//   connecting,
//   error
// }
//
// abstract class ServerProfileEvent {}
//
// class UpdateServerProfileEvent extends ServerProfileEvent {
//   final String? profileId;
//
//   UpdateServerProfileEvent({this.profileId});
// }
//
// class InitializeServerProfileEvent extends ServerProfileEvent {}
//
// class ReportErrorEvent extends ServerProfileEvent {
//   final String errorMessage;
//
//   ReportErrorEvent(this.errorMessage);
// }
//
// class ClearErrorEvent extends ServerProfileEvent {}
//
//
// /// Bloc that manages the current DauntlessApi instance based on server profile changes
// class ServerProfileBloc extends Bloc<ServerProfileEvent, ServerProfileState> {
//   final ServerEnvironmentManager _environmentManager;
//   final Future<DauntlessApi> Function(ServerEnvironmentManager, {String? selectedProfileId}) _apiBuilder;
//   final RemoteLogger? _logger;
//
//   ServerProfileBloc(
//     this._environmentManager,
//     this._apiBuilder,
//     DauntlessApi initialApi,
//     this._logger) : super(ServerProfileState()) {
//     on<UpdateServerProfileEvent>(_onUpdateServerProfile);
//     on<InitializeServerProfileEvent>(_onInitializeServerProfile);
//     on<ReportErrorEvent>(_onReportError);
//     on<ClearErrorEvent>(_onClearError);
//   }
//
//   /// Updates the API with the given server profile ID
//   Future<void> _onUpdateServerProfile(
//     UpdateServerProfileEvent event,
//     Emitter<ServerProfileState> emit,
//   ) async {
//     try {
//       // First emit a connecting state
//       emit(state.copyWith(
//         status: ApiConnectionStatus.connecting,
//         errorMessage: null,
//       ));
//
//       _logger?.info('Updating API with profile ID: ${event.profileId}');
//
//       // Get profile information
//       final profileId = event.profileId ?? _environmentManager.state.selectedProfileId;
//       final profile = _environmentManager.state.getServerProfile(profileId);
//       final baseUrl = profile?.httpUrl ?? '';
//       final profileName = profile?.name ?? 'Unknown';
//
//       // Build a new API instance
//       final api = await _apiBuilder(
//         _environmentManager,
//         selectedProfileId: profileId,
//       );
//
//       // Emit success state with connected status
//       emit(state.copyWith(
//         api: api,
//         status: ApiConnectionStatus.connected,
//         errorMessage: null,
//         currentProfileId: profileId,
//         currentProfileName: profileName,
//         baseUrl: baseUrl,
//       ));
//
//       _logger?.info('Successfully updated API with profile: $profileName, baseUrl: $baseUrl');
//     } catch (e) {
//       _logger?.error('Error updating API: $e');
//       print('ServerProfileBloc: Error updating API: $e');
//
//       // Emit error state but keep the current API
//       emit(state.copyWith(
//         status: ApiConnectionStatus.error,
//         errorMessage: 'Failed to connect to server: $e',
//       ));
//     }
//   }
//
//   /// Initializes the API with the current profile from the environment manager
//   Future<void> _onInitializeServerProfile(
//     InitializeServerProfileEvent event,
//     Emitter<ServerProfileState> emit,
//   ) async {
//     try {
//       _logger?.info('Initializing API with current profile');
//       final currentProfileId = _environmentManager.state.selectedProfileId;
//       add(UpdateServerProfileEvent(profileId: currentProfileId));
//     } catch (e) {
//       _logger?.error('Error initializing API: $e');
//       print('ServerProfileBloc: Error initializing API: $e');
//       emit(state.copyWith(
//         status: ApiConnectionStatus.error,
//         errorMessage: 'Failed to initialize API: $e',
//       ));
//     }
//   }
//
//   /// Reports an error in the bloc
//   void _onReportError(
//     ReportErrorEvent event,
//     Emitter<ServerProfileState> emit,
//   ) {
//     _logger?.error('API error reported: ${event.errorMessage}');
//     emit(state.copyWith(
//       status: ApiConnectionStatus.error,
//       errorMessage: event.errorMessage,
//     ));
//   }
//
//   /// Clears any error in the bloc
//   void _onClearError(
//     ClearErrorEvent event,
//     Emitter<ServerProfileState> emit,
//   ) {
//     emit(state.copyWith(
//       errorMessage: null,
//       // Only reset status if we were in error state
//       status: state.status == ApiConnectionStatus.error ?
//         ApiConnectionStatus.connected : state.status,
//     ));
//   }
// }
