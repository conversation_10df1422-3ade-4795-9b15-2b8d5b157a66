// import 'package:freezed_annotation/freezed_annotation.dart';
//
// import 'server_profile_bloc.dart';
//
// part 'server_profile_state.freezed.dart';
// part 'server_profile_state.g.dart';
//
// @freezed
// class ServerProfileState with _$ServerProfileState {
//
//   const ServerProfileState._();
//
//   const factory ServerProfileState({
//     @Default(ApiConnectionStatus.connected) ApiConnectionStatus status,
//     String? errorMessage,
//     String? currentProfileId,
//     String? currentProfileName,
//     String? baseUrl,
//   }) = _ServerProfileState;
//
//   /// Helper to check if there is an error
//   bool get hasError => errorMessage != null && errorMessage!.isNotEmpty;
//
//   /// Helper to check if connected
//   bool get isConnected => status == ApiConnectionStatus.connected;
//
//   factory ServerProfileState.fromJson(Map<String, dynamic> json) => _$ServerProfileStateFromJson(json);
// }