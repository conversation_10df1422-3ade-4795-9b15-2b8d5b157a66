// lib/frameworks/server_environment/server_environment_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'server_profile.freezed.dart';

part 'server_profile.g.dart';

/// State for ServerEnvironmentManager
@freezed
abstract class ServerProfile with _$ServerProfile {
  /// Private constructor for custom getters
  const ServerProfile._();

  /// Create a new server environment state
  const factory ServerProfile(
      {required String domain,
        // TODO: should this be 'name' or id? ... see 'selectedProfileId' in the ServerEnvironmentConfig
      required String name,
      required String port}) = _ServerProfile;

  /// JSON serialization
  factory ServerProfile.fromJson(Map<String, dynamic> json) =>
      _$ServerProfileFromJson(json);

  String get wsUrl => 'ws://$domain:$port/ws';

  get httpUrl => 'http://$domain:$port';
}
