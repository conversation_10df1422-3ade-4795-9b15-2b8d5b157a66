// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'server_environment_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ServerEnvironmentConfig {
  List<ServerProfile> get profiles; // T => ListObject?>
  String? get selectedProfileId;

  /// Create a copy of ServerEnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ServerEnvironmentConfigCopyWith<ServerEnvironmentConfig> get copyWith =>
      _$ServerEnvironmentConfigCopyWithImpl<ServerEnvironmentConfig>(
          this as ServerEnvironmentConfig, _$identity);

  /// Serializes this ServerEnvironmentConfig to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ServerEnvironmentConfig &&
            const DeepCollectionEquality().equals(other.profiles, profiles) &&
            (identical(other.selectedProfileId, selectedProfileId) ||
                other.selectedProfileId == selectedProfileId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(profiles), selectedProfileId);

  @override
  String toString() {
    return 'ServerEnvironmentConfig(profiles: $profiles, selectedProfileId: $selectedProfileId)';
  }
}

/// @nodoc
abstract mixin class $ServerEnvironmentConfigCopyWith<$Res> {
  factory $ServerEnvironmentConfigCopyWith(ServerEnvironmentConfig value,
          $Res Function(ServerEnvironmentConfig) _then) =
      _$ServerEnvironmentConfigCopyWithImpl;
  @useResult
  $Res call({List<ServerProfile> profiles, String? selectedProfileId});
}

/// @nodoc
class _$ServerEnvironmentConfigCopyWithImpl<$Res>
    implements $ServerEnvironmentConfigCopyWith<$Res> {
  _$ServerEnvironmentConfigCopyWithImpl(this._self, this._then);

  final ServerEnvironmentConfig _self;
  final $Res Function(ServerEnvironmentConfig) _then;

  /// Create a copy of ServerEnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profiles = null,
    Object? selectedProfileId = freezed,
  }) {
    return _then(_self.copyWith(
      profiles: null == profiles
          ? _self.profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<ServerProfile>,
      selectedProfileId: freezed == selectedProfileId
          ? _self.selectedProfileId
          : selectedProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ServerEnvironmentConfig extends ServerEnvironmentConfig {
  const _ServerEnvironmentConfig(
      {final List<ServerProfile> profiles = const [], this.selectedProfileId})
      : _profiles = profiles,
        super._();
  factory _ServerEnvironmentConfig.fromJson(Map<String, dynamic> json) =>
      _$ServerEnvironmentConfigFromJson(json);

  final List<ServerProfile> _profiles;
  @override
  @JsonKey()
  List<ServerProfile> get profiles {
    if (_profiles is EqualUnmodifiableListView) return _profiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_profiles);
  }

// T => ListObject?>
  @override
  final String? selectedProfileId;

  /// Create a copy of ServerEnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ServerEnvironmentConfigCopyWith<_ServerEnvironmentConfig> get copyWith =>
      __$ServerEnvironmentConfigCopyWithImpl<_ServerEnvironmentConfig>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ServerEnvironmentConfigToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ServerEnvironmentConfig &&
            const DeepCollectionEquality().equals(other._profiles, _profiles) &&
            (identical(other.selectedProfileId, selectedProfileId) ||
                other.selectedProfileId == selectedProfileId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_profiles), selectedProfileId);

  @override
  String toString() {
    return 'ServerEnvironmentConfig(profiles: $profiles, selectedProfileId: $selectedProfileId)';
  }
}

/// @nodoc
abstract mixin class _$ServerEnvironmentConfigCopyWith<$Res>
    implements $ServerEnvironmentConfigCopyWith<$Res> {
  factory _$ServerEnvironmentConfigCopyWith(_ServerEnvironmentConfig value,
          $Res Function(_ServerEnvironmentConfig) _then) =
      __$ServerEnvironmentConfigCopyWithImpl;
  @override
  @useResult
  $Res call({List<ServerProfile> profiles, String? selectedProfileId});
}

/// @nodoc
class __$ServerEnvironmentConfigCopyWithImpl<$Res>
    implements _$ServerEnvironmentConfigCopyWith<$Res> {
  __$ServerEnvironmentConfigCopyWithImpl(this._self, this._then);

  final _ServerEnvironmentConfig _self;
  final $Res Function(_ServerEnvironmentConfig) _then;

  /// Create a copy of ServerEnvironmentConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? profiles = null,
    Object? selectedProfileId = freezed,
  }) {
    return _then(_ServerEnvironmentConfig(
      profiles: null == profiles
          ? _self._profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<ServerProfile>,
      selectedProfileId: freezed == selectedProfileId
          ? _self.selectedProfileId
          : selectedProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
