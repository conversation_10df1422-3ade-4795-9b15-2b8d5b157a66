// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'server_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ServerProfile {
  String
      get domain; // TODO: should this be 'name' or id? ... see 'selectedProfileId' in the ServerEnvironmentConfig
  String get name;
  String get port;

  /// Create a copy of ServerProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ServerProfileCopyWith<ServerProfile> get copyWith =>
      _$ServerProfileCopyWithImpl<ServerProfile>(
          this as ServerProfile, _$identity);

  /// Serializes this ServerProfile to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ServerProfile &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.port, port) || other.port == port));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, domain, name, port);

  @override
  String toString() {
    return 'ServerProfile(domain: $domain, name: $name, port: $port)';
  }
}

/// @nodoc
abstract mixin class $ServerProfileCopyWith<$Res> {
  factory $ServerProfileCopyWith(
          ServerProfile value, $Res Function(ServerProfile) _then) =
      _$ServerProfileCopyWithImpl;
  @useResult
  $Res call({String domain, String name, String port});
}

/// @nodoc
class _$ServerProfileCopyWithImpl<$Res>
    implements $ServerProfileCopyWith<$Res> {
  _$ServerProfileCopyWithImpl(this._self, this._then);

  final ServerProfile _self;
  final $Res Function(ServerProfile) _then;

  /// Create a copy of ServerProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? domain = null,
    Object? name = null,
    Object? port = null,
  }) {
    return _then(_self.copyWith(
      domain: null == domain
          ? _self.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _self.port
          : port // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ServerProfile extends ServerProfile {
  const _ServerProfile(
      {required this.domain, required this.name, required this.port})
      : super._();
  factory _ServerProfile.fromJson(Map<String, dynamic> json) =>
      _$ServerProfileFromJson(json);

  @override
  final String domain;
// TODO: should this be 'name' or id? ... see 'selectedProfileId' in the ServerEnvironmentConfig
  @override
  final String name;
  @override
  final String port;

  /// Create a copy of ServerProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ServerProfileCopyWith<_ServerProfile> get copyWith =>
      __$ServerProfileCopyWithImpl<_ServerProfile>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ServerProfileToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ServerProfile &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.port, port) || other.port == port));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, domain, name, port);

  @override
  String toString() {
    return 'ServerProfile(domain: $domain, name: $name, port: $port)';
  }
}

/// @nodoc
abstract mixin class _$ServerProfileCopyWith<$Res>
    implements $ServerProfileCopyWith<$Res> {
  factory _$ServerProfileCopyWith(
          _ServerProfile value, $Res Function(_ServerProfile) _then) =
      __$ServerProfileCopyWithImpl;
  @override
  @useResult
  $Res call({String domain, String name, String port});
}

/// @nodoc
class __$ServerProfileCopyWithImpl<$Res>
    implements _$ServerProfileCopyWith<$Res> {
  __$ServerProfileCopyWithImpl(this._self, this._then);

  final _ServerProfile _self;
  final $Res Function(_ServerProfile) _then;

  /// Create a copy of ServerProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? domain = null,
    Object? name = null,
    Object? port = null,
  }) {
    return _then(_ServerProfile(
      domain: null == domain
          ? _self.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      port: null == port
          ? _self.port
          : port // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
