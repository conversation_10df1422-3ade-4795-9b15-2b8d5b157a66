import 'dart:async';
import 'package:dauntless/di/di.config.dart';
import 'package:dauntless/di/modules/network_modules.dart';
import 'package:dauntless/frameworks/environment/match_selection_environment_manager/match_selection_environment_manager.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/server_environment_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'server_environment_state.dart';

/// Events for ServerEnvironmentManager
abstract class ServerEnvironmentEvent {}

/// Initialize environment from config file
class InitializeEnvironmentEvent extends ServerEnvironmentEvent {}

class ReconnectWebSocket extends ServerEnvironmentEvent {}

/// Configure custom server URLs
class SetSelectedServerProfileEvent extends ServerEnvironmentEvent {
  final String? profileId;

  SetSelectedServerProfileEvent(this.profileId);
}

/// Test connection to server
class ConnectToServerEvent extends ServerEnvironmentEvent {}

class ServerEnvironmentManager
    extends Bloc<ServerEnvironmentEvent, ServerEnvironmentState> {
  final ServerEnvironmentUseCase _useCase;
  final ServerNotificationsUseCase _serverNotificationsUseCase;

  ServerEnvironmentManager(
    this._useCase,
    this._serverNotificationsUseCase,
  ) : super(const ServerEnvironmentState()) {
    on<InitializeEnvironmentEvent>(_onInitializeEnvironment);
    on<SetSelectedServerProfileEvent>(_onSetSelectedServerProfileEvent);
    on<ConnectToServerEvent>(_onConnectToServerEvent);
    on<ReconnectWebSocket>(_onReconnectWebSocket);
  }

  /// Initialize environment from config file
  FutureOr<void> _onInitializeEnvironment(
    InitializeEnvironmentEvent event,
    Emitter<ServerEnvironmentState> emit,
  ) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    try {
      final serverEnvironmentConfig =
          await _useCase.loadServerEnvironmentConfig();

      emit(state.copyWith(
        profiles: serverEnvironmentConfig.profiles,
        processingStatus: ProcessingStatus.loaded,
        // selectedProfileId: serverEnvironmentConfig.selectedProfileId,
      ));

      if (serverEnvironmentConfig.selectedProfileId != null) {
        add(SetSelectedServerProfileEvent(
            serverEnvironmentConfig.selectedProfileId));
      }
    } catch (e) {
      // If loading fails, use default URLs
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
      ));
      debugPrint('Failed to load server config: $e');
    }
  }

  /// Configure custom server URLs
  FutureOr<void> _onSetSelectedServerProfileEvent(
    SetSelectedServerProfileEvent event,
    Emitter<ServerEnvironmentState> emit,
  ) async {
    // Only take action if the profile actually changed
    if (state.selectedProfileId != event.profileId) {
      print(
          'ServerEnvironmentManager: Changing profile from ${state.selectedProfileId} to ${event.profileId}');

      if (GetIt.I.currentScopeName == staticServerConnected) {
        await GetIt.I.popScope();
      }

      _serverNotificationsUseCase.disconnect();

      // Update state with provided custom URLs
      emit(state.copyWith(
        selectedProfileId: event.profileId,
        selectedProfileConnectionStatus: ProcessingStatus.loading,
      ));

      if (event.profileId != null) {
        await GetIt.I.initServerConnectedScope();
        add(ConnectToServerEvent());
      }

      // Save the updated selected profile ID to config file
      try {
        final updatedConfig = state.config.copyWith(
          selectedProfileId: event.profileId,
        );
        await _useCase.saveServerEnvironmentConfig(updatedConfig);
        print(
            'ServerEnvironmentManager: Saved selected profile ${event.profileId} to config');
      } catch (e) {
        print('ServerEnvironmentManager: Failed to save config: $e');
        // Don't fail the operation if save fails, just log the error
      }

      emit(state.copyWith(
          selectedProfileConnectionStatus: ProcessingStatus.loaded));
      // await GetIt.I.reRegisterDauntlessApi(selectedProfileId: event.profileId);
    }
  }

  /// Test connection to server
  FutureOr<void> _onConnectToServerEvent(
    ConnectToServerEvent event,
    Emitter<ServerEnvironmentState> emit,
  ) async {
    final now = DateTime.now();

    // Mark connection attempt
    emit(state.copyWith(
      lastConnectionAttempt: now,
      selectedProfileConnectionStatus: ProcessingStatus.loading,
    ));

    final isConnected = await _useCase.testConnection();
    if (!isConnected) {
      emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.error,
      ));
      return;
    }
    emit(state.copyWith(
      lastSuccessfulConnection: now,
      selectedProfileConnectionStatus: ProcessingStatus.loaded,
    ));

    // Also test WebSocket connection
    add(ReconnectWebSocket());
    GetIt.I<MatchSelectionEnvironmentManager>().add(TriggerMatchLoadingEvent());
  }

  /// Helper method to reconnect WebSocket with current configuration
  Future<void> _onReconnectWebSocket(
      ReconnectWebSocket event, Emitter<ServerEnvironmentState> emit) async {
    emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.loading));
    try {
      print(
          'ServerEnvironmentManager: Reconnecting WebSocket with profile ${state.selectedProfileId}');
      print(
          'ServerEnvironmentManager: Using WebSocket URL: ${state.profile?.wsUrl}');

      // First disconnect current WebSocket connection
      _serverNotificationsUseCase.disconnect();

      // Small delay to ensure disconnect completes
      await Future.delayed(const Duration(milliseconds: 200));

      final serverEnvironmentConfig = state.profile ??
          (await _useCase.loadServerEnvironmentConfig()).selected;
      final wsUrl = serverEnvironmentConfig?.wsUrl;
      if (wsUrl == null) {
        throw Exception('WebSocket URL is null');
      }

      // Now reconnect with current URL configuration
      try {
        await _serverNotificationsUseCase.connect();
      } catch (e) {
        emit(state.copyWith(
          selectedProfileConnectionStatus: ProcessingStatus.error,
          lastConnectionAttempt: DateTime.now(),
        ));
        print('ServerEnvironmentManager: Error reconnecting WebSocket: $e');
        return;
      }

      GetIt.I<MatchSelectionEnvironmentManager>()
          .add(TriggerMatchLoadingEvent());

      // Update state to reflect successful connection
      emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.loaded,
        lastConnectionAttempt: DateTime.now(),
        lastSuccessfulConnection: DateTime.now(),
      ));

      print('ServerEnvironmentManager: WebSocket reconnection successful');
    } catch (e) {
      emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.error,
        lastConnectionAttempt: DateTime.now(),
      ));
      print('ServerEnvironmentManager: Error reconnecting WebSocket: $e');
    }
  }
}
