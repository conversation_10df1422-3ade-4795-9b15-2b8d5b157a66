// lib/frameworks/server_environment/server_environment_state.dart
import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'server_profile.dart';

part 'server_environment_config.freezed.dart';
part 'server_environment_config.g.dart';

/// State for ServerEnvironmentManager
@freezed
abstract class ServerEnvironmentConfig with _$ServerEnvironmentConfig {
  /// Private constructor for custom getters
  const ServerEnvironmentConfig._();

  /// Create a new server environment state
  const factory ServerEnvironmentConfig({
    @Default([]) List<ServerProfile> profiles, // T => ListObject?>
    String? selectedProfileId
}) = _ServerEnvironmentConfig;

  /// JSON serialization
  factory ServerEnvironmentConfig.fromJson(Map<String, dynamic> json) =>
      _$ServerEnvironmentConfigFromJson(json);

  ServerProfile? get selected => profiles.firstWhereOrNull((p) => p.name == selectedProfileId);

  String? get wsUrl => selected == null ? null : 'ws://${selected!.domain}:${selected!.port}/ws';

  get httpUrl => selected == null ? null : 'http://${selected!.domain}:${selected!.port}';
}
