// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'server_environment_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ServerEnvironmentState {
  ProcessingStatus get processingStatus;
  ProcessingStatus get selectedProfileConnectionStatus;
  List<ServerProfile> get profiles;
  String? get selectedProfileId;
  bool get useDebugUrls;
  DateTime? get lastConnectionAttempt;
  DateTime? get lastSuccessfulConnection;

  /// Create a copy of ServerEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ServerEnvironmentStateCopyWith<ServerEnvironmentState> get copyWith =>
      _$ServerEnvironmentStateCopyWithImpl<ServerEnvironmentState>(
          this as ServerEnvironmentState, _$identity);

  /// Serializes this ServerEnvironmentState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ServerEnvironmentState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.selectedProfileConnectionStatus,
                    selectedProfileConnectionStatus) ||
                other.selectedProfileConnectionStatus ==
                    selectedProfileConnectionStatus) &&
            const DeepCollectionEquality().equals(other.profiles, profiles) &&
            (identical(other.selectedProfileId, selectedProfileId) ||
                other.selectedProfileId == selectedProfileId) &&
            (identical(other.useDebugUrls, useDebugUrls) ||
                other.useDebugUrls == useDebugUrls) &&
            (identical(other.lastConnectionAttempt, lastConnectionAttempt) ||
                other.lastConnectionAttempt == lastConnectionAttempt) &&
            (identical(
                    other.lastSuccessfulConnection, lastSuccessfulConnection) ||
                other.lastSuccessfulConnection == lastSuccessfulConnection));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      selectedProfileConnectionStatus,
      const DeepCollectionEquality().hash(profiles),
      selectedProfileId,
      useDebugUrls,
      lastConnectionAttempt,
      lastSuccessfulConnection);

  @override
  String toString() {
    return 'ServerEnvironmentState(processingStatus: $processingStatus, selectedProfileConnectionStatus: $selectedProfileConnectionStatus, profiles: $profiles, selectedProfileId: $selectedProfileId, useDebugUrls: $useDebugUrls, lastConnectionAttempt: $lastConnectionAttempt, lastSuccessfulConnection: $lastSuccessfulConnection)';
  }
}

/// @nodoc
abstract mixin class $ServerEnvironmentStateCopyWith<$Res> {
  factory $ServerEnvironmentStateCopyWith(ServerEnvironmentState value,
          $Res Function(ServerEnvironmentState) _then) =
      _$ServerEnvironmentStateCopyWithImpl;
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      ProcessingStatus selectedProfileConnectionStatus,
      List<ServerProfile> profiles,
      String? selectedProfileId,
      bool useDebugUrls,
      DateTime? lastConnectionAttempt,
      DateTime? lastSuccessfulConnection});
}

/// @nodoc
class _$ServerEnvironmentStateCopyWithImpl<$Res>
    implements $ServerEnvironmentStateCopyWith<$Res> {
  _$ServerEnvironmentStateCopyWithImpl(this._self, this._then);

  final ServerEnvironmentState _self;
  final $Res Function(ServerEnvironmentState) _then;

  /// Create a copy of ServerEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? processingStatus = null,
    Object? selectedProfileConnectionStatus = null,
    Object? profiles = null,
    Object? selectedProfileId = freezed,
    Object? useDebugUrls = null,
    Object? lastConnectionAttempt = freezed,
    Object? lastSuccessfulConnection = freezed,
  }) {
    return _then(_self.copyWith(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      selectedProfileConnectionStatus: null == selectedProfileConnectionStatus
          ? _self.selectedProfileConnectionStatus
          : selectedProfileConnectionStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      profiles: null == profiles
          ? _self.profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<ServerProfile>,
      selectedProfileId: freezed == selectedProfileId
          ? _self.selectedProfileId
          : selectedProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
      useDebugUrls: null == useDebugUrls
          ? _self.useDebugUrls
          : useDebugUrls // ignore: cast_nullable_to_non_nullable
              as bool,
      lastConnectionAttempt: freezed == lastConnectionAttempt
          ? _self.lastConnectionAttempt
          : lastConnectionAttempt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastSuccessfulConnection: freezed == lastSuccessfulConnection
          ? _self.lastSuccessfulConnection
          : lastSuccessfulConnection // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ServerEnvironmentState extends ServerEnvironmentState {
  const _ServerEnvironmentState(
      {this.processingStatus = ProcessingStatus.start,
      this.selectedProfileConnectionStatus = ProcessingStatus.start,
      final List<ServerProfile> profiles = const [],
      this.selectedProfileId,
      this.useDebugUrls = false,
      this.lastConnectionAttempt,
      this.lastSuccessfulConnection})
      : _profiles = profiles,
        super._();
  factory _ServerEnvironmentState.fromJson(Map<String, dynamic> json) =>
      _$ServerEnvironmentStateFromJson(json);

  @override
  @JsonKey()
  final ProcessingStatus processingStatus;
  @override
  @JsonKey()
  final ProcessingStatus selectedProfileConnectionStatus;
  final List<ServerProfile> _profiles;
  @override
  @JsonKey()
  List<ServerProfile> get profiles {
    if (_profiles is EqualUnmodifiableListView) return _profiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_profiles);
  }

  @override
  final String? selectedProfileId;
  @override
  @JsonKey()
  final bool useDebugUrls;
  @override
  final DateTime? lastConnectionAttempt;
  @override
  final DateTime? lastSuccessfulConnection;

  /// Create a copy of ServerEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ServerEnvironmentStateCopyWith<_ServerEnvironmentState> get copyWith =>
      __$ServerEnvironmentStateCopyWithImpl<_ServerEnvironmentState>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ServerEnvironmentStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ServerEnvironmentState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.selectedProfileConnectionStatus,
                    selectedProfileConnectionStatus) ||
                other.selectedProfileConnectionStatus ==
                    selectedProfileConnectionStatus) &&
            const DeepCollectionEquality().equals(other._profiles, _profiles) &&
            (identical(other.selectedProfileId, selectedProfileId) ||
                other.selectedProfileId == selectedProfileId) &&
            (identical(other.useDebugUrls, useDebugUrls) ||
                other.useDebugUrls == useDebugUrls) &&
            (identical(other.lastConnectionAttempt, lastConnectionAttempt) ||
                other.lastConnectionAttempt == lastConnectionAttempt) &&
            (identical(
                    other.lastSuccessfulConnection, lastSuccessfulConnection) ||
                other.lastSuccessfulConnection == lastSuccessfulConnection));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      selectedProfileConnectionStatus,
      const DeepCollectionEquality().hash(_profiles),
      selectedProfileId,
      useDebugUrls,
      lastConnectionAttempt,
      lastSuccessfulConnection);

  @override
  String toString() {
    return 'ServerEnvironmentState(processingStatus: $processingStatus, selectedProfileConnectionStatus: $selectedProfileConnectionStatus, profiles: $profiles, selectedProfileId: $selectedProfileId, useDebugUrls: $useDebugUrls, lastConnectionAttempt: $lastConnectionAttempt, lastSuccessfulConnection: $lastSuccessfulConnection)';
  }
}

/// @nodoc
abstract mixin class _$ServerEnvironmentStateCopyWith<$Res>
    implements $ServerEnvironmentStateCopyWith<$Res> {
  factory _$ServerEnvironmentStateCopyWith(_ServerEnvironmentState value,
          $Res Function(_ServerEnvironmentState) _then) =
      __$ServerEnvironmentStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      ProcessingStatus selectedProfileConnectionStatus,
      List<ServerProfile> profiles,
      String? selectedProfileId,
      bool useDebugUrls,
      DateTime? lastConnectionAttempt,
      DateTime? lastSuccessfulConnection});
}

/// @nodoc
class __$ServerEnvironmentStateCopyWithImpl<$Res>
    implements _$ServerEnvironmentStateCopyWith<$Res> {
  __$ServerEnvironmentStateCopyWithImpl(this._self, this._then);

  final _ServerEnvironmentState _self;
  final $Res Function(_ServerEnvironmentState) _then;

  /// Create a copy of ServerEnvironmentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? processingStatus = null,
    Object? selectedProfileConnectionStatus = null,
    Object? profiles = null,
    Object? selectedProfileId = freezed,
    Object? useDebugUrls = null,
    Object? lastConnectionAttempt = freezed,
    Object? lastSuccessfulConnection = freezed,
  }) {
    return _then(_ServerEnvironmentState(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      selectedProfileConnectionStatus: null == selectedProfileConnectionStatus
          ? _self.selectedProfileConnectionStatus
          : selectedProfileConnectionStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      profiles: null == profiles
          ? _self._profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<ServerProfile>,
      selectedProfileId: freezed == selectedProfileId
          ? _self.selectedProfileId
          : selectedProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
      useDebugUrls: null == useDebugUrls
          ? _self.useDebugUrls
          : useDebugUrls // ignore: cast_nullable_to_non_nullable
              as bool,
      lastConnectionAttempt: freezed == lastConnectionAttempt
          ? _self.lastConnectionAttempt
          : lastConnectionAttempt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastSuccessfulConnection: freezed == lastSuccessfulConnection
          ? _self.lastSuccessfulConnection
          : lastSuccessfulConnection // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
