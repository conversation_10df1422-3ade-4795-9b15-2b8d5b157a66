import 'package:common/models/player.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:dauntless/frameworks/game_config/game_config_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_event.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/models/base/resource_value.dart';
import 'package:dauntless/models/base/saved_game_state.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logging/logging.dart';

import 'match_save_state.dart';

abstract class MatchSaveEvent {}
// class LoadSaveStateEvent extends SaveStateEvent {}
class LoadMostRecentMatchSaveEvent extends MatchSaveEvent {}
class SaveMatchStateEvent extends MatchSaveEvent {}

class LoadNewMatchEvent extends MatchSaveEvent {
  final dynamic gameConfig;
  final List<dynamic> players;
  
  LoadNewMatchEvent({this.gameConfig, this.players = const []});
}

class MatchSaveManager extends Bloc<MatchSaveEvent, MatchSaveState> {
  final GameMatchManager _gameMatchManager;
  final GameConfigManager _gameConfigManager;
  final Logger _logger = Logger('MatchSaveManager');

  MatchSaveManager(this._gameMatchManager, this._gameConfigManager) : super(const MatchSaveState()) {
    // on<LoadSaveStateEvent>(_onLoadSaveStateEvent);
    on<LoadMostRecentMatchSaveEvent>(_onLoadMostRecentMatchSaveEvent);
    on<SaveMatchStateEvent>(_onSaveMatchStateEvent);
    on<LoadNewMatchEvent>(_onLoadNewMatchEvent);
  }

  // void _onLoadSaveStateEvent(
  //     LoadSaveStateEvent event, Emitter<SaveStateState> emit) async {
  //   emit(state.copyWith(processingStatus: ProcessingStatus.loading));
  //   // final mostRecentGameSave = await _appHistoryUseCase.getMostRecentSaveForGame(state.loadedGameConfig!);
  //   // final saveState = await _saveStateUseCase.loadSaveState();
  //   emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
  // }

  void _onLoadMostRecentMatchSaveEvent(
      LoadMostRecentMatchSaveEvent event, Emitter<MatchSaveState> emit) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));

    if (_gameConfigManager.state.loadedGameConfig == null) {
      _logger.severe('No game config loaded');
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
        errorMessage: 'No game config loaded'
      ));
      return;
    }

    // Legacy method no longer available
    _logger.info('Loading most recent match save is not implemented');
    emit(state.copyWith(
      processingStatus: ProcessingStatus.error,
      errorMessage: 'Loading most recent match save is not implemented'
    ));
    // This function is not implemented yet
    // TODO: implement load functionality
  }

  void _onSaveMatchStateEvent(
      SaveMatchStateEvent event, Emitter<MatchSaveState> emit) async {
    emit(state.copyWith(
      savedGameState: SavedGameState(matchState: _gameMatchManager.state),
        processingStatus: ProcessingStatus.loading));

    // Save functionality will be implemented later
    _logger.info('Saving match state (stub implementation)');
    // TODO: implement save functionality
    
    emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
  }

  void _onLoadNewMatchEvent(
      LoadNewMatchEvent event, Emitter<MatchSaveState> emit) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    
    // Log detailed debugging information
    _logger.info('LoadNewMatchEvent received with gameConfig: ${event.gameConfig != null ? "provided" : "null"}');
    _logger.info('Current _gameConfigManager.state.loadedGameConfig: ${_gameConfigManager.state.loadedGameConfig != null ? "loaded" : "null"}');
    
    if (event.gameConfig != null) {
      _logger.info('GameConfig from event - ID: ${event.gameConfig.id}, Name: ${event.gameConfig.name}');
      _logger.info('Players from event: ${event.players.length}');
      for (var i = 0; i < event.players.length; i++) {
        final player = event.players[i];
        _logger.info('Player[$i]: ${player.toString()}');
      }
    }
    
    // Get the game config - either from the event or from the manager
    final gameConfig = event.gameConfig ?? _gameConfigManager.state.loadedGameConfig;
    if (gameConfig == null) {
      _logger.severe('No game config loaded for new match');
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
        errorMessage: 'No game config loaded'
      ));
      return;
    }

    // Create a match config from the game config
    _logger.info('Creating new match state from gameConfig: ${gameConfig.id}');
    
    try {
      // Create a match config using the game config and player information from the event
      final hostId = event.players.isNotEmpty ? event.players.first.id : '';
      _logger.info('Using hostId: $hostId');
      
      final matchConfig = MatchConfig(
        gameId: gameConfig.id,
        // Use network mode if any players are network type
        selectedGameMode: event.players.any((p) => p.type == PlayerType.humanNetwork || p.type == PlayerType.botNetwork) 
            ? GameMode.server 
            : GameMode.hotSeat,
        hostId: hostId,
        matchId: const GenerateIdIfNeededConverter().fromJson(null),
        players: List<Player>.from(event.players),
        name: gameConfig.name,
      );
      
      _logger.info('Created matchConfig with ${matchConfig.players.length} players');
      
      // Create empty hands and resources for each player
      final Map<PlayerId, List<GameCard>> hands = {};
      final Map<PlayerId, Map<ResourceId, ResourceValue>> resources = {};
      
      // Initialize empty hands and resources for all players
      for (final player in matchConfig.players) {
        hands[player.id] = [];
        resources[player.id] = {};
        _logger.info('Initialized empty hand and resources for player: ${player.id}');
      }
      
      // Create a new game match state
      final newMatchState = GameMatchState(
        matchConfig: matchConfig,
        userPlayerId: matchConfig.hostId,
        turnCount: 0,
        hands: hands,
        resources: resources,
        loadedCardClasses: {}, // Start with empty card classes
      );
      
      _logger.info('Created new match state with ${matchConfig.players.length} players');
      _logger.info('Match config details: ${matchConfig.toJson()}');
      _logger.info('Players: ${matchConfig.players.map((p) => p.toJson()).join(', ')}');
      
      // Store relevant information in savedGameState for persistence if needed
      // We're mainly setting the status to loaded, actual state management is in GameMatchManager
      _logger.info('Updating MatchSaveManager state to loaded');
      emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
      
      // The most important part - propagate to the GameMatchManager
      _logger.info('Dispatching LoadMatchStateEvent to GameMatchManager');
      _gameMatchManager.add(LoadMatchStateEvent(newMatchState));
      _logger.info('Match start sequence completed');
    } catch (e) {
      _logger.severe('Error creating match state: $e');
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
        errorMessage: 'Error creating match: $e'
      ));
    }
  }
}