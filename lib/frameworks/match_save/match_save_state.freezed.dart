// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_save_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchSaveState {
  ProcessingStatus get processingStatus;
  SavedGameState? get savedGameState;
  String? get errorMessage;

  /// Create a copy of MatchSaveState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchSaveStateCopyWith<MatchSaveState> get copyWith =>
      _$MatchSaveStateCopyWithImpl<MatchSaveState>(
          this as MatchSaveState, _$identity);

  /// Serializes this MatchSaveState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchSaveState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.savedGameState, savedGameState) ||
                other.savedGameState == savedGameState) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, processingStatus, savedGameState, errorMessage);

  @override
  String toString() {
    return 'MatchSaveState(processingStatus: $processingStatus, savedGameState: $savedGameState, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class $MatchSaveStateCopyWith<$Res> {
  factory $MatchSaveStateCopyWith(
          MatchSaveState value, $Res Function(MatchSaveState) _then) =
      _$MatchSaveStateCopyWithImpl;
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      SavedGameState? savedGameState,
      String? errorMessage});

  $SavedGameStateCopyWith<$Res>? get savedGameState;
}

/// @nodoc
class _$MatchSaveStateCopyWithImpl<$Res>
    implements $MatchSaveStateCopyWith<$Res> {
  _$MatchSaveStateCopyWithImpl(this._self, this._then);

  final MatchSaveState _self;
  final $Res Function(MatchSaveState) _then;

  /// Create a copy of MatchSaveState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? processingStatus = null,
    Object? savedGameState = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_self.copyWith(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      savedGameState: freezed == savedGameState
          ? _self.savedGameState
          : savedGameState // ignore: cast_nullable_to_non_nullable
              as SavedGameState?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MatchSaveState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SavedGameStateCopyWith<$Res>? get savedGameState {
    if (_self.savedGameState == null) {
      return null;
    }

    return $SavedGameStateCopyWith<$Res>(_self.savedGameState!, (value) {
      return _then(_self.copyWith(savedGameState: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _MatchSaveState implements MatchSaveState {
  const _MatchSaveState(
      {this.processingStatus = ProcessingStatus.start,
      this.savedGameState,
      this.errorMessage});
  factory _MatchSaveState.fromJson(Map<String, dynamic> json) =>
      _$MatchSaveStateFromJson(json);

  @override
  @JsonKey()
  final ProcessingStatus processingStatus;
  @override
  final SavedGameState? savedGameState;
  @override
  final String? errorMessage;

  /// Create a copy of MatchSaveState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchSaveStateCopyWith<_MatchSaveState> get copyWith =>
      __$MatchSaveStateCopyWithImpl<_MatchSaveState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchSaveStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchSaveState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.savedGameState, savedGameState) ||
                other.savedGameState == savedGameState) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, processingStatus, savedGameState, errorMessage);

  @override
  String toString() {
    return 'MatchSaveState(processingStatus: $processingStatus, savedGameState: $savedGameState, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class _$MatchSaveStateCopyWith<$Res>
    implements $MatchSaveStateCopyWith<$Res> {
  factory _$MatchSaveStateCopyWith(
          _MatchSaveState value, $Res Function(_MatchSaveState) _then) =
      __$MatchSaveStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      SavedGameState? savedGameState,
      String? errorMessage});

  @override
  $SavedGameStateCopyWith<$Res>? get savedGameState;
}

/// @nodoc
class __$MatchSaveStateCopyWithImpl<$Res>
    implements _$MatchSaveStateCopyWith<$Res> {
  __$MatchSaveStateCopyWithImpl(this._self, this._then);

  final _MatchSaveState _self;
  final $Res Function(_MatchSaveState) _then;

  /// Create a copy of MatchSaveState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? processingStatus = null,
    Object? savedGameState = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_MatchSaveState(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      savedGameState: freezed == savedGameState
          ? _self.savedGameState
          : savedGameState // ignore: cast_nullable_to_non_nullable
              as SavedGameState?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MatchSaveState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SavedGameStateCopyWith<$Res>? get savedGameState {
    if (_self.savedGameState == null) {
      return null;
    }

    return $SavedGameStateCopyWith<$Res>(_self.savedGameState!, (value) {
      return _then(_self.copyWith(savedGameState: value));
    });
  }
}

// dart format on
