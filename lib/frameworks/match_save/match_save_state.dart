import 'package:dauntless/models/base/saved_game_state.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_save_state.freezed.dart';
part 'match_save_state.g.dart';

@freezed
abstract class MatchSaveState with _$MatchSaveState {
  const factory MatchSaveState({
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
    SavedGameState? savedGameState,
    String? errorMessage,
  }) = _MatchSaveState;

  factory MatchSaveState.fromJson(Map<String, dynamic> json) =>
      _$MatchSaveStateFromJson(json);
}