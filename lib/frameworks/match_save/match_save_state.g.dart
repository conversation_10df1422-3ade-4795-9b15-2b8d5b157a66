// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_save_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchSaveState _$MatchSaveStateFromJson(Map<String, dynamic> json) =>
    _MatchSaveState(
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      savedGameState: json['savedGameState'] == null
          ? null
          : SavedGameState.fromJson(
              json['savedGameState'] as Map<String, dynamic>),
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$MatchSaveStateToJson(_MatchSaveState instance) =>
    <String, dynamic>{
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'savedGameState': instance.savedGameState,
      'errorMessage': instance.errorMessage,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
