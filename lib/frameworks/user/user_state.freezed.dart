// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserState {
  User? get user;
  UserConfig? get userConfig;
  ProcessingStatus get processingStatus;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserStateCopyWith<UserState> get copyWith =>
      _$UserStateCopyWithImpl<UserState>(this as UserState, _$identity);

  /// Serializes this UserState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserState &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.userConfig, userConfig) ||
                other.userConfig == userConfig) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, user, userConfig, processingStatus);

  @override
  String toString() {
    return 'UserState(user: $user, userConfig: $userConfig, processingStatus: $processingStatus)';
  }
}

/// @nodoc
abstract mixin class $UserStateCopyWith<$Res> {
  factory $UserStateCopyWith(UserState value, $Res Function(UserState) _then) =
      _$UserStateCopyWithImpl;
  @useResult
  $Res call(
      {User? user, UserConfig? userConfig, ProcessingStatus processingStatus});

  $UserCopyWith<$Res>? get user;
  $UserConfigCopyWith<$Res>? get userConfig;
}

/// @nodoc
class _$UserStateCopyWithImpl<$Res> implements $UserStateCopyWith<$Res> {
  _$UserStateCopyWithImpl(this._self, this._then);

  final UserState _self;
  final $Res Function(UserState) _then;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? userConfig = freezed,
    Object? processingStatus = null,
  }) {
    return _then(_self.copyWith(
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userConfig: freezed == userConfig
          ? _self.userConfig
          : userConfig // ignore: cast_nullable_to_non_nullable
              as UserConfig?,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
    ));
  }

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserConfigCopyWith<$Res>? get userConfig {
    if (_self.userConfig == null) {
      return null;
    }

    return $UserConfigCopyWith<$Res>(_self.userConfig!, (value) {
      return _then(_self.copyWith(userConfig: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _UserState extends UserState {
  const _UserState(
      {this.user,
      this.userConfig,
      this.processingStatus = ProcessingStatus.start})
      : super._();
  factory _UserState.fromJson(Map<String, dynamic> json) =>
      _$UserStateFromJson(json);

  @override
  final User? user;
  @override
  final UserConfig? userConfig;
  @override
  @JsonKey()
  final ProcessingStatus processingStatus;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserStateCopyWith<_UserState> get copyWith =>
      __$UserStateCopyWithImpl<_UserState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserState &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.userConfig, userConfig) ||
                other.userConfig == userConfig) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, user, userConfig, processingStatus);

  @override
  String toString() {
    return 'UserState(user: $user, userConfig: $userConfig, processingStatus: $processingStatus)';
  }
}

/// @nodoc
abstract mixin class _$UserStateCopyWith<$Res>
    implements $UserStateCopyWith<$Res> {
  factory _$UserStateCopyWith(
          _UserState value, $Res Function(_UserState) _then) =
      __$UserStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {User? user, UserConfig? userConfig, ProcessingStatus processingStatus});

  @override
  $UserCopyWith<$Res>? get user;
  @override
  $UserConfigCopyWith<$Res>? get userConfig;
}

/// @nodoc
class __$UserStateCopyWithImpl<$Res> implements _$UserStateCopyWith<$Res> {
  __$UserStateCopyWithImpl(this._self, this._then);

  final _UserState _self;
  final $Res Function(_UserState) _then;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? user = freezed,
    Object? userConfig = freezed,
    Object? processingStatus = null,
  }) {
    return _then(_UserState(
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userConfig: freezed == userConfig
          ? _self.userConfig
          : userConfig // ignore: cast_nullable_to_non_nullable
              as UserConfig?,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
    ));
  }

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserConfigCopyWith<$Res>? get userConfig {
    if (_self.userConfig == null) {
      return null;
    }

    return $UserConfigCopyWith<$Res>(_self.userConfig!, (value) {
      return _then(_self.copyWith(userConfig: value));
    });
  }
}

// dart format on
