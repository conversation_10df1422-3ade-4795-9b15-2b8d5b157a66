// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserState _$UserStateFromJson(Map<String, dynamic> json) => _UserState(
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
      userConfig: json['userConfig'] == null
          ? null
          : UserConfig.fromJson(json['userConfig'] as Map<String, dynamic>),
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
    );

Map<String, dynamic> _$UserStateToJson(_UserState instance) =>
    <String, dynamic>{
      'user': instance.user,
      'userConfig': instance.userConfig,
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
