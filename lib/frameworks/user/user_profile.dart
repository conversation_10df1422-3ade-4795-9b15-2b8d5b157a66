import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';
part 'user_profile.g.dart';

/// Individual user profile data
@freezed
abstract class UserProfile with _$UserProfile {
  /// Private constructor for custom getters
  const UserProfile._();

  /// Create a user profile
  const factory UserProfile({
    required String id,
    required String name,
    String? displayName,
  }) = _UserProfile;

  /// JSON serialization
  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  /// Get display name or fallback to name
  String get effectiveDisplayName => displayName ?? name;
}
