// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserConfig {
  List<UserProfile> get profiles;
  String? get selectedProfileId;

  /// Create a copy of UserConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserConfigCopyWith<UserConfig> get copyWith =>
      _$UserConfigCopyWithImpl<UserConfig>(this as UserConfig, _$identity);

  /// Serializes this UserConfig to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserConfig &&
            const DeepCollectionEquality().equals(other.profiles, profiles) &&
            (identical(other.selectedProfileId, selectedProfileId) ||
                other.selectedProfileId == selectedProfileId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(profiles), selectedProfileId);

  @override
  String toString() {
    return 'UserConfig(profiles: $profiles, selectedProfileId: $selectedProfileId)';
  }
}

/// @nodoc
abstract mixin class $UserConfigCopyWith<$Res> {
  factory $UserConfigCopyWith(
          UserConfig value, $Res Function(UserConfig) _then) =
      _$UserConfigCopyWithImpl;
  @useResult
  $Res call({List<UserProfile> profiles, String? selectedProfileId});
}

/// @nodoc
class _$UserConfigCopyWithImpl<$Res> implements $UserConfigCopyWith<$Res> {
  _$UserConfigCopyWithImpl(this._self, this._then);

  final UserConfig _self;
  final $Res Function(UserConfig) _then;

  /// Create a copy of UserConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profiles = null,
    Object? selectedProfileId = freezed,
  }) {
    return _then(_self.copyWith(
      profiles: null == profiles
          ? _self.profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<UserProfile>,
      selectedProfileId: freezed == selectedProfileId
          ? _self.selectedProfileId
          : selectedProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _UserConfig extends UserConfig {
  const _UserConfig(
      {final List<UserProfile> profiles = const [], this.selectedProfileId})
      : _profiles = profiles,
        super._();
  factory _UserConfig.fromJson(Map<String, dynamic> json) =>
      _$UserConfigFromJson(json);

  final List<UserProfile> _profiles;
  @override
  @JsonKey()
  List<UserProfile> get profiles {
    if (_profiles is EqualUnmodifiableListView) return _profiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_profiles);
  }

  @override
  final String? selectedProfileId;

  /// Create a copy of UserConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserConfigCopyWith<_UserConfig> get copyWith =>
      __$UserConfigCopyWithImpl<_UserConfig>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserConfigToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserConfig &&
            const DeepCollectionEquality().equals(other._profiles, _profiles) &&
            (identical(other.selectedProfileId, selectedProfileId) ||
                other.selectedProfileId == selectedProfileId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_profiles), selectedProfileId);

  @override
  String toString() {
    return 'UserConfig(profiles: $profiles, selectedProfileId: $selectedProfileId)';
  }
}

/// @nodoc
abstract mixin class _$UserConfigCopyWith<$Res>
    implements $UserConfigCopyWith<$Res> {
  factory _$UserConfigCopyWith(
          _UserConfig value, $Res Function(_UserConfig) _then) =
      __$UserConfigCopyWithImpl;
  @override
  @useResult
  $Res call({List<UserProfile> profiles, String? selectedProfileId});
}

/// @nodoc
class __$UserConfigCopyWithImpl<$Res> implements _$UserConfigCopyWith<$Res> {
  __$UserConfigCopyWithImpl(this._self, this._then);

  final _UserConfig _self;
  final $Res Function(_UserConfig) _then;

  /// Create a copy of UserConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? profiles = null,
    Object? selectedProfileId = freezed,
  }) {
    return _then(_UserConfig(
      profiles: null == profiles
          ? _self._profiles
          : profiles // ignore: cast_nullable_to_non_nullable
              as List<UserProfile>,
      selectedProfileId: freezed == selectedProfileId
          ? _self.selectedProfileId
          : selectedProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
