import 'package:common/models/user.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/frameworks/user/user_config.dart';
import 'package:dauntless/frameworks/user/user_profile.dart';

part 'user_state.freezed.dart';
part 'user_state.g.dart';

@freezed
abstract class UserState with _$UserState {
  /// Private constructor for custom getters
  const UserState._();

  /// Create a user state
  const factory UserState({
    User? user,
    UserConfig? userConfig,
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
  }) = _UserState;

  /// JSON serialization
  factory UserState.fromJson(Map<String, dynamic> json) => _$UserStateFromJson(json);

  /// Whether the user has a username
  bool get hasUsername => user?.id.isNotEmpty == true;

  /// Whether profiles are loading
  bool get isLoading => processingStatus == ProcessingStatus.loading;

  /// Whether there was an error
  bool get hasError => processingStatus == ProcessingStatus.error;

  /// Get available profiles
  List<UserProfile> get profiles => userConfig?.profiles ?? [];

  /// Get selected profile
  UserProfile? get selectedProfile => userConfig?.selected;
}