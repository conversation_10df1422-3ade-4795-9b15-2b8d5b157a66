import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'user_profile.dart';

part 'user_config.freezed.dart';
part 'user_config.g.dart';

/// Configuration for user profiles
@freezed
abstract class UserConfig with _$UserConfig {
  /// Private constructor for custom getters
  const UserConfig._();

  /// Create a user configuration
  const factory UserConfig({
    @Default([]) List<UserProfile> profiles,
    String? selectedProfileId,
  }) = _UserConfig;

  /// JSON serialization
  factory UserConfig.fromJson(Map<String, dynamic> json) =>
      _$UserConfigFromJson(json);

  /// Get the currently selected profile
  UserProfile? get selected => profiles.firstWhereOrNull((p) => p.id == selectedProfileId);

  /// Check if a profile with the given ID exists
  bool hasProfile(String profileId) => profiles.any((p) => p.id == profileId);

  /// Get profile by ID
  UserProfile? getProfile(String profileId) => profiles.firstWhereOrNull((p) => p.id == profileId);
}
