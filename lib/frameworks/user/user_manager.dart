import 'dart:async';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/frameworks/user/user_config.dart';
import 'package:dauntless/frameworks/user/user_profile.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/user_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/models/user.dart';
import 'package:injectable/injectable.dart';

abstract class UserEvent {}

class InitializeUserEvent extends UserEvent {}

class LoadUserConfigEvent extends UserEvent {}

class AddUserProfileEvent extends UserEvent {
  final UserProfile profile;
  
  AddUserProfileEvent({required this.profile});
}

class UpdateUserProfileEvent extends UserEvent {
  final UserProfile profile;
  
  UpdateUserProfileEvent({required this.profile});
}

class RemoveUserProfileEvent extends UserEvent {
  final String profileId;
  
  RemoveUserProfileEvent({required this.profileId});
}

class SetSelectedUserProfileEvent extends UserEvent {
  final String? profileId;
  
  SetSelectedUserProfileEvent({required this.profileId});
}

@singleton
class UserManager extends Bloc<UserEvent, UserState> {
  final UserUseCase _useCase;

  UserManager(this._useCase)
      : super(const UserState(user: null)) {
    on<InitializeUserEvent>(_onInitializeUser);
    on<LoadUserConfigEvent>(_onLoadUserConfig);
    on<AddUserProfileEvent>(_onAddUserProfile);
    on<UpdateUserProfileEvent>(_onUpdateUserProfile);
    on<RemoveUserProfileEvent>(_onRemoveUserProfile);
    on<SetSelectedUserProfileEvent>(_onSetSelectedUserProfile);
    
    // Initialize user config on startup
    add(InitializeUserEvent());
  }

  /// Initialize user manager and load config
  FutureOr<void> _onInitializeUser(
    InitializeUserEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      final userConfig = await _useCase.loadUserConfig();
      
      // Create User object from selected profile
      User? currentUser;
      if (userConfig.selected != null) {
        currentUser = User(id: userConfig.selected!.name);
      }

      emit(state.copyWith(
        user: currentUser,
        userConfig: userConfig,
        processingStatus: ProcessingStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(processingStatus: ProcessingStatus.error));
      print('USER_MANAGER: Failed to initialize user: $e');
    }
  }

  /// Load user configuration
  FutureOr<void> _onLoadUserConfig(
    LoadUserConfigEvent event,
    Emitter<UserState> emit,
  ) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    add(InitializeUserEvent());
  }

  /// Add a new user profile
  FutureOr<void> _onAddUserProfile(
    AddUserProfileEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      final currentConfig = state.userConfig ?? const UserConfig();
      final updatedConfig = await _useCase.addUserProfile(currentConfig, event.profile);
      
      emit(state.copyWith(
        userConfig: updatedConfig,
        processingStatus: ProcessingStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(processingStatus: ProcessingStatus.error));
      print('USER_MANAGER: Failed to add user profile: $e');
    }
  }

  /// Update an existing user profile
  FutureOr<void> _onUpdateUserProfile(
    UpdateUserProfileEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      final currentConfig = state.userConfig ?? const UserConfig();
      final updatedConfig = await _useCase.updateUserProfile(currentConfig, event.profile);
      
      // Update current user if this is the selected profile
      User? currentUser = state.user;
      if (updatedConfig.selectedProfileId == event.profile.id) {
        currentUser = User(id: event.profile.name);
      }

      emit(state.copyWith(
        user: currentUser,
        userConfig: updatedConfig,
        processingStatus: ProcessingStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(processingStatus: ProcessingStatus.error));
      print('USER_MANAGER: Failed to update user profile: $e');
    }
  }

  /// Remove a user profile
  FutureOr<void> _onRemoveUserProfile(
    RemoveUserProfileEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      final currentConfig = state.userConfig ?? const UserConfig();
      final updatedConfig = await _useCase.removeUserProfile(currentConfig, event.profileId);
      
      // Update current user if needed
      User? currentUser;
      if (updatedConfig.selected != null) {
        currentUser = User(id: updatedConfig.selected!.name);
      }

      emit(state.copyWith(
        user: currentUser,
        userConfig: updatedConfig,
        processingStatus: ProcessingStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(processingStatus: ProcessingStatus.error));
      print('USER_MANAGER: Failed to remove user profile: $e');
    }
  }

  /// Set the selected user profile
  FutureOr<void> _onSetSelectedUserProfile(
    SetSelectedUserProfileEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      final currentConfig = state.userConfig ?? const UserConfig();
      final updatedConfig = await _useCase.setSelectedProfile(currentConfig, event.profileId);
      
      // Update current user
      User? currentUser;
      if (updatedConfig.selected != null) {
        currentUser = User(id: updatedConfig.selected!.name);
      }

      emit(state.copyWith(
        user: currentUser,
        userConfig: updatedConfig,
        processingStatus: ProcessingStatus.loaded,
      ));
    } catch (e) {
      emit(state.copyWith(processingStatus: ProcessingStatus.error));
      print('USER_MANAGER: Failed to set selected user profile: $e');
    }
  }
}
