// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'websocket_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$WebSocketState {
  WebSocketConnectionStatus get connectionStatus;
  String? get errorMessage;
  bool get isSubscribedToOpenMatches;
  List<GameMatch> get openMatches;
  Map<String, Map<String, dynamic>> get topicData;
  Set<String> get activeTopics;

  /// Create a copy of WebSocketState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WebSocketStateCopyWith<WebSocketState> get copyWith =>
      _$WebSocketStateCopyWithImpl<WebSocketState>(
          this as WebSocketState, _$identity);

  /// Serializes this WebSocketState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WebSocketState &&
            (identical(other.connectionStatus, connectionStatus) ||
                other.connectionStatus == connectionStatus) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isSubscribedToOpenMatches,
                    isSubscribedToOpenMatches) ||
                other.isSubscribedToOpenMatches == isSubscribedToOpenMatches) &&
            const DeepCollectionEquality()
                .equals(other.openMatches, openMatches) &&
            const DeepCollectionEquality().equals(other.topicData, topicData) &&
            const DeepCollectionEquality()
                .equals(other.activeTopics, activeTopics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      connectionStatus,
      errorMessage,
      isSubscribedToOpenMatches,
      const DeepCollectionEquality().hash(openMatches),
      const DeepCollectionEquality().hash(topicData),
      const DeepCollectionEquality().hash(activeTopics));

  @override
  String toString() {
    return 'WebSocketState(connectionStatus: $connectionStatus, errorMessage: $errorMessage, isSubscribedToOpenMatches: $isSubscribedToOpenMatches, openMatches: $openMatches, topicData: $topicData, activeTopics: $activeTopics)';
  }
}

/// @nodoc
abstract mixin class $WebSocketStateCopyWith<$Res> {
  factory $WebSocketStateCopyWith(
          WebSocketState value, $Res Function(WebSocketState) _then) =
      _$WebSocketStateCopyWithImpl;
  @useResult
  $Res call(
      {WebSocketConnectionStatus connectionStatus,
      String? errorMessage,
      bool isSubscribedToOpenMatches,
      List<GameMatch> openMatches,
      Map<String, Map<String, dynamic>> topicData,
      Set<String> activeTopics});
}

/// @nodoc
class _$WebSocketStateCopyWithImpl<$Res>
    implements $WebSocketStateCopyWith<$Res> {
  _$WebSocketStateCopyWithImpl(this._self, this._then);

  final WebSocketState _self;
  final $Res Function(WebSocketState) _then;

  /// Create a copy of WebSocketState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? connectionStatus = null,
    Object? errorMessage = freezed,
    Object? isSubscribedToOpenMatches = null,
    Object? openMatches = null,
    Object? topicData = null,
    Object? activeTopics = null,
  }) {
    return _then(_self.copyWith(
      connectionStatus: null == connectionStatus
          ? _self.connectionStatus
          : connectionStatus // ignore: cast_nullable_to_non_nullable
              as WebSocketConnectionStatus,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isSubscribedToOpenMatches: null == isSubscribedToOpenMatches
          ? _self.isSubscribedToOpenMatches
          : isSubscribedToOpenMatches // ignore: cast_nullable_to_non_nullable
              as bool,
      openMatches: null == openMatches
          ? _self.openMatches
          : openMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      topicData: null == topicData
          ? _self.topicData
          : topicData // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, dynamic>>,
      activeTopics: null == activeTopics
          ? _self.activeTopics
          : activeTopics // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _WebSocketState implements WebSocketState {
  const _WebSocketState(
      {this.connectionStatus = WebSocketConnectionStatus.disconnected,
      this.errorMessage,
      this.isSubscribedToOpenMatches = false,
      final List<GameMatch> openMatches = const [],
      final Map<String, Map<String, dynamic>> topicData = const {},
      final Set<String> activeTopics = const {}})
      : _openMatches = openMatches,
        _topicData = topicData,
        _activeTopics = activeTopics;
  factory _WebSocketState.fromJson(Map<String, dynamic> json) =>
      _$WebSocketStateFromJson(json);

  @override
  @JsonKey()
  final WebSocketConnectionStatus connectionStatus;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final bool isSubscribedToOpenMatches;
  final List<GameMatch> _openMatches;
  @override
  @JsonKey()
  List<GameMatch> get openMatches {
    if (_openMatches is EqualUnmodifiableListView) return _openMatches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_openMatches);
  }

  final Map<String, Map<String, dynamic>> _topicData;
  @override
  @JsonKey()
  Map<String, Map<String, dynamic>> get topicData {
    if (_topicData is EqualUnmodifiableMapView) return _topicData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_topicData);
  }

  final Set<String> _activeTopics;
  @override
  @JsonKey()
  Set<String> get activeTopics {
    if (_activeTopics is EqualUnmodifiableSetView) return _activeTopics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_activeTopics);
  }

  /// Create a copy of WebSocketState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WebSocketStateCopyWith<_WebSocketState> get copyWith =>
      __$WebSocketStateCopyWithImpl<_WebSocketState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WebSocketStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WebSocketState &&
            (identical(other.connectionStatus, connectionStatus) ||
                other.connectionStatus == connectionStatus) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isSubscribedToOpenMatches,
                    isSubscribedToOpenMatches) ||
                other.isSubscribedToOpenMatches == isSubscribedToOpenMatches) &&
            const DeepCollectionEquality()
                .equals(other._openMatches, _openMatches) &&
            const DeepCollectionEquality()
                .equals(other._topicData, _topicData) &&
            const DeepCollectionEquality()
                .equals(other._activeTopics, _activeTopics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      connectionStatus,
      errorMessage,
      isSubscribedToOpenMatches,
      const DeepCollectionEquality().hash(_openMatches),
      const DeepCollectionEquality().hash(_topicData),
      const DeepCollectionEquality().hash(_activeTopics));

  @override
  String toString() {
    return 'WebSocketState(connectionStatus: $connectionStatus, errorMessage: $errorMessage, isSubscribedToOpenMatches: $isSubscribedToOpenMatches, openMatches: $openMatches, topicData: $topicData, activeTopics: $activeTopics)';
  }
}

/// @nodoc
abstract mixin class _$WebSocketStateCopyWith<$Res>
    implements $WebSocketStateCopyWith<$Res> {
  factory _$WebSocketStateCopyWith(
          _WebSocketState value, $Res Function(_WebSocketState) _then) =
      __$WebSocketStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {WebSocketConnectionStatus connectionStatus,
      String? errorMessage,
      bool isSubscribedToOpenMatches,
      List<GameMatch> openMatches,
      Map<String, Map<String, dynamic>> topicData,
      Set<String> activeTopics});
}

/// @nodoc
class __$WebSocketStateCopyWithImpl<$Res>
    implements _$WebSocketStateCopyWith<$Res> {
  __$WebSocketStateCopyWithImpl(this._self, this._then);

  final _WebSocketState _self;
  final $Res Function(_WebSocketState) _then;

  /// Create a copy of WebSocketState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? connectionStatus = null,
    Object? errorMessage = freezed,
    Object? isSubscribedToOpenMatches = null,
    Object? openMatches = null,
    Object? topicData = null,
    Object? activeTopics = null,
  }) {
    return _then(_WebSocketState(
      connectionStatus: null == connectionStatus
          ? _self.connectionStatus
          : connectionStatus // ignore: cast_nullable_to_non_nullable
              as WebSocketConnectionStatus,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isSubscribedToOpenMatches: null == isSubscribedToOpenMatches
          ? _self.isSubscribedToOpenMatches
          : isSubscribedToOpenMatches // ignore: cast_nullable_to_non_nullable
              as bool,
      openMatches: null == openMatches
          ? _self._openMatches
          : openMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      topicData: null == topicData
          ? _self._topicData
          : topicData // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, dynamic>>,
      activeTopics: null == activeTopics
          ? _self._activeTopics
          : activeTopics // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ));
  }
}

// dart format on
