// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WebSocketState _$WebSocketStateFromJson(Map<String, dynamic> json) =>
    _WebSocketState(
      connectionStatus: $enumDecodeNullable(
              _$WebSocketConnectionStatusEnumMap, json['connectionStatus']) ??
          WebSocketConnectionStatus.disconnected,
      errorMessage: json['errorMessage'] as String?,
      isSubscribedToOpenMatches:
          json['isSubscribedToOpenMatches'] as bool? ?? false,
      openMatches: (json['openMatches'] as List<dynamic>?)
              ?.map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      topicData: (json['topicData'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as Map<String, dynamic>),
          ) ??
          const {},
      activeTopics: (json['activeTopics'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toSet() ??
          const {},
    );

Map<String, dynamic> _$WebSocketStateToJson(_WebSocketState instance) =>
    <String, dynamic>{
      'connectionStatus':
          _$WebSocketConnectionStatusEnumMap[instance.connectionStatus]!,
      'errorMessage': instance.errorMessage,
      'isSubscribedToOpenMatches': instance.isSubscribedToOpenMatches,
      'openMatches': instance.openMatches,
      'topicData': instance.topicData,
      'activeTopics': instance.activeTopics.toList(),
    };

const _$WebSocketConnectionStatusEnumMap = {
  WebSocketConnectionStatus.disconnected: 'disconnected',
  WebSocketConnectionStatus.connecting: 'connecting',
  WebSocketConnectionStatus.connected: 'connected',
  WebSocketConnectionStatus.error: 'error',
};
