import 'package:common/models/game_match.dart' as project_match;
import 'package:dauntless/models/base/game_match.dart';
import 'websocket_state.dart';

abstract class WebSocketEvent {}

class ConnectWebSocketEvent extends WebSocketEvent {}

class DisconnectWebSocketEvent extends WebSocketEvent {}

class SubscribeToMatchEvent extends WebSocketEvent {
  final String matchId;
  SubscribeToMatchEvent(this.matchId);
}

class UnsubscribeFromMatchEvent extends WebSocketEvent {
  final String matchId;
  UnsubscribeFromMatchEvent(this.matchId);
}

// class RegisterMatchFactoriesEvent extends WebSocketEvent {}

/// Event to handle real-time connection status updates
class UpdateConnectionStatusEvent extends WebSocketEvent {
  final WebSocketConnectionStatus status;
  final String? errorMessage;
  
  UpdateConnectionStatusEvent(this.status, {this.errorMessage});
}

/// Event to subscribe to a specific topic
class SubscribeToTopic<PERSON>vent extends WebSocketEvent {
  final String topic;
  final void Function(dynamic)? listener;
  
  SubscribeToTopicEvent(this.topic, {this.listener});
}

/// Event to unsubscribe from a specific topic
class Unsubscribe<PERSON>romTopic<PERSON>vent extends WebSocketEvent {
  final String topic;
  
  UnsubscribeFromTopicEvent(this.topic);
}

/// Event to handle a topic update
class TopicUpdateEvent extends WebSocketEvent {
  final String topic;
  final Map<String, dynamic> data;
  
  TopicUpdateEvent(this.topic, this.data);
}

/// Event to send a message to the WebSocket server
class SendMessageEvent extends WebSocketEvent {
  final Map<String, dynamic> message;
  
  SendMessageEvent(this.message);
}