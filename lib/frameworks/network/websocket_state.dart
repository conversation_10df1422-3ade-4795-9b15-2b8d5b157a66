import 'package:dauntless/models/base/game_match.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'websocket_state.freezed.dart';
part 'websocket_state.g.dart';

enum WebSocketConnectionStatus {
  disconnected,
  connecting,
  connected,
  error
}

@freezed
abstract class WebSocketState with _$WebSocketState {
  const factory WebSocketState({
    @Default(WebSocketConnectionStatus.disconnected) WebSocketConnectionStatus connectionStatus,
    String? errorMessage,
    @Default(false) bool isSubscribedToOpenMatches,
    @Default([]) List<GameMatch> openMatches,
    @Default({}) Map<String, Map<String, dynamic>> topicData,
    @Default({}) Set<String> activeTopics,
  }) = _WebSocketState;

  factory WebSocketState.fromJson(Map<String, dynamic> json) =>
      _$WebSocketStateFromJson(json);
}
