import 'dart:async';

import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/match_save/match_save_state.dart';
import 'package:dauntless/models/base/game_match.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';

import 'websocket_event.dart';
import 'websocket_state.dart';

/// Manager for handling WebSocket real-time updates using Bloc pattern
@singleton // TODO: rename this -- really like an EventBus? Doesn't matter whether it's through a websocket or not
class WebSocketManager extends Bloc<WebSocketEvent, WebSocketState> {
  final ServerNotificationsUseCase _serverNotificationsUseCase;
  StreamSubscription<MatchSaveState>? _matchUpdateSubscription;
  StreamSubscription<bool>? _connectionStatusSubscription;
  final _matchSpecificController =
      StreamController<Map<String, MatchSaveState>>.broadcast();
  final _openMatchesController = StreamController<List<GameMatch>>.broadcast();
  final _topicControllers = <String, StreamController<Map<String, dynamic>>>{};

  /// Stream of game_match-specific updates
  // Stream<Map<String, GameMatch>> get matchSpecificUpdates => _matchSpecificController.stream;

  /// Stream of open matches updates - this is the public API other components subscribe to
  Stream<List<GameMatch>> get openMatchesUpdates =>
      _openMatchesController.stream;

  /// Send a message to the WebSocket server
  void sendMessage(Map<String, dynamic> message) {
    add(SendMessageEvent(message));
  }

  /// Get stream for a specific topic
  Stream<Map<String, dynamic>> getTopicStream(String topic) {
    _topicControllers[topic] ??=
        StreamController<Map<String, dynamic>>.broadcast();
    return _topicControllers[topic]!.stream.map((mapResult) => mapResult[topic]);
  }

  WebSocketManager(this._serverNotificationsUseCase) : super(const WebSocketState()) {
    on<ConnectWebSocketEvent>(_onConnectWebSocketEvent);
    on<DisconnectWebSocketEvent>(_onDisconnectWebSocketEvent);
    on<UnsubscribeFromMatchEvent>(_onUnsubscribeFromMatchEvent);
    // on<RegisterMatchFactoriesEvent>(_onRegisterMatchFactoriesEvent);
    on<UpdateConnectionStatusEvent>(_onUpdateConnectionStatusEvent);
    // on<SubscribeToOpenMatchesEvent>(_onSubscribeToOpenMatchesEvent);
    // on<UnsubscribeFromOpenMatchesEvent>(_onUnsubscribeFromOpenMatchesEvent);
    // on<OpenMatchesUpdateEvent>(_onOpenMatchesUpdateEvent);
    on<SubscribeToTopicEvent>(_onSubscribeToTopicEvent);
    on<UnsubscribeFromTopicEvent>(_onUnsubscribeFromTopicEvent);
    on<TopicUpdateEvent>(_onTopicUpdateEvent);
    on<SendMessageEvent>(_onSendMessageEvent);

    // Listen for game_match updates
    _setupMatchUpdateListener();

    // Register game_match factories
    // add(RegisterMatchFactoriesEvent());
  }

  /// Setup listener for game_match updates and connection status
  void _setupMatchUpdateListener() {
    // _matchUpdateSubscription = _serverNotificationsUseCase.matchUpdates.listen(_onMatchUpdate);

    // Subscribe to connection status changes
    _connectionStatusSubscription =
        _serverNotificationsUseCase.connectionStatusUpdates.listen((isConnected) {
      // Update the state based on the connection status
      if (isConnected) {
        add(UpdateConnectionStatusEvent(WebSocketConnectionStatus.connected));
      } else {
        add(UpdateConnectionStatusEvent(
            WebSocketConnectionStatus.disconnected));
      }
    });

    // Subscribe to open matches updates
    _serverNotificationsUseCase.openMatchesUpdates.listen((matches) {
      // add(OpenMatchesUpdateEvent(matches));
    });
  }

  /// Handle Connect WebSocket Event
  Future<void> _onConnectWebSocketEvent(
      ConnectWebSocketEvent event, Emitter<WebSocketState> emit) async {
    emit(
        state.copyWith(connectionStatus: WebSocketConnectionStatus.connecting));

    /// handle no ws url case // TODO: clearly define separate biz/UI logic
    final serverEnvironmentConfig = GetIt.I<ServerEnvironmentManager>().state.profile;
    final wsUrl = serverEnvironmentConfig?.wsUrl;
    if (wsUrl == null) {
      emit(state.copyWith(
        connectionStatus: WebSocketConnectionStatus.error,
        errorMessage: 'No WebSocket URL found',
      ));
      return;
    }

    try {
      await _serverNotificationsUseCase.connect();
      emit(state.copyWith(
          connectionStatus: WebSocketConnectionStatus.connected));
    } catch (e) {
      emit(state.copyWith(
        connectionStatus: WebSocketConnectionStatus.error,
        errorMessage: 'Failed to connect: ${e.toString()}',
      ));
    }
  }

  /// Handle Disconnect WebSocket Event
  void _onDisconnectWebSocketEvent(
      DisconnectWebSocketEvent event, Emitter<WebSocketState> emit) {
    _serverNotificationsUseCase.disconnect();
    emit(state.copyWith(
        connectionStatus: WebSocketConnectionStatus.disconnected));
  }

  /// Handle Unsubscribe from Match Event
  void _onUnsubscribeFromMatchEvent(
      UnsubscribeFromMatchEvent event, Emitter<WebSocketState> emit) {
    // _serverNotificationsUseCase.unsubscribeFromMatch(event.matchId);
    //
    // final updatedMatches = Map<String, GameMatch>.from(state.activeMatches);
    // updatedMatches.remove(event.matchId);
    //
    // emit(state.copyWith(activeMatches: updatedMatches));
  }

  /// Handle Register Match Factories Event
  // void _onRegisterMatchFactoriesEvent(
  //     RegisterMatchFactoriesEvent event, Emitter<WebSocketState> emit) {
  //   // Register different game_match types here as needed
  //
  //   // Register the Liberator game factory
  //   _serverNotificationsUseCase.registerMatchFactory('liberator', (data) => GameMatch.fromJson(data));
  //
  //   print('WebSocketManager: Registered match factory for game type: liberator');
  // }

  /// Handle game_match update from WebSocket
  // void _onMatchUpdate(GameMatch match) {
  //   final matchId = match.id;
  //
  //   // Create a game_match update event to use in the event handler instead of directly calling emit
  //   // This will trigger the event handler which has access to the emit function
  //   add(MatchUpdateEvent(match));
  //
  //   // Notify listeners of the specific game_match update for the stream
  //   _matchSpecificController.add({matchId: match});
  // }
  //
  // /// Handle game_match update event
  // void _onMatchUpdateEvent(
  //     MatchUpdateEvent event, Emitter<WebSocketState> emit) {
  //   final matchId = event.match.id;
  //
  //   // Update state with new game_match data
  //   final updatedMatches = Map<String, GameMatch>.from(state.activeMatches);
  //   updatedMatches[matchId] = event.match;
  //
  //   emit(state.copyWith(activeMatches: updatedMatches));
  // }

  /// Handle connection status update event
  void _onUpdateConnectionStatusEvent(
      UpdateConnectionStatusEvent event, Emitter<WebSocketState> emit) {
    emit(state.copyWith(
      connectionStatus: event.status,
      errorMessage: event.errorMessage,
    ));
  }

  /// Subscribe to open matches updates
  // void _onSubscribeToOpenMatchesEvent(
  //     SubscribeToOpenMatchesEvent event, Emitter<WebSocketState> emit) async {
  //   /// only if already connected
  //   if (state.connectionStatus != WebSocketConnectionStatus.connected) {
  //     return;
  //   }
  //
  //   try {
  //     await _serverNotificationsUseCase.subscribeToOpenMatches();
  //     emit(state.copyWith(isSubscribedToOpenMatches: true));
  //   } catch (e) {
  //     emit(state.copyWith(
  //       errorMessage: 'Failed to subscribe to open matches: ${e.toString()}',
  //     ));
  //   }
  // }

  /// Unsubscribe from open matches updates
  // void _onUnsubscribeFromOpenMatchesEvent(
  //     UnsubscribeFromOpenMatchesEvent event, Emitter<WebSocketState> emit) {
  //   _serverNotificationsUseCase.unsubscribeFromOpenMatches();
  //   emit(state.copyWith(isSubscribedToOpenMatches: false));
  // }
  //
  // /// Handle open matches update event
  // void _onOpenMatchesUpdateEvent(
  //     OpenMatchesUpdateEvent event, Emitter<WebSocketState> emit) {
  //   // Notify listeners of the open matches update
  //   _openMatchesController.add(event.matches);
  //
  //   // Update the state with the new open matches
  //   emit(state.copyWith(openMatches: event.matches));
  // }

  /// Handle subscribe to topic event
  Future<void> _onSubscribeToTopicEvent(
      SubscribeToTopicEvent event, Emitter<WebSocketState> emit) async {
    if (state.connectionStatus != WebSocketConnectionStatus.connected) {
      return;
    }
    try {
      await _serverNotificationsUseCase.subscribeToTopic(event.topic);

      // Create controller for this topic if it doesn't exist
      _topicControllers[event.topic] ??=
          StreamController<Map<String, dynamic>>.broadcast();

      if (event.listener != null) {
        getTopicStream(event.topic).listen(event.listener);
      }


      // Setup listener for this topic if not already set up
      _serverNotificationsUseCase.getTopicStream(event.topic).listen((data) {
        if (data is Map<String, dynamic>) {
          // Forward to the local controller
          _topicControllers[event.topic]?.add(data);

          // Also create an event for the bloc
          add(TopicUpdateEvent(event.topic, data));
        }
      });

      // Update state with active topics
      final updatedTopics = Set<String>.from(state.activeTopics)
        ..add(event.topic);
      emit(state.copyWith(activeTopics: updatedTopics));
    } catch (e) {
      emit(state.copyWith(
        errorMessage:
            'Failed to subscribe to topic ${event.topic}: ${e.toString()}',
      ));
    }
  }

  /// Handle unsubscribe from topic event
  void _onUnsubscribeFromTopicEvent(
      UnsubscribeFromTopicEvent event, Emitter<WebSocketState> emit) {
    _serverNotificationsUseCase.unsubscribeFromTopic(event.topic);

    // Update state with active topics
    final updatedTopics = Set<String>.from(state.activeTopics)
      ..remove(event.topic);
    emit(state.copyWith(activeTopics: updatedTopics));
  }

  /// Handle topic update event
  void _onTopicUpdateEvent(
      TopicUpdateEvent event, Emitter<WebSocketState> emit) {
    // Update the state with the topic data
    final updatedTopicData =
        Map<String, Map<String, dynamic>>.from(state.topicData);
    updatedTopicData[event.topic] = event.data;

    emit(state.copyWith(topicData: updatedTopicData));
  }

  /// Handle send message event
  void _onSendMessageEvent(
      SendMessageEvent event, Emitter<WebSocketState> emit) {
    try {
      _serverNotificationsUseCase.sendMessage(event.message);
    } catch (e) {
      emit(state.copyWith(
        errorMessage: 'Failed to send message: ${e.toString()}',
      ));
    }
  }

  @override
  Future<void> close() {
    _matchUpdateSubscription?.cancel();
    _connectionStatusSubscription?.cancel();
    _matchSpecificController.close();
    _openMatchesController.close();

    // Close all topic controllers
    for (final controller in _topicControllers.values) {
      controller.close();
    }

    return super.close();
  }
}
