// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_config_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GameConfigState {
  ProcessingStatus get processingStatus;
  List<GameConfig> get availableConfigs;
  GameConfig? get loadedGameConfig;
  String? get basePath;

  /// Create a copy of GameConfigState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GameConfigStateCopyWith<GameConfigState> get copyWith =>
      _$GameConfigStateCopyWithImpl<GameConfigState>(
          this as GameConfigState, _$identity);

  /// Serializes this GameConfigState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GameConfigState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality()
                .equals(other.availableConfigs, availableConfigs) &&
            (identical(other.loadedGameConfig, loadedGameConfig) ||
                other.loadedGameConfig == loadedGameConfig) &&
            (identical(other.basePath, basePath) ||
                other.basePath == basePath));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      const DeepCollectionEquality().hash(availableConfigs),
      loadedGameConfig,
      basePath);

  @override
  String toString() {
    return 'GameConfigState(processingStatus: $processingStatus, availableConfigs: $availableConfigs, loadedGameConfig: $loadedGameConfig, basePath: $basePath)';
  }
}

/// @nodoc
abstract mixin class $GameConfigStateCopyWith<$Res> {
  factory $GameConfigStateCopyWith(
          GameConfigState value, $Res Function(GameConfigState) _then) =
      _$GameConfigStateCopyWithImpl;
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      List<GameConfig> availableConfigs,
      GameConfig? loadedGameConfig,
      String? basePath});

  $GameConfigCopyWith<$Res>? get loadedGameConfig;
}

/// @nodoc
class _$GameConfigStateCopyWithImpl<$Res>
    implements $GameConfigStateCopyWith<$Res> {
  _$GameConfigStateCopyWithImpl(this._self, this._then);

  final GameConfigState _self;
  final $Res Function(GameConfigState) _then;

  /// Create a copy of GameConfigState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? processingStatus = null,
    Object? availableConfigs = null,
    Object? loadedGameConfig = freezed,
    Object? basePath = freezed,
  }) {
    return _then(_self.copyWith(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      availableConfigs: null == availableConfigs
          ? _self.availableConfigs
          : availableConfigs // ignore: cast_nullable_to_non_nullable
              as List<GameConfig>,
      loadedGameConfig: freezed == loadedGameConfig
          ? _self.loadedGameConfig
          : loadedGameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig?,
      basePath: freezed == basePath
          ? _self.basePath
          : basePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of GameConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res>? get loadedGameConfig {
    if (_self.loadedGameConfig == null) {
      return null;
    }

    return $GameConfigCopyWith<$Res>(_self.loadedGameConfig!, (value) {
      return _then(_self.copyWith(loadedGameConfig: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _GameConfigState extends GameConfigState {
  const _GameConfigState(
      {this.processingStatus = ProcessingStatus.start,
      final List<GameConfig> availableConfigs = const [],
      this.loadedGameConfig = null,
      this.basePath = null})
      : _availableConfigs = availableConfigs,
        super._();
  factory _GameConfigState.fromJson(Map<String, dynamic> json) =>
      _$GameConfigStateFromJson(json);

  @override
  @JsonKey()
  final ProcessingStatus processingStatus;
  final List<GameConfig> _availableConfigs;
  @override
  @JsonKey()
  List<GameConfig> get availableConfigs {
    if (_availableConfigs is EqualUnmodifiableListView)
      return _availableConfigs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableConfigs);
  }

  @override
  @JsonKey()
  final GameConfig? loadedGameConfig;
  @override
  @JsonKey()
  final String? basePath;

  /// Create a copy of GameConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GameConfigStateCopyWith<_GameConfigState> get copyWith =>
      __$GameConfigStateCopyWithImpl<_GameConfigState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GameConfigStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GameConfigState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            const DeepCollectionEquality()
                .equals(other._availableConfigs, _availableConfigs) &&
            (identical(other.loadedGameConfig, loadedGameConfig) ||
                other.loadedGameConfig == loadedGameConfig) &&
            (identical(other.basePath, basePath) ||
                other.basePath == basePath));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      const DeepCollectionEquality().hash(_availableConfigs),
      loadedGameConfig,
      basePath);

  @override
  String toString() {
    return 'GameConfigState(processingStatus: $processingStatus, availableConfigs: $availableConfigs, loadedGameConfig: $loadedGameConfig, basePath: $basePath)';
  }
}

/// @nodoc
abstract mixin class _$GameConfigStateCopyWith<$Res>
    implements $GameConfigStateCopyWith<$Res> {
  factory _$GameConfigStateCopyWith(
          _GameConfigState value, $Res Function(_GameConfigState) _then) =
      __$GameConfigStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      List<GameConfig> availableConfigs,
      GameConfig? loadedGameConfig,
      String? basePath});

  @override
  $GameConfigCopyWith<$Res>? get loadedGameConfig;
}

/// @nodoc
class __$GameConfigStateCopyWithImpl<$Res>
    implements _$GameConfigStateCopyWith<$Res> {
  __$GameConfigStateCopyWithImpl(this._self, this._then);

  final _GameConfigState _self;
  final $Res Function(_GameConfigState) _then;

  /// Create a copy of GameConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? processingStatus = null,
    Object? availableConfigs = null,
    Object? loadedGameConfig = freezed,
    Object? basePath = freezed,
  }) {
    return _then(_GameConfigState(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      availableConfigs: null == availableConfigs
          ? _self._availableConfigs
          : availableConfigs // ignore: cast_nullable_to_non_nullable
              as List<GameConfig>,
      loadedGameConfig: freezed == loadedGameConfig
          ? _self.loadedGameConfig
          : loadedGameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig?,
      basePath: freezed == basePath
          ? _self.basePath
          : basePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of GameConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res>? get loadedGameConfig {
    if (_self.loadedGameConfig == null) {
      return null;
    }

    return $GameConfigCopyWith<$Res>(_self.loadedGameConfig!, (value) {
      return _then(_self.copyWith(loadedGameConfig: value));
    });
  }
}

// dart format on
