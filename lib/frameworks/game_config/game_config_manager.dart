import 'package:dauntless/frameworks/match_save/match_save_manager.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'game_config_event.dart';
import 'game_config_state.dart';

class GameConfigManager extends Bloc<GameConfigEvent, GameConfigState> {
  final GameConfigUseCase _gameConfigUseCase;

  GameConfigManager(this._gameConfigUseCase)
      : super(const GameConfigState()) {
    on<LoadAvailableGameConfigsEvent>(_onLoadAvailableGameConfigsEvent);
    on<LoadGameConfigEvent>(_onLoadGameConfigEvent);
  }

  void _onLoadAvailableGameConfigsEvent(
      LoadAvailableGameConfigsEvent event, Emitter<GameConfigState> emit) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    final gameConfigs = await _gameConfigUseCase.loadLocalGameConfigs();
    emit(state.copyWith(
        availableConfigs: gameConfigs, processingStatus: ProcessingStatus.loaded));
  }

  void _onLoadGameConfigEvent(
      LoadGameConfigEvent event, Emitter<GameConfigState> emit) async {
    emit(state.copyWith(
        processingStatus: ProcessingStatus.loading,
        basePath: event.basePath
    ));
    final gameConfig = await _gameConfigUseCase.loadGameConfig(event.basePath);
    emit(state.copyWith(
        loadedGameConfig: gameConfig, processingStatus: ProcessingStatus.loaded));

    // TODO: figure out handling this flow
    GetIt.I<MatchSaveManager>().add(LoadMostRecentMatchSaveEvent());
  }
}