import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'game_config_state.freezed.dart';

part 'game_config_state.g.dart';

@freezed
abstract class GameConfigState with _$GameConfigState {
  const factory GameConfigState({
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
    @Default([]) List<GameConfig> availableConfigs,
    @Default(null) GameConfig? loadedGameConfig,
    @Default(null) String? basePath,
  }) = _GameConfigState;

  factory GameConfigState.fromJson(Map<String, dynamic> json) =>
      _$GameConfigStateFromJson(json);

  const GameConfigState._();
}
