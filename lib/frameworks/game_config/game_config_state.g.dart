// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_config_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GameConfigState _$GameConfigStateFromJson(Map<String, dynamic> json) =>
    _GameConfigState(
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      availableConfigs: (json['availableConfigs'] as List<dynamic>?)
              ?.map((e) => GameConfig.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      loadedGameConfig: json['loadedGameConfig'] == null
          ? null
          : GameConfig.fromJson(
              json['loadedGameConfig'] as Map<String, dynamic>),
      basePath: json['basePath'] as String? ?? null,
    );

Map<String, dynamic> _$GameConfigStateToJson(_GameConfigState instance) =>
    <String, dynamic>{
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'availableConfigs': instance.availableConfigs,
      'loadedGameConfig': instance.loadedGameConfig,
      'basePath': instance.basePath,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
