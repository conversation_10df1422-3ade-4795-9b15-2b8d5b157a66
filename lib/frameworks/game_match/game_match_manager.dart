import 'package:dauntless/dev/dev_initial_state.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_event.dart';
import 'package:dauntless/use_cases/generic_card_class_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/use_cases/match/match_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:dauntless/use_cases/vehicles_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'game_match_event.dart';
import 'game_match_state.dart';

class GameMatchManager extends Bloc<GameMatchEvent, GameMatchState> {
  final MatchUseCase _matchUseCase;
  final GenericCardClassUseCase _genericCardClassUseCase;
  final PlayersUseCase _playersUseCase;
  final LocationsUseCase _locationsUseCase;
  final VehiclesUseCase _vehiclesUseCase;

  GameMatchManager(
      this._matchUseCase,
      this._playersUseCase,
      this._genericCardClassUseCase,
      this._locationsUseCase,
      this._vehiclesUseCase)
      : super(DevInitialState.initialMatchState) {
    on<LoadMatchStateEvent>(_onLoadMatchStateEvent);
    on<LoadGameCardsEvent>(_onLoadLocationsEvent);
    on<StartSelectActiveActionTargetEvent>(_onSelectActiveActionEvent);
    on<SelectActiveActionTargetEvent>(_onSelectActiveActionTargetEvent);
    on<SelectTargetedActionEvent>(_onSelectTargetedActionEvent);
    on<ClearActiveActionEvent>(_onCancelActiveActionEvent);
    on<CancelPlayEvent>(_onCancelPlayEvent);
    on<SubmitPlayerTurnEvent>(_onSubmitPlayerTurnEvent);
    on<CreateNewGroupForActiveActionEvent>(
        _onCreateNewGroupForActiveActionEvent);
    
    // Match creation and joining events have been moved to MatchSelectionBloc
  }

  void _onLoadMatchStateEvent(
      LoadMatchStateEvent event, Emitter<GameMatchState> emit) {
    emit(event.matchState);
    // TODO: how to handle routing ... bloc-to-bloc communication
    GetIt.I<CommandCenterBloc>().add(TapMatchManagementEvent());
  }

  void _onLoadLocationsEvent(
      LoadGameCardsEvent event, Emitter<GameMatchState> emit) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    final cardClasses = await _genericCardClassUseCase.getGameCardClasses();
    emit(state.copyWith(
        hands: Map.from(state.hands)
          ..addAll({locationsPlayerId: _locationsUseCase.objects}),
        loadedCardClasses: Map.from(state.loadedCardClasses)
          ..addAll({
            ...cardClasses,
            ..._locationsUseCase.locationClasses,
            // locationsPlayerId: _locationsUseCase.locationClasses,
            ..._vehiclesUseCase.vehicleClasses
            // state.userPlayerId: _vehiclesUseCase.vehicleClasses,
          }),
        processingStatus: ProcessingStatus.loaded));
  }

  void _onSelectActiveActionEvent(
      StartSelectActiveActionTargetEvent event, Emitter<GameMatchState> emit) {
    final updatedState = _matchUseCase.handleSelectActiveAction(
        state, event.action, event.cardId);

    emit(updatedState);
  }

  void _onCancelActiveActionEvent(
      ClearActiveActionEvent event, Emitter<GameMatchState> emit) {
    emit(state.copyWith(activeAction: null));
  }

  void _onSelectActiveActionTargetEvent(
      SelectActiveActionTargetEvent event, Emitter<GameMatchState> emit) {
    if (state.activeAction == null) {
      return;
    } else if (state.activeAction!.action.actionAttributes
        .contains(ActionAttribute.immediateEffect)) {
      final updatedState =
          _matchUseCase.handleImmediateAction(state, state.activeAction!);
      emit(updatedState);
      return;
    }

    final updatedHand =
        List<GameCard>.from(state.hands[state.userPlayerId] ?? []);

    final updatedTurnActions =
        List<TargetedAction>.from(state.currentTurnActions);

    updatedTurnActions.removeWhere((targetedAction) =>
        targetedAction.subjectCardId == state.activeAction!.subjectCardId);

    /// ****** get turns for MOVE action // TODO: move to use case; unify logic w/ LocationViewCard
    final actionSubjectCard =
        state.getCardForId(state.activeAction?.subjectCardId).$2!;

    final targetLocationCardClass = state.getCardClassForId(event.cardId);

    final actionBaseLocation = actionSubjectCard.locationId == null
        ? null
        : _locationsUseCase.getBaseLocationCard(
            state.hands, actionSubjectCard.locationId!);
    final distance = actionBaseLocation?.gridCoordinates == null
        ? null
        : targetLocationCardClass
            ?.getGridDistance(actionBaseLocation!.gridCoordinates!);
    final moveSpeed =
        state.activeAction!.reconciledAttributes['move_speed']?.toDouble();

    final remainingTurns = distance == null || moveSpeed == null
        ? 0
        : (distance / moveSpeed).ceil();

    updatedTurnActions.add(state.activeAction!
        .copyWith(objectCardId: event.cardId, remainingTurns: remainingTurns));

    emit(state.copyWith(
        activeAction: null,
        hands: {
          ...state.hands,
          state.userPlayerId: updatedHand,
        },
        currentTurnActions: updatedTurnActions));
  }

  void _onSelectTargetedActionEvent(
      SelectTargetedActionEvent event, Emitter<GameMatchState> emit) {
    // final updatedHand =
    //     List<GameCard>.from(state.hands[state.userPlayerId] ?? []);

    final updatedTurnActions =
        List<TargetedAction>.from(state.currentTurnActions);

    updatedTurnActions.removeWhere((targetedAction) =>
        targetedAction.subjectCardId == event.targetedAction.subjectCardId);

    updatedTurnActions.add(event.targetedAction);

    emit(state.copyWith(hands: {
      ...state.hands,
      // state.userPlayerId: updatedHand,
    }, currentTurnActions: updatedTurnActions));
  }

  void _onCancelPlayEvent(CancelPlayEvent event, Emitter<GameMatchState> emit) {
    final updatedHand =
        List<GameCard>.from(state.hands[state.userPlayerId] ?? []);

    final subjectCard =
        state.getCardForId(event.selectedAction.subjectCardId).$2!;
    updatedHand.add(subjectCard);

    final updatedTurnActions =
        List<TargetedAction>.from(state.currentTurnActions);

    updatedTurnActions.removeWhere(
        (action) => action.subjectCardId == event.selectedAction.subjectCardId);

    emit(state.copyWith(hands: {
      ...state.hands,
      state.userPlayerId: updatedHand,
    }, currentTurnActions: updatedTurnActions));
  }

  void _onSubmitPlayerTurnEvent(
      SubmitPlayerTurnEvent event, Emitter<GameMatchState> emit) async {
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));

    /// submit turn to API
    final updatedState = await _matchUseCase.submitPlayerTurn(state);
    emit(updatedState);

    // if (_matchUseCase.allPlayersSubmittedTurn) {
    //   emit(await _matchUseCase.processTurn(updatedState));
    // }

    // TODO: better handle Manager-Bloc communication; Cmd Ctr blocs should probably have the first 'endTurn'? But then how does it handle finishing?
    // GetIt.I<CommandCenterBloc>().add(TurnStartEvent());
    // emit(state.copyWith(turnCount: state.turnCount + 1));
  }

  void _onCreateNewGroupForActiveActionEvent(
      CreateNewGroupForActiveActionEvent event, Emitter<GameMatchState> emit) {
    if (state.activeAction?.subjectCardId == null) {
      throw Exception('No active action subject card');
    }
    final activeAction = state.activeAction!;
    emit(state.copyWith(
      processingStatus: ProcessingStatus.loading,
      activeAction: null,
    ));

    final subjectCard = state.getCardForId(activeAction.subjectCardId).$2!;
    final updatedState = _matchUseCase.createNewGroup(state, subjectCard);
    emit(updatedState.copyWith(processingStatus: ProcessingStatus.loaded));
  }
  
  // Match creation and joining events have been moved to MatchSelectionBloc
}
