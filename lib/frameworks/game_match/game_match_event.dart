import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/targeted_action.dart';

import 'game_match_state.dart';

abstract class GameMatchEvent {}
class LoadMatchStateEvent extends Game<PERSON>atchEvent {
  final GameMatchState matchState;

  LoadMatchStateEvent(this.matchState);
}
class LoadGameCardsEvent extends GameMatchEvent {}
class SubmitPlayerTurnEvent extends GameMatchEvent {}
class SelectActiveActionTargetEvent extends GameMatchEvent {
  // final ActiveAction action;
  final GameCardId cardId;

  SelectActiveActionTargetEvent({
    // required this.action,
    required this.cardId});
}
class CancelPlayEvent extends GameMatchEvent {
  final TargetedAction selectedAction;

  CancelPlayEvent({required this.selectedAction});
}
class StartSelectActiveActionTargetEvent extends GameMatchEvent {
  final CardAction action;
  final GameCardId cardId;

  StartSelectActiveActionTargetEvent({required this.action, required this.cardId});
}
class SelectTargetedActionEvent extends GameMatchEvent {
  final TargetedAction targetedAction;

  SelectTargetedActionEvent(this.targetedAction);
}
class ClearActiveActionEvent extends GameMatchEvent {}
class CreateNewGroupForActiveActionEvent extends GameMatchEvent {}

// Match creation and joining events have been moved to match_selection_event.dart