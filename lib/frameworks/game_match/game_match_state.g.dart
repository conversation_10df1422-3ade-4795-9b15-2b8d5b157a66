// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_match_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchState _$MatchStateFromJson(Map<String, dynamic> json) => _MatchState(
      matchConfig:
          MatchConfig.fromJson(json['matchConfig'] as Map<String, dynamic>),
      userPlayerId: json['userPlayerId'] as String,
      turnCount: (json['turnCount'] as num).toInt(),
      hands: (json['hands'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => GameCard.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      resources: (json['resources'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k,
            (e as Map<String, dynamic>).map(
              (k, e) => MapEntry(
                  k, ResourceValue.fromJson(e as Map<String, dynamic>)),
            )),
      ),
      loadedCardClasses:
          (json['loadedCardClasses'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry(k, GameCardClass.fromJson(e as Map<String, dynamic>)),
      ),
      activeAction: json['activeAction'] == null
          ? null
          : TargetedAction.fromJson(
              json['activeAction'] as Map<String, dynamic>),
      currentTurnActions: (json['currentTurnActions'] as List<dynamic>?)
              ?.map((e) => TargetedAction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
    );

Map<String, dynamic> _$MatchStateToJson(_MatchState instance) =>
    <String, dynamic>{
      'matchConfig': instance.matchConfig,
      'userPlayerId': instance.userPlayerId,
      'turnCount': instance.turnCount,
      'hands': instance.hands,
      'resources': instance.resources,
      'loadedCardClasses': instance.loadedCardClasses,
      'activeAction': instance.activeAction,
      'currentTurnActions': instance.currentTurnActions,
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
