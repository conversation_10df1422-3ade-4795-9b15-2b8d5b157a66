// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_match_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
GameMatchState _$GameMatchStateFromJson(Map<String, dynamic> json) {
  return _MatchState.fromJson(json);
}

/// @nodoc
mixin _$GameMatchState {
  MatchConfig get matchConfig;
  PlayerId get userPlayerId;
  int get turnCount;

  /// this is really more like 'instantiated cards'?
  Map<PlayerId, List<GameCard>> get hands;

  /// should this name distinguish 'game-wide' resources from location- or other- specific resources?
  Map<PlayerId, Map<ResourceId, ResourceValue>>
      get resources; // TODO: is this how we should do this?
  Map<CardClassId, GameCardClass>
      get loadedCardClasses; // required Map<PlayerId, List<GameCardClass>> loadedCardClasses,
  TargetedAction? get activeAction;
  List<TargetedAction> get currentTurnActions;
  ProcessingStatus get processingStatus;

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GameMatchStateCopyWith<GameMatchState> get copyWith =>
      _$GameMatchStateCopyWithImpl<GameMatchState>(
          this as GameMatchState, _$identity);

  /// Serializes this GameMatchState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GameMatchState &&
            (identical(other.matchConfig, matchConfig) ||
                other.matchConfig == matchConfig) &&
            (identical(other.userPlayerId, userPlayerId) ||
                other.userPlayerId == userPlayerId) &&
            (identical(other.turnCount, turnCount) ||
                other.turnCount == turnCount) &&
            const DeepCollectionEquality().equals(other.hands, hands) &&
            const DeepCollectionEquality().equals(other.resources, resources) &&
            const DeepCollectionEquality()
                .equals(other.loadedCardClasses, loadedCardClasses) &&
            (identical(other.activeAction, activeAction) ||
                other.activeAction == activeAction) &&
            const DeepCollectionEquality()
                .equals(other.currentTurnActions, currentTurnActions) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      matchConfig,
      userPlayerId,
      turnCount,
      const DeepCollectionEquality().hash(hands),
      const DeepCollectionEquality().hash(resources),
      const DeepCollectionEquality().hash(loadedCardClasses),
      activeAction,
      const DeepCollectionEquality().hash(currentTurnActions),
      processingStatus);

  @override
  String toString() {
    return 'GameMatchState(matchConfig: $matchConfig, userPlayerId: $userPlayerId, turnCount: $turnCount, hands: $hands, resources: $resources, loadedCardClasses: $loadedCardClasses, activeAction: $activeAction, currentTurnActions: $currentTurnActions, processingStatus: $processingStatus)';
  }
}

/// @nodoc
abstract mixin class $GameMatchStateCopyWith<$Res> {
  factory $GameMatchStateCopyWith(
          GameMatchState value, $Res Function(GameMatchState) _then) =
      _$GameMatchStateCopyWithImpl;
  @useResult
  $Res call(
      {MatchConfig matchConfig,
      PlayerId userPlayerId,
      int turnCount,
      Map<PlayerId, List<GameCard>> hands,
      Map<PlayerId, Map<ResourceId, ResourceValue>> resources,
      Map<CardClassId, GameCardClass> loadedCardClasses,
      TargetedAction? activeAction,
      List<TargetedAction> currentTurnActions,
      ProcessingStatus processingStatus});

  $MatchConfigCopyWith<$Res> get matchConfig;
  $TargetedActionCopyWith<$Res>? get activeAction;
}

/// @nodoc
class _$GameMatchStateCopyWithImpl<$Res>
    implements $GameMatchStateCopyWith<$Res> {
  _$GameMatchStateCopyWithImpl(this._self, this._then);

  final GameMatchState _self;
  final $Res Function(GameMatchState) _then;

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchConfig = null,
    Object? userPlayerId = null,
    Object? turnCount = null,
    Object? hands = null,
    Object? resources = null,
    Object? loadedCardClasses = null,
    Object? activeAction = freezed,
    Object? currentTurnActions = null,
    Object? processingStatus = null,
  }) {
    return _then(_self.copyWith(
      matchConfig: null == matchConfig
          ? _self.matchConfig
          : matchConfig // ignore: cast_nullable_to_non_nullable
              as MatchConfig,
      userPlayerId: null == userPlayerId
          ? _self.userPlayerId
          : userPlayerId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
      turnCount: null == turnCount
          ? _self.turnCount
          : turnCount // ignore: cast_nullable_to_non_nullable
              as int,
      hands: null == hands
          ? _self.hands
          : hands // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, List<GameCard>>,
      resources: null == resources
          ? _self.resources
          : resources // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, Map<ResourceId, ResourceValue>>,
      loadedCardClasses: null == loadedCardClasses
          ? _self.loadedCardClasses
          : loadedCardClasses // ignore: cast_nullable_to_non_nullable
              as Map<CardClassId, GameCardClass>,
      activeAction: freezed == activeAction
          ? _self.activeAction
          : activeAction // ignore: cast_nullable_to_non_nullable
              as TargetedAction?,
      currentTurnActions: null == currentTurnActions
          ? _self.currentTurnActions
          : currentTurnActions // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
    ));
  }

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MatchConfigCopyWith<$Res> get matchConfig {
    return $MatchConfigCopyWith<$Res>(_self.matchConfig, (value) {
      return _then(_self.copyWith(matchConfig: value));
    });
  }

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TargetedActionCopyWith<$Res>? get activeAction {
    if (_self.activeAction == null) {
      return null;
    }

    return $TargetedActionCopyWith<$Res>(_self.activeAction!, (value) {
      return _then(_self.copyWith(activeAction: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _MatchState extends GameMatchState {
  const _MatchState(
      {required this.matchConfig,
      required this.userPlayerId,
      required this.turnCount,
      required final Map<PlayerId, List<GameCard>> hands,
      required final Map<PlayerId, Map<ResourceId, ResourceValue>> resources,
      required final Map<CardClassId, GameCardClass> loadedCardClasses,
      this.activeAction = null,
      final List<TargetedAction> currentTurnActions = const [],
      this.processingStatus = ProcessingStatus.start})
      : _hands = hands,
        _resources = resources,
        _loadedCardClasses = loadedCardClasses,
        _currentTurnActions = currentTurnActions,
        super._();
  factory _MatchState.fromJson(Map<String, dynamic> json) =>
      _$MatchStateFromJson(json);

  @override
  final MatchConfig matchConfig;
  @override
  final PlayerId userPlayerId;
  @override
  final int turnCount;

  /// this is really more like 'instantiated cards'?
  final Map<PlayerId, List<GameCard>> _hands;

  /// this is really more like 'instantiated cards'?
  @override
  Map<PlayerId, List<GameCard>> get hands {
    if (_hands is EqualUnmodifiableMapView) return _hands;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_hands);
  }

  /// should this name distinguish 'game-wide' resources from location- or other- specific resources?
  final Map<PlayerId, Map<ResourceId, ResourceValue>> _resources;

  /// should this name distinguish 'game-wide' resources from location- or other- specific resources?
  @override
  Map<PlayerId, Map<ResourceId, ResourceValue>> get resources {
    if (_resources is EqualUnmodifiableMapView) return _resources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_resources);
  }

// TODO: is this how we should do this?
  final Map<CardClassId, GameCardClass> _loadedCardClasses;
// TODO: is this how we should do this?
  @override
  Map<CardClassId, GameCardClass> get loadedCardClasses {
    if (_loadedCardClasses is EqualUnmodifiableMapView)
      return _loadedCardClasses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_loadedCardClasses);
  }

// required Map<PlayerId, List<GameCardClass>> loadedCardClasses,
  @override
  @JsonKey()
  final TargetedAction? activeAction;
  final List<TargetedAction> _currentTurnActions;
  @override
  @JsonKey()
  List<TargetedAction> get currentTurnActions {
    if (_currentTurnActions is EqualUnmodifiableListView)
      return _currentTurnActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentTurnActions);
  }

  @override
  @JsonKey()
  final ProcessingStatus processingStatus;

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchStateCopyWith<_MatchState> get copyWith =>
      __$MatchStateCopyWithImpl<_MatchState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchState &&
            (identical(other.matchConfig, matchConfig) ||
                other.matchConfig == matchConfig) &&
            (identical(other.userPlayerId, userPlayerId) ||
                other.userPlayerId == userPlayerId) &&
            (identical(other.turnCount, turnCount) ||
                other.turnCount == turnCount) &&
            const DeepCollectionEquality().equals(other._hands, _hands) &&
            const DeepCollectionEquality()
                .equals(other._resources, _resources) &&
            const DeepCollectionEquality()
                .equals(other._loadedCardClasses, _loadedCardClasses) &&
            (identical(other.activeAction, activeAction) ||
                other.activeAction == activeAction) &&
            const DeepCollectionEquality()
                .equals(other._currentTurnActions, _currentTurnActions) &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      matchConfig,
      userPlayerId,
      turnCount,
      const DeepCollectionEquality().hash(_hands),
      const DeepCollectionEquality().hash(_resources),
      const DeepCollectionEquality().hash(_loadedCardClasses),
      activeAction,
      const DeepCollectionEquality().hash(_currentTurnActions),
      processingStatus);

  @override
  String toString() {
    return 'GameMatchState(matchConfig: $matchConfig, userPlayerId: $userPlayerId, turnCount: $turnCount, hands: $hands, resources: $resources, loadedCardClasses: $loadedCardClasses, activeAction: $activeAction, currentTurnActions: $currentTurnActions, processingStatus: $processingStatus)';
  }
}

/// @nodoc
abstract mixin class _$MatchStateCopyWith<$Res>
    implements $GameMatchStateCopyWith<$Res> {
  factory _$MatchStateCopyWith(
          _MatchState value, $Res Function(_MatchState) _then) =
      __$MatchStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {MatchConfig matchConfig,
      PlayerId userPlayerId,
      int turnCount,
      Map<PlayerId, List<GameCard>> hands,
      Map<PlayerId, Map<ResourceId, ResourceValue>> resources,
      Map<CardClassId, GameCardClass> loadedCardClasses,
      TargetedAction? activeAction,
      List<TargetedAction> currentTurnActions,
      ProcessingStatus processingStatus});

  @override
  $MatchConfigCopyWith<$Res> get matchConfig;
  @override
  $TargetedActionCopyWith<$Res>? get activeAction;
}

/// @nodoc
class __$MatchStateCopyWithImpl<$Res> implements _$MatchStateCopyWith<$Res> {
  __$MatchStateCopyWithImpl(this._self, this._then);

  final _MatchState _self;
  final $Res Function(_MatchState) _then;

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchConfig = null,
    Object? userPlayerId = null,
    Object? turnCount = null,
    Object? hands = null,
    Object? resources = null,
    Object? loadedCardClasses = null,
    Object? activeAction = freezed,
    Object? currentTurnActions = null,
    Object? processingStatus = null,
  }) {
    return _then(_MatchState(
      matchConfig: null == matchConfig
          ? _self.matchConfig
          : matchConfig // ignore: cast_nullable_to_non_nullable
              as MatchConfig,
      userPlayerId: null == userPlayerId
          ? _self.userPlayerId
          : userPlayerId // ignore: cast_nullable_to_non_nullable
              as PlayerId,
      turnCount: null == turnCount
          ? _self.turnCount
          : turnCount // ignore: cast_nullable_to_non_nullable
              as int,
      hands: null == hands
          ? _self._hands
          : hands // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, List<GameCard>>,
      resources: null == resources
          ? _self._resources
          : resources // ignore: cast_nullable_to_non_nullable
              as Map<PlayerId, Map<ResourceId, ResourceValue>>,
      loadedCardClasses: null == loadedCardClasses
          ? _self._loadedCardClasses
          : loadedCardClasses // ignore: cast_nullable_to_non_nullable
              as Map<CardClassId, GameCardClass>,
      activeAction: freezed == activeAction
          ? _self.activeAction
          : activeAction // ignore: cast_nullable_to_non_nullable
              as TargetedAction?,
      currentTurnActions: null == currentTurnActions
          ? _self._currentTurnActions
          : currentTurnActions // ignore: cast_nullable_to_non_nullable
              as List<TargetedAction>,
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
    ));
  }

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MatchConfigCopyWith<$Res> get matchConfig {
    return $MatchConfigCopyWith<$Res>(_self.matchConfig, (value) {
      return _then(_self.copyWith(matchConfig: value));
    });
  }

  /// Create a copy of GameMatchState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TargetedActionCopyWith<$Res>? get activeAction {
    if (_self.activeAction == null) {
      return null;
    }

    return $TargetedActionCopyWith<$Res>(_self.activeAction!, (value) {
      return _then(_self.copyWith(activeAction: value));
    });
  }
}

// dart format on
