import 'package:collection/collection.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/models/base/resource_value.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/util/extensions.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/use_cases/players_use_case.dart';

part 'game_match_state.freezed.dart';
part 'game_match_state.g.dart';

const PlayerId locationsPlayerId = 'locationsPlayer';

@freezed
abstract class GameMatchState with _$GameMatchState {
  const factory GameMatchState({
    required MatchConfig matchConfig,
    required PlayerId userPlayerId,
    required int turnCount,
    /// this is really more like 'instantiated cards'?
    required Map<PlayerId, List<GameCard>> hands,

    /// should this name distinguish 'game-wide' resources from location- or other- specific resources?
    required Map<PlayerId, Map<ResourceId, ResourceValue>> resources,

    // TODO: is this how we should do this?
    required Map<CardClassId, GameCardClass> loadedCardClasses,
    // required Map<PlayerId, List<GameCardClass>> loadedCardClasses,
    @Default(null) TargetedAction? activeAction,
    @Default([]) List<TargetedAction> currentTurnActions,
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
  }) = _MatchState;

  /// Helper Getters
  /// prepend 0000 to the id for file sorting purposes
      String get turnCountString => turnCount.toString().padLeft(4, '0');

  GameCardClass? getClassForCard(GameCardId cardId) {
    final cardClass = loadedCardClasses[cardId];
    if (false && cardClass == null) {
      print('************************************************************************************');
      print('No card class found for cardId: $cardId');
      print('************************************************************************************');
    }
    return cardClass;
  }

  Map<PlayerId, List<GameCard>> getCardsWithActiveActions() =>
      hands.map((playerId, cards) => MapEntry(playerId,
          cards.where((card) => card.activeActions.isNotEmpty).toList()));

  List<GameCardClass> get locationCards => loadedCardClasses.values
      .where((card) => card.type == CardType.location)
      .toList();

  Map<PlayerId, List<GameCard>> getCardsForLocation(LocationId locationId,
      {bool getGroupChildren = false}) {
    final children = hands.map((playerId, cards) => MapEntry(playerId,
        cards.where((card) => card.locationId == locationId).toList()));
    children.removeWhere((key, value) => value.isEmpty);

    if (!getGroupChildren) return children;
    final Map<PlayerId, List<GameCard>> childrenWithGroupings =
        {}; // Map.from(children);
    for (final playerId in children.keys) {
      final subChildren = children[playerId]!;
      for (final child in subChildren) {
        if (child.type == CardType.grouping) {
          final childrenInGrouping = getCardsForLocation(child.id);

          for (final playerId in childrenInGrouping.keys) {
            if (childrenWithGroupings[playerId] == null) {
              childrenWithGroupings[playerId] = [];
            }
            childrenWithGroupings[playerId]!
                .addAll(childrenInGrouping[playerId]!);
            // children.addAll(childrenInGrouping);
          }
        }
      }
    }
    for (final playerId in children.keys) {
      if (childrenWithGroupings[playerId] == null) {
        childrenWithGroupings[playerId] = [];
      }
      childrenWithGroupings[playerId]!.addAll(children[playerId]!);
    }
    return childrenWithGroupings;
  }

  Map<PlayerId, List<(GameCard, GameCardClass?)>> getCardsAndClassesForLocation(
      LocationId locationId,
      {bool getGroupChildren = false}) {
    final gameCards =
        getCardsForLocation(locationId, getGroupChildren: getGroupChildren);
    final cardsAndClasses = gameCards.map((playerId, cards) => MapEntry(
        playerId,
        cards
            .map((card) => (card, getClassForCard(card.classId)))
            // .whereType<(GameCard, GameCardClass)>()
            .toList()));
    return cardsAndClasses;
  }

  // TODO: support different reconciliation forms: min, max, add to list, etc.
  Map<String, dynamic> getReconciledAttributes(
    LocationId cardId, {
    /// defaults to currentUserId
    PlayerId? playerId,
  }) {
    final card = getCardForId(cardId).$2!;
    final cardClass = getClassForCard(card.classId);

    if (card.type == CardType.grouping) {
      return getCardsAndClassesForLocation(cardId)[playerId ?? userPlayerId]
              ?.getReconciledAttributes() ??
          {};
    } else {
      return cardClass?.attributes ?? {};
    }
  }

  Set<TargetedAction> getSelectedActionsForCard(GameCard card) =>
      currentTurnActions
          .where((action) => action.subjectCardId == card.id)
          .toSet();

  Set<GameCard> get currentPlayerUnplayedCards => hands[userPlayerId]!.toSet();

  // .where((card) =>
  //     !currentTurnActions.any((action) => action.subjectCard.id == card.id))
  // .toList();

  Set<GameCard> get currentPlayerUnplayedGroupings => currentPlayerUnplayedCards
      .where((card) => card.type == CardType.grouping)
      .toSet();

  bool cardIsActiveActionTarget(GameCardId? cardId) {
    final card = getCardForId(cardId).$2;
    List<CardType> targets = activeAction?.action.targets ?? [];
    List<ActionTargetFilter> targetFilters =
        activeAction?.action.targetFilters ?? [];
    if (!targets.contains(card?.type)) {
      return false;
    }
    final subjectCard = activeAction == null
        ? null
        : getCardForId(activeAction!.subjectCardId).$2;
    if (targetFilters.contains(ActionTargetFilter.currentLocation) &&
        card?.classId != subjectCard?.locationId) {
      return false;
    }
    if (targetFilters.contains(ActionTargetFilter.notCurrentLocation) &&
        card?.classId == subjectCard?.locationId) {
      return false;
    }
    if (targetFilters.contains(ActionTargetFilter.sameType) &&
        card?.type != subjectCard?.type) {
      return false;
    }
    if (targetFilters.contains(ActionTargetFilter.notCurrentGrouping) &&
        card?.locationId == subjectCard?.id) {
      return false;
    }
    return true;
  }

  (PlayerId? playerId, GameCard? card) getCardForId(GameCardId? cardId) {
    if (cardId == null) {
      return (null, null);
    }
    for (final playerId in hands.keys) {
      final card = hands[playerId]!.firstWhereOrNull(
        (card) => card.id == cardId,
      );
      if (card != null) {
        return (playerId, card);
      }
    }
    return (null, null);
  }

  GameCardClass? getCardClassForId(CardClassId? classId) {
    if (classId == null) {
      return null;
    }
    return loadedCardClasses[classId];
  }

  GameCardClass? getCardClassForCardId(GameCardId? cardId) {
    final card = getCardForId(cardId).$2;
    return getCardClassForId(card?.classId);
  }

  List<GameCard> get currentPlayerHand => hands[userPlayerId] ?? [];

  /// Helper Constructors
  const GameMatchState._();

  factory GameMatchState.fromJson(Map<String, dynamic> json) =>
      _$GameMatchStateFromJson(json);
}
