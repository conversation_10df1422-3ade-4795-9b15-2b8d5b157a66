import 'package:dauntless/ui/liberator/screens/command_center/command_center_screen.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:loggy/loggy.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'di/di.dart';
import 'ui/blocs/theme/theme_bloc.dart';
import 'ui/blocs/theme/theme_state.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await configureDependencies();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget with UiLoggy {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) => ResponsiveSizer(
        builder: (context, orientation, screenType) => BlocProvider<ThemeBloc>(
            create: (_) => GetIt.I<ThemeBloc>()..add(LoadThemeEvent()),
            child: Bloc<PERSON>uilder<ThemeBloc, ThemeState>(
              builder: (context, state) =>
                  state.processingStatus != ProcessingStatus.loaded
                      ? const CircularProgressIndicator()
                      : MaterialApp(
                          title: 'Dauntless',
                          theme: state.theme,
                          home: CommandCenterScreen(),
                        ),
            )),
      );
}
