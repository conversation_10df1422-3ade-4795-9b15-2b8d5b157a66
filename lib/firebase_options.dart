// // File generated by FlutterFire CLI.
// // ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
// import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
// import 'package:flutter/foundation.dart'
//     show defaultTargetPlatform, kIsWeb, TargetPlatform;
//
// /// Default [FirebaseOptions] for use with your Firebase apps.
// ///
// /// Example:
// /// ```dart
// /// import 'firebase_options.dart';
// /// // ...
// /// await Firebase.initializeApp(
// ///   options: DefaultFirebaseOptions.currentPlatform,
// /// );
// /// ```
// class DefaultFirebaseOptions {
//   static FirebaseOptions get currentPlatform {
//     if (kIsWeb) {
//       return web;
//     }
//     switch (defaultTargetPlatform) {
//       case TargetPlatform.android:
//         return android;
//       case TargetPlatform.iOS:
//         return ios;
//       case TargetPlatform.macOS:
//         return macos;
//       case TargetPlatform.windows:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions have not been configured for windows - '
//           'you can reconfigure this by running the FlutterFire CLI again.',
//         );
//       case TargetPlatform.linux:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions have not been configured for linux - '
//           'you can reconfigure this by running the FlutterFire CLI again.',
//         );
//       default:
//         throw UnsupportedError(
//           'DefaultFirebaseOptions are not supported for this platform.',
//         );
//     }
//   }
//
//   static const FirebaseOptions web = FirebaseOptions(
//     apiKey: 'AIzaSyDNXcUT0DzHDoyFxuHf2yep0BTx-DHJmo0',
//     appId: '1:1079986991100:web:e65726da81bf4ae2824c79',
//     messagingSenderId: '1079986991100',
//     projectId: 'dauntless-55bb9',
//     authDomain: 'dauntless-55bb9.firebaseapp.com',
//     storageBucket: 'dauntless-55bb9.appspot.com',
//     measurementId: 'G-7Q7264F4F2',
//   );
//
//   static const FirebaseOptions android = FirebaseOptions(
//     apiKey: 'AIzaSyDMUdymTwfSdmm4m3w8o9oPHksheonTgOg',
//     appId: '1:1079986991100:android:ca78f8f16a540a60824c79',
//     messagingSenderId: '1079986991100',
//     projectId: 'dauntless-55bb9',
//     storageBucket: 'dauntless-55bb9.appspot.com',
//   );
//
//   static const FirebaseOptions ios = FirebaseOptions(
//     apiKey: 'AIzaSyDgaXjItwrGV0gXXEFpmh9wROZ0B8EbZcg',
//     appId: '1:1079986991100:ios:ce3cbac93b1dc107824c79',
//     messagingSenderId: '1079986991100',
//     projectId: 'dauntless-55bb9',
//     storageBucket: 'dauntless-55bb9.appspot.com',
//     iosBundleId: 'com.example.dauntless',
//   );
//
//   static const FirebaseOptions macos = FirebaseOptions(
//     apiKey: 'AIzaSyDgaXjItwrGV0gXXEFpmh9wROZ0B8EbZcg',
//     appId: '1:1079986991100:ios:7c8eaffb2420926b824c79',
//     messagingSenderId: '1079986991100',
//     projectId: 'dauntless-55bb9',
//     storageBucket: 'dauntless-55bb9.appspot.com',
//     iosBundleId: 'com.example.dauntless.RunnerTests',
//   );
// }
