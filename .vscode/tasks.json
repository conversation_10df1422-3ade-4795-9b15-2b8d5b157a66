{"version": "2.0.0", "tasks": [{"label": "Build Runner - Build", "type": "shell", "command": "flutter packages pub run --enable-experiment=class-modifiers build_runner build --delete-conflicting-outputs", "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "Build Runner - Watch", "type": "shell", "command": "flutter packages pub run --enable-experiment=class-modifiers build_runner watch --delete-conflicting-outputs", "group": "build", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}]}