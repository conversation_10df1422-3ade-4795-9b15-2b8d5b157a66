{"name": "flutter_docker", "context": "..", "dockerFile": "../Dockerfile", "remoteUser": "developer", "mounts": ["source=/dev/bus/usb,target=/dev/bus/usb,type=bind"], "settings": {"terminal.integrated.shell.linux": null}, "runArgs": ["--privileged"], "extensions": ["dart-code.flutter"], "workspaceMount": "source=${localWorkspaceFolder}/workspace,target=/home/<USER>/workspace,type=bind,consistency=delegated", "workspaceFolder": "/home/<USER>/workspace"}