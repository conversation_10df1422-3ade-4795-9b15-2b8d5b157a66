import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Basic Widget Tests', () {
    testWidgets('Simple widget should render', (WidgetTester tester) async {
      // Build a simple test widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Text('Hello, Test!'),
          ),
        ),
      );

      // Verify that the text appears
      expect(find.text('Hello, Test!'), findsOneWidget);
      expect(find.byType(MaterialApp), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('Button interaction should work', (WidgetTester tester) async {
      int counter = 0;

      // Build a widget with a button
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    Text('Counter: $counter'),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          counter++;
                        });
                      },
                      child: const Text('Increment'),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Verify initial state
      expect(find.text('Counter: 0'), findsOneWidget);
      expect(find.text('Increment'), findsOneWidget);

      // Tap the button
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Verify the counter incremented
      expect(find.text('Counter: 1'), findsOneWidget);
      expect(find.text('Counter: 0'), findsNothing);
    });

    testWidgets('Text field should accept input', (WidgetTester tester) async {
      final controller = TextEditingController();

      // Build a widget with a text field
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextField(
              controller: controller,
              decoration: const InputDecoration(
                hintText: 'Enter text',
              ),
            ),
          ),
        ),
      );

      // Find the text field
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      // Enter text
      await tester.enterText(textField, 'Hello, Flutter!');
      await tester.pump();

      // Verify the text was entered
      expect(controller.text, equals('Hello, Flutter!'));
      expect(find.text('Hello, Flutter!'), findsOneWidget);
    });
  });
}
