import 'package:bloc_test/bloc_test.dart';
import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_bloc.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_event.dart';
import 'package:dauntless/ui/blocs/list_object/list_object_state.dart';
import 'package:dauntless/ui/views/list_object_view.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockListObjectUseCase extends Mock implements ListObjectUseCase<GameCard> {}
class MockGroupingsUseCase extends Mock implements GroupingsUseCase {}

void main() {
  group('ListObjectBloc', () {
    late ListObjectBloc<GameCard> listObjectBloc;
    late MockListObjectUseCase mockListObjectUseCase;
    late MockGroupingsUseCase mockGroupingsUseCase;
    late List<ListObjectHook<GameCard, ListObjectEvent>> hooks;

    setUp(() {
      mockListObjectUseCase = MockListObjectUseCase();
      mockGroupingsUseCase = MockGroupingsUseCase();
      hooks = [];

      // Set up default mock behaviors
      when(() => mockListObjectUseCase.init()).thenAnswer((_) async => {});
      when(() => mockListObjectUseCase.objects).thenReturn([]);
      when(() => mockGroupingsUseCase.init()).thenAnswer((_) async => {});
      when(() => mockGroupingsUseCase.objects).thenReturn([]);
      when(() => mockGroupingsUseCase.allGroupingIds).thenReturn({});
      when(() => mockGroupingsUseCase.getIdsByGroupingIds(any())).thenReturn({});
      when(() => mockGroupingsUseCase.getGroupingsByIds(any())).thenReturn([]);

      // Register fallback values
      registerFallbackValue(createTestGameCard());
      registerFallbackValue(<String>[]);
    });

    tearDown(() {
      listObjectBloc.close();
    });

    group('Constructor', () {
      test('should initialize with correct default state', () {
        // Act
        listObjectBloc = ListObjectBloc<GameCard>(mockListObjectUseCase, hooks);

        // Assert
        expect(listObjectBloc.state.list, isEmpty);
        expect(listObjectBloc.state.listCardId, isNull);
        expect(listObjectBloc.state.selectedObject, isNull);
        expect(listObjectBloc.state.viewType, equals(ListObjectViewType.list));
        expect(listObjectBloc.state.buildType, equals(ListObjectViewBuildOption.children));
        expect(listObjectBloc.state.groupings, isEmpty);
        expect(listObjectBloc.state.hoverObject, isNull);
      });

      test('should initialize with starting list and card ID', () {
        // Arrange
        final startingList = [
          createTestGameCard(id: 'card1', name: 'Card 1'),
          createTestGameCard(id: 'card2', name: 'Card 2'),
        ];
        const listCardId = 'list-card-123';

        // Act
        listObjectBloc = ListObjectBloc<GameCard>(
          mockListObjectUseCase,
          hooks,
          startingListObject: startingList,
          listCardId: listCardId,
        );

        // Assert
        expect(listObjectBloc.state.list, equals(startingList));
        expect(listObjectBloc.state.listCardId, equals(listCardId));
      });

      test('should initialize with groupings use case', () {
        // Act
        listObjectBloc = ListObjectBloc<GameCard>(
          mockListObjectUseCase,
          hooks,
          groupingsUseCase: mockGroupingsUseCase,
        );

        // Assert - Should not throw and should be properly initialized
        expect(listObjectBloc.state, isA<ListObjectState<GameCard>>());
      });
    });

    group('InitListObjectEvent', () {
      setUp(() {
        listObjectBloc = ListObjectBloc<GameCard>(
          mockListObjectUseCase,
          hooks,
          groupingsUseCase: mockGroupingsUseCase,
        );
      });

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should initialize use cases and update state with objects',
        setUp: () {
          final mockObjects = [
            createTestGameCard(id: 'obj1', name: 'Object 1'),
            createTestGameCard(id: 'obj2', name: 'Object 2'),
          ];
          when(() => mockListObjectUseCase.objects).thenReturn(mockObjects);
        },
        build: () => listObjectBloc,
        act: (bloc) => bloc.add(InitListObjectEvent<GameCard>(null)),
        verify: (_) {
          verify(() => mockListObjectUseCase.init()).called(1);
          verify(() => mockGroupingsUseCase.init()).called(1);
        },
      );

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should handle build type parameter',
        setUp: () {
          when(() => mockListObjectUseCase.objects).thenReturn([]);
        },
        build: () => listObjectBloc,
        act: (bloc) => bloc.add(InitListObjectEvent<GameCard>(
          null,
          buildType: ListObjectViewBuildOption.groupings,
        )),
        verify: (_) {
          verify(() => mockListObjectUseCase.init()).called(1);
          verify(() => mockGroupingsUseCase.init()).called(1);
        },
      );

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should handle groupings build type',
        setUp: () {
          final mockGroupingIds = {'group1', 'group2'};
          when(() => mockListObjectUseCase.objects).thenReturn([]);
          when(() => mockGroupingsUseCase.allGroupingIds).thenReturn(mockGroupingIds);
        },
        build: () => listObjectBloc,
        act: (bloc) => bloc.add(InitListObjectEvent<GameCard>(
          null,
          buildType: ListObjectViewBuildOption.groupings,
        )),
        verify: (_) {
          verify(() => mockGroupingsUseCase.allGroupingIds).called(1);
        },
      );

      test('should handle initialization without groupings use case', () async {
        // Arrange
        final blocWithoutGroupings = ListObjectBloc<GameCard>(
          mockListObjectUseCase,
          hooks,
        );
        final mockObjects = [createTestGameCard(id: 'obj1', name: 'Object 1')];
        when(() => mockListObjectUseCase.objects).thenReturn(mockObjects);

        // Act
        blocWithoutGroupings.add(InitListObjectEvent<GameCard>(null));
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockListObjectUseCase.init()).called(1);
        verifyNever(() => mockGroupingsUseCase.init());

        blocWithoutGroupings.close();
      });
    });

    group('SetListViewTypeEvent', () {
      setUp(() {
        listObjectBloc = ListObjectBloc<GameCard>(mockListObjectUseCase, hooks);
      });

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should update view type to grid',
        build: () => listObjectBloc,
        act: (bloc) => bloc.add(SetListViewTypeEvent<GameCard>(ListObjectViewType.grid)),
        expect: () => [
          listObjectBloc.state.copyWith(viewType: ListObjectViewType.grid),
        ],
      );

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should update view type to list',
        build: () => listObjectBloc,
        act: (bloc) => bloc.add(SetListViewTypeEvent<GameCard>(ListObjectViewType.list)),
        expect: () => [
          listObjectBloc.state.copyWith(viewType: ListObjectViewType.list),
        ],
      );

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should handle multiple view type changes',
        build: () => listObjectBloc,
        act: (bloc) {
          bloc.add(SetListViewTypeEvent<GameCard>(ListObjectViewType.grid));
          bloc.add(SetListViewTypeEvent<GameCard>(ListObjectViewType.list));
        },
        expect: () => [
          listObjectBloc.state.copyWith(viewType: ListObjectViewType.grid),
          listObjectBloc.state.copyWith(viewType: ListObjectViewType.list),
        ],
      );
    });

    group('SelectListObjectEvent', () {
      setUp(() {
        listObjectBloc = ListObjectBloc<GameCard>(
          mockListObjectUseCase,
          hooks,
          groupingsUseCase: mockGroupingsUseCase,
        );
      });

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should select object and call use case',
        setUp: () {
          when(() => mockGroupingsUseCase.getGroupingsByIds(any())).thenReturn([]);
        },
        build: () => listObjectBloc,
        act: (bloc) {
          final testCard = createTestGameCard(id: 'test-card', name: 'Test Card');
          bloc.add(SelectListObjectEvent<GameCard>(testCard));
        },
        verify: (_) {
          verify(() => mockListObjectUseCase.selectObject(any())).called(1);
          verify(() => mockGroupingsUseCase.getGroupingsByIds(any())).called(1);
        },
      );

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should deselect object when selecting same object',
        setUp: () {
          when(() => mockGroupingsUseCase.getGroupingsByIds(any())).thenReturn([]);
        },
        build: () => listObjectBloc,
        seed: () => listObjectBloc.state.copyWith(
          selectedObject: createTestGameCard(id: 'test-card', name: 'Test Card'),
        ),
        act: (bloc) {
          final testCard = createTestGameCard(id: 'test-card', name: 'Test Card');
          bloc.add(SelectListObjectEvent<GameCard>(testCard));
        },
        verify: (_) {
          // Should not call selectObject when deselecting
          verifyNever(() => mockListObjectUseCase.selectObject(any()));
          verify(() => mockGroupingsUseCase.getGroupingsByIds([''])).called(1);
        },
      );

      test('should execute hooks when selecting object', () async {
        // Arrange
        bool hookCalled = false;
        final testHook = ListObjectHook<GameCard, SelectListObjectEvent<GameCard>>(
          (event, state) {
            hookCalled = true;
          },
        );
        final hooksWithCallback = [testHook];
        
        final blocWithHooks = ListObjectBloc<GameCard>(
          mockListObjectUseCase,
          hooksWithCallback,
          groupingsUseCase: mockGroupingsUseCase,
        );

        when(() => mockGroupingsUseCase.getGroupingsByIds(any())).thenReturn([]);

        // Act
        final testCard = createTestGameCard(id: 'test-card', name: 'Test Card');
        blocWithHooks.add(SelectListObjectEvent<GameCard>(testCard));
        await Future.delayed(const Duration(milliseconds: 50));

        // Assert
        expect(hookCalled, isTrue);

        blocWithHooks.close();
      });
    });

    group('HoverListObjectEvent', () {
      setUp(() {
        listObjectBloc = ListObjectBloc<GameCard>(mockListObjectUseCase, hooks);
      });

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should set hover object',
        build: () => listObjectBloc,
        act: (bloc) {
          final testCard = createTestGameCard(id: 'hover-card', name: 'Hover Card');
          bloc.add(HoverListObjectEvent<GameCard>(testCard));
        },
        expect: () => [
          listObjectBloc.state.copyWith(
            hoverObject: createTestGameCard(id: 'hover-card', name: 'Hover Card'),
          ),
        ],
      );

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should clear hover object when null',
        build: () => listObjectBloc,
        seed: () => listObjectBloc.state.copyWith(
          hoverObject: createTestGameCard(id: 'hover-card', name: 'Hover Card'),
        ),
        act: (bloc) => bloc.add(HoverListObjectEvent<GameCard>(null)),
        expect: () => [
          listObjectBloc.state.copyWith(hoverObject: null),
        ],
      );

      blocTest<ListObjectBloc<GameCard>, ListObjectState<GameCard>>(
        'should handle rapid hover changes',
        build: () => listObjectBloc,
        act: (bloc) {
          final card1 = createTestGameCard(id: 'card1', name: 'Card 1');
          final card2 = createTestGameCard(id: 'card2', name: 'Card 2');
          bloc.add(HoverListObjectEvent<GameCard>(card1));
          bloc.add(HoverListObjectEvent<GameCard>(card2));
          bloc.add(HoverListObjectEvent<GameCard>(null));
        },
        expect: () => [
          listObjectBloc.state.copyWith(hoverObject: createTestGameCard(id: 'card1', name: 'Card 1')),
          listObjectBloc.state.copyWith(hoverObject: createTestGameCard(id: 'card2', name: 'Card 2')),
          listObjectBloc.state.copyWith(hoverObject: null),
        ],
      );
    });

    group('Complex Scenarios', () {
      setUp(() {
        listObjectBloc = ListObjectBloc<GameCard>(
          mockListObjectUseCase,
          hooks,
          groupingsUseCase: mockGroupingsUseCase,
        );
      });

      test('should handle complete workflow', () async {
        // Arrange
        final mockObjects = [
          createTestGameCard(id: 'obj1', name: 'Object 1'),
          createTestGameCard(id: 'obj2', name: 'Object 2'),
        ];
        final mockGroupings = [
          createTestCardGrouping(id: 'group1', groupingId: 'grouping1'),
        ];

        when(() => mockListObjectUseCase.objects).thenReturn(mockObjects);
        when(() => mockGroupingsUseCase.objects).thenReturn(mockGroupings);
        when(() => mockGroupingsUseCase.getGroupingsByIds(any())).thenReturn(mockGroupings);

        // Act - Initialize
        listObjectBloc.add(InitListObjectEvent<GameCard>(null));
        await Future.delayed(const Duration(milliseconds: 50));

        // Act - Change view type
        listObjectBloc.add(SetListViewTypeEvent<GameCard>(ListObjectViewType.grid));
        await Future.delayed(const Duration(milliseconds: 10));

        // Act - Select object
        listObjectBloc.add(SelectListObjectEvent<GameCard>(mockObjects.first));
        await Future.delayed(const Duration(milliseconds: 10));

        // Act - Hover object
        listObjectBloc.add(HoverListObjectEvent<GameCard>(mockObjects.last));
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockListObjectUseCase.init()).called(1);
        verify(() => mockGroupingsUseCase.init()).called(1);
        verify(() => mockListObjectUseCase.selectObject(mockObjects.first)).called(1);
      });

      test('should handle successful initialization workflow', () async {
        // Arrange
        final mockObjects = [createTestGameCard(id: 'obj1', name: 'Object 1')];
        when(() => mockListObjectUseCase.objects).thenReturn(mockObjects);

        // Act
        listObjectBloc.add(InitListObjectEvent<GameCard>(null));
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockListObjectUseCase.init()).called(1);
        verify(() => mockGroupingsUseCase.init()).called(1);
      });
    });

    group('Edge Cases', () {
      test('should handle empty hooks list', () {
        // Act
        final bloc = ListObjectBloc<GameCard>(mockListObjectUseCase, []);

        // Assert
        expect(bloc.state, isA<ListObjectState<GameCard>>());
        bloc.close();
      });

      test('should handle null groupings use case', () {
        // Act
        final bloc = ListObjectBloc<GameCard>(mockListObjectUseCase, hooks);

        // Assert
        expect(bloc.state, isA<ListObjectState<GameCard>>());
        bloc.close();
      });

      test('should handle large object lists', () async {
        // Arrange
        final largeObjectList = List.generate(1000, (index) =>
          createTestGameCard(id: 'obj$index', name: 'Object $index')
        );
        when(() => mockListObjectUseCase.objects).thenReturn(largeObjectList);

        final bloc = ListObjectBloc<GameCard>(mockListObjectUseCase, hooks);

        // Act
        bloc.add(InitListObjectEvent<GameCard>(null));
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert - Should handle large lists without issues
        verify(() => mockListObjectUseCase.init()).called(1);
        bloc.close();
      });
    });
  });
}

/// Test helper to create GameCard instances
GameCard createTestGameCard({
  String? id,
  String? name,
  CardType? type,
}) {
  return GameCard(
    id: id ?? 'test-card-id',
    name: name ?? 'Test Card',
    type: type ?? CardType.vehicle,
    classId: 'test-class-id',
  );
}

/// Test helper to create CardGrouping instances
CardGrouping createTestCardGrouping({
  String? id,
  String? groupingId,
  String? name,
}) {
  return CardGrouping(
    id: id ?? 'test-grouping-id',
    groupingId: groupingId ?? 'test-grouping-group-id',
    name_: name ?? 'Test Grouping',
  );
}
