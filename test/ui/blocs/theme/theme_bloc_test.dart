import 'package:bloc_test/bloc_test.dart';
import 'package:dauntless/ui/blocs/theme/theme_bloc.dart';
import 'package:dauntless/ui/blocs/theme/theme_sizings.dart';
import 'package:dauntless/ui/blocs/theme/theme_state.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/theme_use_case.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockThemeUseCase extends Mock implements ThemeUseCase {}

void main() {
  group('ThemeBloc', () {
    late ThemeBloc themeBloc;
    late MockThemeUseCase mockThemeUseCase;

    setUp(() {
      mockThemeUseCase = MockThemeUseCase();
      themeBloc = ThemeBloc(mockThemeUseCase);
    });

    tearDown(() {
      themeBloc.close();
    });

    test('initial state should have correct default values', () {
      expect(themeBloc.state.theme, isNull);
      expect(themeBloc.state.sizings, equals(const ThemeSizings()));
      expect(themeBloc.state.processingStatus, equals(ProcessingStatus.start));
    });

    group('LoadThemeEvent', () {
      blocTest<ThemeBloc, ThemeState>(
        'should emit loading then loaded state when theme loads successfully',
        setUp: () {
          final testTheme = ThemeData.light();
          when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
          when(() => mockThemeUseCase.theme).thenReturn(testTheme);
        },
        build: () => themeBloc,
        act: (bloc) => bloc.add(LoadThemeEvent()),
        expect: () => [
          const ThemeState(
            processingStatus: ProcessingStatus.loading,
            sizings: ThemeSizings(),
          ),
          ThemeState(
            theme: ThemeData.light(),
            processingStatus: ProcessingStatus.loaded,
            sizings: const ThemeSizings(),
          ),
        ],
        verify: (_) {
          verify(() => mockThemeUseCase.init()).called(1);
          verify(() => mockThemeUseCase.theme).called(1);
        },
      );

      blocTest<ThemeBloc, ThemeState>(
        'should emit loading then loaded state with null theme when use case returns null',
        setUp: () {
          when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
          when(() => mockThemeUseCase.theme).thenReturn(null);
        },
        build: () => themeBloc,
        act: (bloc) => bloc.add(LoadThemeEvent()),
        expect: () => [
          const ThemeState(
            processingStatus: ProcessingStatus.loading,
            sizings: ThemeSizings(),
          ),
          const ThemeState(
            theme: null,
            processingStatus: ProcessingStatus.loaded,
            sizings: ThemeSizings(),
          ),
        ],
        verify: (_) {
          verify(() => mockThemeUseCase.init()).called(1);
          verify(() => mockThemeUseCase.theme).called(1);
        },
      );

      test('should handle successful theme loading workflow', () async {
        // Arrange
        final testTheme = ThemeData.light();
        when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
        when(() => mockThemeUseCase.theme).thenReturn(testTheme);

        // Act
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert - Should complete successfully
        verify(() => mockThemeUseCase.init()).called(1);
        verify(() => mockThemeUseCase.theme).called(1);
      });

      test('should handle multiple LoadThemeEvent calls', () async {
        // Arrange
        final testTheme = ThemeData.dark();
        when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
        when(() => mockThemeUseCase.theme).thenReturn(testTheme);

        // Act
        themeBloc.add(LoadThemeEvent());
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockThemeUseCase.init()).called(2);
        verify(() => mockThemeUseCase.theme).called(2);
      });

      test('should work with complex theme data', () async {
        // Arrange
        final complexTheme = ThemeData(
          primaryColor: Colors.purple,
          brightness: Brightness.dark,
          fontFamily: 'Roboto',
          textTheme: const TextTheme(
            headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
            bodyLarge: TextStyle(fontSize: 16),
          ),
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
          ),
        );
        when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
        when(() => mockThemeUseCase.theme).thenReturn(complexTheme);

        // Act
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockThemeUseCase.init()).called(1);
        verify(() => mockThemeUseCase.theme).called(1);
      });

      test('should handle slow theme loading', () async {
        // Arrange
        final testTheme = ThemeData.light();
        when(() => mockThemeUseCase.init()).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
        });
        when(() => mockThemeUseCase.theme).thenReturn(testTheme);

        // Act
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 200));

        // Assert
        verify(() => mockThemeUseCase.init()).called(1);
        verify(() => mockThemeUseCase.theme).called(1);
      });
    });

    group('ThemeState', () {
      test('should maintain sizings across state changes', () {
        const customSizings = ThemeSizings(
          iconSize: 32,
          primaryCardWidth: 600,
          padding: 12,
        );
        
        final state = ThemeState(
          theme: ThemeData.light(),
          sizings: customSizings,
          processingStatus: ProcessingStatus.loaded,
        );

        expect(state.sizings.iconSize, equals(32));
        expect(state.sizings.primaryCardWidth, equals(600));
        expect(state.sizings.padding, equals(12));
      });

      test('should use default sizings when not specified', () {
        const state = ThemeState();
        
        expect(state.sizings, equals(const ThemeSizings()));
        expect(state.sizings.iconSize, equals(24)); // Default value
        expect(state.sizings.primaryCardWidth, equals(496)); // Default value
        expect(state.sizings.padding, equals(8)); // Default value
      });

      test('should support copyWith for state updates', () {
        const initialState = ThemeState();
        final newTheme = ThemeData.dark();
        
        final updatedState = initialState.copyWith(
          theme: newTheme,
          processingStatus: ProcessingStatus.loaded,
        );

        expect(updatedState.theme, equals(newTheme));
        expect(updatedState.processingStatus, equals(ProcessingStatus.loaded));
        expect(updatedState.sizings, equals(const ThemeSizings())); // Should remain unchanged
      });
    });

    group('Edge Cases', () {
      test('should handle concurrent LoadThemeEvent calls', () async {
        // Arrange
        final testTheme = ThemeData.light();
        when(() => mockThemeUseCase.init()).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
        });
        when(() => mockThemeUseCase.theme).thenReturn(testTheme);

        // Act
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 10));
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockThemeUseCase.init()).called(2);
      });

      test('should handle Material 3 themes', () async {
        // Arrange
        final material3Theme = ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        );
        when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
        when(() => mockThemeUseCase.theme).thenReturn(material3Theme);

        // Act
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockThemeUseCase.init()).called(1);
        verify(() => mockThemeUseCase.theme).called(1);
      });

      test('should handle theme use case returning different themes over time', () async {
        final lightTheme = ThemeData.light();
        final darkTheme = ThemeData.dark();

        when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
        when(() => mockThemeUseCase.theme).thenReturn(lightTheme);

        // First load
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 10));

        // Change theme in use case
        when(() => mockThemeUseCase.theme).thenReturn(darkTheme);

        // Second load
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 10));

        verify(() => mockThemeUseCase.init()).called(2);
        verify(() => mockThemeUseCase.theme).called(2);
      });
    });

    group('Integration Scenarios', () {
      test('should work in typical app initialization flow', () async {
        // Arrange
        final appTheme = ThemeData(
          primaryColor: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        );
        when(() => mockThemeUseCase.init()).thenAnswer((_) async => {});
        when(() => mockThemeUseCase.theme).thenReturn(appTheme);

        // Act
        themeBloc.add(LoadThemeEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockThemeUseCase.init()).called(1);
        verify(() => mockThemeUseCase.theme).called(1);
      });
    });
  });
}
