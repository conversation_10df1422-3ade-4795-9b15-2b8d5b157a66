import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dauntless/ui/blocs/theme/theme_bloc.dart';
import 'package:get_it/get_it.dart';

import 'test_dependencies.dart';

/// Helper class for widget testing
class WidgetTestHelpers {
  /// Create a test widget with proper BLoC providers and theme
  static Widget createTestWidget({
    required Widget child,
    ThemeBloc? themeBloc,
    ThemeData? theme,
  }) {
    return MaterialApp(
      theme: theme ?? ThemeData.light(),
      home: MultiBlocProvider(
        providers: [
          BlocProvider<ThemeBloc>(
            create: (context) => themeBloc ?? GetIt.instance<ThemeBloc>(),
          ),
        ],
        child: child,
      ),
    );
  }

  /// Create a test widget with minimal setup (no BLoCs)
  static Widget createMinimalTestWidget({
    required Widget child,
    ThemeData? theme,
  }) {
    return MaterialApp(
      theme: theme ?? ThemeData.light(),
      home: Scaffold(body: child),
    );
  }

  /// Pump a widget with proper setup
  static Future<void> pumpTestWidget(
    WidgetTester tester, {
    required Widget child,
    ThemeBloc? themeBloc,
    ThemeData? theme,
  }) async {
    await tester.pumpWidget(
      createTestWidget(
        child: child,
        themeBloc: themeBloc,
        theme: theme,
      ),
    );
  }

  /// Common test setup that initializes dependencies
  static Future<void> setUp() async {
    await TestDependencies.configure();
  }

  /// Common test teardown that cleans up dependencies
  static Future<void> tearDown() async {
    await TestDependencies.reset();
  }

  /// Find a widget by its key
  static Finder findByKey(String key) {
    return find.byKey(Key(key));
  }

  /// Find a widget by its type
  static Finder findByType<T extends Widget>() {
    return find.byType(T);
  }

  /// Find text in the widget tree
  static Finder findText(String text) {
    return find.text(text);
  }

  /// Tap a widget and pump
  static Future<void> tapAndPump(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await tester.pump();
  }

  /// Enter text in a text field and pump
  static Future<void> enterTextAndPump(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// Wait for animations to complete
  static Future<void> pumpAndSettle(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }
}
