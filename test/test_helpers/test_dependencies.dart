import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/repositories/theme_repository.dart';
import 'package:dauntless/ui/blocs/theme/theme_bloc.dart';
import 'package:dauntless/use_cases/theme_use_case.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

/// Mock implementations for testing
class MockServerRepository extends Mock implements ServerRepository {}
class MockThemeRepository extends Mock implements ThemeRepository {}
class MockThemeUseCase extends Mock implements ThemeUseCase {}

/// Test dependency injection setup
class TestDependencies {
  static GetIt? _getIt;

  /// Configure test dependencies
  static Future<GetIt> configure() async {
    // Reset GetIt if it was previously configured
    if (_getIt != null) {
      await _getIt!.reset();
    }

    _getIt = GetIt.instance;

    // Register mock repositories
    _getIt!.registerLazySingleton<ServerRepository>(() => MockServerRepository());
    _getIt!.registerLazySingleton<ThemeRepository>(() => MockThemeRepository());
    
    // Register mock use cases
    _getIt!.registerLazySingleton<ThemeUseCase>(() => MockThemeUseCase());
    
    // Register BLoCs
    _getIt!.registerFactory<ThemeBloc>(() => ThemeBloc(
      themeUseCase: _getIt!<ThemeUseCase>(),
    ));

    return _getIt!;
  }

  /// Reset all dependencies
  static Future<void> reset() async {
    if (_getIt != null) {
      await _getIt!.reset();
      _getIt = null;
    }
  }

  /// Get a mock instance
  static T getMock<T extends Object>() {
    return _getIt!<T>();
  }
}
