import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/repositories/vehicles_repository.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/vehicles_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockVehiclesRepository extends Mock implements VehiclesRepository {}

void main() {
  group('VehiclesUseCase', () {
    late VehiclesUseCase vehiclesUseCase;
    late MockVehiclesRepository mockVehiclesRepository;

    setUp(() {
      mockVehiclesRepository = MockVehiclesRepository();
      vehiclesUseCase = VehiclesUseCase(mockVehiclesRepository);
    });

    group('Constructor', () {
      test('should initialize with VehiclesRepository dependency', () {
        expect(vehiclesUseCase, isA<VehiclesUseCase>());
        expect(vehiclesUseCase, isA<ListObjectUseCase<GameCard>>());
      });

      test('should start with initial status', () {
        expect(vehiclesUseCase.status, equals(ProcessingStatus.start));
      });

      test('should start with empty objects list', () {
        expect(vehiclesUseCase.objects, isEmpty);
      });

      test('should start with empty vehicle classes', () {
        expect(vehiclesUseCase.vehicleClasses, isEmpty);
      });
    });

    group('init', () {
      test('should load vehicles successfully', () async {
        // Arrange
        final mockVehicleClasses = [
          createTestGameCardClass(id: 'vehicle1', name: 'X-Wing', type: CardType.vehicle),
          createTestGameCardClass(id: 'vehicle2', name: 'TIE Fighter', type: CardType.vehicle),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => mockVehicleClasses);

        // Act
        await vehiclesUseCase.init();

        // Assert
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loaded));
        expect(vehiclesUseCase.objects, hasLength(2));
        expect(vehiclesUseCase.vehicleClasses, hasLength(2));
        
        // Verify objects are GameCards created from GameCardClasses
        final objects = vehiclesUseCase.objects;
        expect(objects.first.name, equals('X-Wing'));
        expect(objects.last.name, equals('TIE Fighter'));
        
        verify(() => mockVehiclesRepository.get()).called(1);
      });

      test('should handle empty vehicle list', () async {
        // Arrange
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => []);

        // Act
        await vehiclesUseCase.init();

        // Assert
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loaded));
        expect(vehiclesUseCase.objects, isEmpty);
        expect(vehiclesUseCase.vehicleClasses, isEmpty);
        verify(() => mockVehiclesRepository.get()).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        when(() => mockVehiclesRepository.get())
            .thenThrow(Exception('Failed to load vehicles'));

        // Act & Assert
        expect(
          () => vehiclesUseCase.init(),
          throwsException,
        );
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loading));
        verify(() => mockVehiclesRepository.get()).called(1);
      });

      test('should handle multiple init calls gracefully', () async {
        // Arrange
        final mockVehicleClasses = [
          createTestGameCardClass(id: 'vehicle1', name: 'X-Wing', type: CardType.vehicle),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return mockVehicleClasses;
        });

        // Act - Start multiple concurrent init calls
        final futures = [
          vehiclesUseCase.init(),
          vehiclesUseCase.init(),
          vehiclesUseCase.init(),
        ];
        await Future.wait(futures);

        // Assert
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loaded));
        expect(vehiclesUseCase.objects, hasLength(1));
        // Repository should only be called once due to the completer pattern
        verify(() => mockVehiclesRepository.get()).called(1);
      });

      test('should return existing future when already loading', () async {
        // Arrange
        final mockVehicleClasses = [
          createTestGameCardClass(id: 'vehicle1', name: 'X-Wing', type: CardType.vehicle),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return mockVehicleClasses;
        });

        // Act - Start first init call
        final firstFuture = vehiclesUseCase.init();
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loading));
        
        // Start second init call while first is still loading
        final secondFuture = vehiclesUseCase.init();
        
        // Wait for both to complete
        await Future.wait([firstFuture, secondFuture]);

        // Assert
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loaded));
        verify(() => mockVehiclesRepository.get()).called(1);
      });
    });

    group('objects getter', () {
      test('should return GameCards created from GameCardClasses', () async {
        // Arrange
        final mockVehicleClasses = [
          createTestGameCardClass(
            id: 'x-wing-class',
            name: 'X-Wing Fighter',
            type: CardType.vehicle,
          ),
          createTestGameCardClass(
            id: 'tie-fighter-class',
            name: 'TIE Fighter',
            type: CardType.vehicle,
          ),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => mockVehicleClasses);

        // Act
        await vehiclesUseCase.init();
        final objects = vehiclesUseCase.objects;

        // Assert
        expect(objects, hasLength(2));
        expect(objects, everyElement(isA<GameCard>()));
        expect(objects.first.name, equals('X-Wing Fighter'));
        expect(objects.first.type, equals(CardType.vehicle));
        expect(objects.last.name, equals('TIE Fighter'));
        expect(objects.last.type, equals(CardType.vehicle));
      });

      test('should return empty list before initialization', () {
        // Assert
        expect(vehiclesUseCase.objects, isEmpty);
      });
    });

    group('vehicleClasses getter', () {
      test('should return map of vehicle classes by ID', () async {
        // Arrange
        final mockVehicleClasses = [
          createTestGameCardClass(id: 'x-wing', name: 'X-Wing', type: CardType.vehicle),
          createTestGameCardClass(id: 'tie-fighter', name: 'TIE Fighter', type: CardType.vehicle),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => mockVehicleClasses);

        // Act
        await vehiclesUseCase.init();
        final vehicleClasses = vehiclesUseCase.vehicleClasses;

        // Assert
        expect(vehicleClasses, isA<Map<String, GameCardClass>>());
        expect(vehicleClasses, hasLength(2));
        expect(vehicleClasses['x-wing'], isNotNull);
        expect(vehicleClasses['x-wing']!.name, equals('X-Wing'));
        expect(vehicleClasses['tie-fighter'], isNotNull);
        expect(vehicleClasses['tie-fighter']!.name, equals('TIE Fighter'));
      });

      test('should return empty map before initialization', () {
        // Assert
        expect(vehiclesUseCase.vehicleClasses, isEmpty);
      });
    });

    group('status getter', () {
      test('should track processing status correctly', () async {
        // Arrange
        final mockVehicleClasses = [
          createTestGameCardClass(id: 'vehicle1', name: 'Test Vehicle', type: CardType.vehicle),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 10));
          return mockVehicleClasses;
        });

        // Assert initial status
        expect(vehiclesUseCase.status, equals(ProcessingStatus.start));

        // Act - Start loading
        final initFuture = vehiclesUseCase.init();
        
        // Assert loading status
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loading));
        
        // Wait for completion
        await initFuture;
        
        // Assert loaded status
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loaded));
      });
    });

    group('selectObject', () {
      test('should handle object selection without implementation', () {
        // Arrange
        final testCard = createTestGameCard(id: 'test-vehicle', name: 'Test Vehicle');

        // Act & Assert - Should not throw
        expect(() => vehiclesUseCase.selectObject(testCard), returnsNormally);
      });

      test('should handle different object types', () {
        // Arrange
        final testCard = createTestGameCard(id: 'different-type', name: 'Different Vehicle');

        // Act & Assert - Should not throw
        expect(() => vehiclesUseCase.selectObject(testCard), returnsNormally);
      });
    });

    group('Edge Cases', () {
      test('should handle large number of vehicles', () async {
        // Arrange
        final largeVehicleList = List.generate(1000, (index) => 
          createTestGameCardClass(
            id: 'vehicle$index',
            name: 'Vehicle $index',
            type: CardType.vehicle,
          )
        );
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => largeVehicleList);

        // Act
        await vehiclesUseCase.init();

        // Assert
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loaded));
        expect(vehiclesUseCase.objects, hasLength(1000));
        expect(vehiclesUseCase.vehicleClasses, hasLength(1000));
      });

      test('should handle vehicles with special characters in names', () async {
        // Arrange
        final specialVehicles = [
          createTestGameCardClass(
            id: 'special-1',
            name: 'X-Wing™ Fighter (Rebel)',
            type: CardType.vehicle,
          ),
          createTestGameCardClass(
            id: 'special-2',
            name: 'TIE/ln Fighter [Imperial] 🚀',
            type: CardType.vehicle,
          ),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => specialVehicles);

        // Act
        await vehiclesUseCase.init();

        // Assert
        expect(vehiclesUseCase.objects, hasLength(2));
        expect(vehiclesUseCase.objects.first.name, contains('X-Wing™'));
        expect(vehiclesUseCase.objects.last.name, contains('🚀'));
      });

      test('should handle duplicate vehicle IDs gracefully', () async {
        // Arrange
        final duplicateVehicles = [
          createTestGameCardClass(id: 'duplicate', name: 'Vehicle 1', type: CardType.vehicle),
          createTestGameCardClass(id: 'duplicate', name: 'Vehicle 2', type: CardType.vehicle),
        ];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => duplicateVehicles);

        // Act
        await vehiclesUseCase.init();

        // Assert
        expect(vehiclesUseCase.objects, hasLength(2));
        expect(vehiclesUseCase.vehicleClasses, hasLength(1)); // Map will overwrite duplicate keys
        expect(vehiclesUseCase.vehicleClasses['duplicate']!.name, equals('Vehicle 2')); // Last one wins
      });
    });

    group('Integration Scenarios', () {
      test('should work in typical vehicle loading workflow', () async {
        // Arrange - Simulate loading rebel and imperial vehicles
        final rebelVehicles = [
          createTestGameCardClass(id: 'x-wing', name: 'X-Wing', type: CardType.vehicle),
          createTestGameCardClass(id: 'y-wing', name: 'Y-Wing', type: CardType.vehicle),
        ];
        final imperialVehicles = [
          createTestGameCardClass(id: 'tie-fighter', name: 'TIE Fighter', type: CardType.vehicle),
          createTestGameCardClass(id: 'tie-bomber', name: 'TIE Bomber', type: CardType.vehicle),
        ];
        final allVehicles = [...rebelVehicles, ...imperialVehicles];
        
        when(() => mockVehiclesRepository.get())
            .thenAnswer((_) async => allVehicles);

        // Act
        await vehiclesUseCase.init();

        // Assert - Verify we can access vehicles by faction
        final vehicleNames = vehiclesUseCase.objects.map((v) => v.name).toList();
        expect(vehicleNames, contains('X-Wing'));
        expect(vehicleNames, contains('Y-Wing'));
        expect(vehicleNames, contains('TIE Fighter'));
        expect(vehicleNames, contains('TIE Bomber'));
        
        // Verify we can look up specific vehicles
        expect(vehiclesUseCase.vehicleClasses['x-wing'], isNotNull);
        expect(vehiclesUseCase.vehicleClasses['tie-fighter'], isNotNull);
      });

      test('should handle re-initialization after error', () async {
        // Arrange - First call fails
        when(() => mockVehiclesRepository.get())
            .thenThrow(Exception('Network error'));

        // Act - First init fails
        expect(() => vehiclesUseCase.init(), throwsException);
        expect(vehiclesUseCase.status, equals(ProcessingStatus.loading));

        // Arrange - Second call succeeds (for new instance)
        final newMockRepository = MockVehiclesRepository();
        when(() => newMockRepository.get())
            .thenAnswer((_) async => [
              createTestGameCardClass(id: 'vehicle1', name: 'Test Vehicle', type: CardType.vehicle),
            ]);

        // Create new instance for retry (since status is stuck in loading)
        final retryUseCase = VehiclesUseCase(newMockRepository);
        await retryUseCase.init();

        // Assert - Second attempt succeeds
        expect(retryUseCase.status, equals(ProcessingStatus.loaded));
        expect(retryUseCase.objects, hasLength(1));
      });
    });
  });
}

/// Test helper to create GameCardClass instances
GameCardClass createTestGameCardClass({
  String? id,
  String? name,
  CardType? type,
}) {
  return GameCardClass(
    id: id ?? 'test-class-id',
    name: name ?? 'Test Card Class',
    type: type ?? CardType.vehicle,
  );
}

/// Test helper to create GameCard instances
GameCard createTestGameCard({
  String? id,
  String? name,
  CardType? type,
}) {
  return GameCard(
    id: id ?? 'test-card-id',
    name: name ?? 'Test Card',
    type: type ?? CardType.vehicle,
    classId: 'test-class-id',
  );
}
