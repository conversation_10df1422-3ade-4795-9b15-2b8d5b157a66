import 'dart:async';

import 'package:common/models/player.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/api/dtos/submit_turn_dto.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/repositories/match_repository.dart';
import 'package:dauntless/repositories/players_repository.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match/network_match_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

// Mock classes
class MockServerRepository extends Mock implements ServerRepository {}
class MockRemoteLogger extends Mock implements RemoteLogger {}
class MockPlayersRepository extends Mock implements PlayersRepository {}
class MockMatchRepository extends Mock implements MatchRepository {}

void main() {
  group('NetworkMatchUseCase', () {
    late NetworkMatchUseCase networkMatchUseCase;
    late MockServerRepository mockServerRepository;
    late MockRemoteLogger mockLogger;
    late MockPlayersRepository mockPlayersRepository;
    late MockMatchRepository mockMatchRepository;
    late StreamController<Map<String, dynamic>> serverMessagesController;

    setUp(() {
      mockServerRepository = MockServerRepository();
      mockLogger = MockRemoteLogger();
      mockPlayersRepository = MockPlayersRepository();
      mockMatchRepository = MockMatchRepository();
      serverMessagesController = StreamController<Map<String, dynamic>>.broadcast();

      // Setup server messages stream
      when(() => mockServerRepository.serverMessages)
          .thenAnswer((_) => serverMessagesController.stream);

      // Register fallback values for mocktail
      registerFallbackValue(const Player(id: 'test-player'));
      registerFallbackValue(<TargetedAction>[]);
      registerFallbackValue(SubmitTurnDto(
        gameMatchId: 'test-match',
        playerId: 'test-player',
        move: [],
        turnNumber: 1,
      ));

      // Setup default mock behaviors
      when(() => mockLogger.info(any())).thenReturn(null);
      when(() => mockLogger.error(any())).thenReturn(null);
      when(() => mockLogger.warn(any())).thenReturn(null);
      when(() => mockPlayersRepository.playablePlayers).thenReturn([
        const Player(id: 'player1', name: 'Player 1', type: PlayerType.humanLocal),
        const Player(id: 'player2', name: 'Player 2', type: PlayerType.humanLocal),
      ]);
      when(() => mockMatchRepository.submittedActiveActions).thenReturn({});
      when(() => mockMatchRepository.submitPlayerTurnActions(any(), any())).thenReturn(null);
      when(() => mockMatchRepository.submitDevOtherPlayerTurnActions(any(), any())).thenReturn(null);

      networkMatchUseCase = NetworkMatchUseCase(
        mockLogger,
        mockPlayersRepository,
        mockMatchRepository,
        mockServerRepository,
      );
    });

    tearDown(() {
      serverMessagesController.close();
      networkMatchUseCase.dispose();
    });

    group('Constructor and Initialization', () {
      test('should initialize with all dependencies', () {
        expect(networkMatchUseCase, isNotNull);
        verify(() => mockServerRepository.serverMessages).called(1);
      });

      test('should setup server message listener on construction', () {
        // Verify that the constructor sets up the listener
        verify(() => mockServerRepository.serverMessages).called(1);
      });
    });

    group('submitPlayerTurn', () {
      late GameMatchState testState;

      setUp(() {
        testState = GameMatchState(
          userPlayerId: 'player1',
          matchConfig: const MatchConfig(
            gameId: 'test-match-123',
            selectedGameMode: GameMode.hotSeat,
            hostId: 'test-host',
            matchId: 'test-match-123',
          ),
          hands: const {},
          resources: const {},
          loadedCardClasses: const {},
          currentTurnActions: [
            TargetedAction.genId(
              action: const CardAction(
                id: 'move-action',
                name: 'Move',
                type: ActionType.move,
              ),
              subjectCardId: 'card1',
            ),
          ],
          turnCount: 1,
        );
      });

      test('should submit turn to server successfully', () async {
        // Arrange
        when(() => mockServerRepository.submitTurnActions(any()))
            .thenAnswer((_) async => true);

        // Act
        final result = await networkMatchUseCase.submitPlayerTurn(testState);

        // Assert
        verify(() => mockServerRepository.submitTurnActions(any())).called(1);
        verify(() => mockLogger.info(any())).called(greaterThan(0));
        expect(result.userPlayerId, equals('player2')); // Should switch to next player
      });

      test('should fallback to local repository when server submission fails', () async {
        // Arrange
        when(() => mockServerRepository.submitTurnActions(any()))
            .thenAnswer((_) async => false);

        // Act
        final result = await networkMatchUseCase.submitPlayerTurn(testState);

        // Assert
        verify(() => mockServerRepository.submitTurnActions(any())).called(1);
        verify(() => mockMatchRepository.submitPlayerTurnActions('player1', any())).called(1);
        verify(() => mockLogger.error(any())).called(1);
        expect(result.userPlayerId, equals('player2'));
      });

      test('should create correct SubmitTurnDto', () async {
        // Arrange
        SubmitTurnDto? capturedDto;
        when(() => mockServerRepository.submitTurnActions(any()))
            .thenAnswer((invocation) async {
          capturedDto = invocation.positionalArguments[0] as SubmitTurnDto;
          return true;
        });

        // Act
        await networkMatchUseCase.submitPlayerTurn(testState);

        // Assert
        expect(capturedDto, isNotNull);
        expect(capturedDto!.playerId, equals('player1'));
        expect(capturedDto!.gameMatchId, equals('test-match-123'));
        expect(capturedDto!.turnNumber, equals(1));
        expect(capturedDto!.move.length, equals(1));
      });

      test('should switch to next player after submission', () async {
        // Arrange
        when(() => mockServerRepository.submitTurnActions(any()))
            .thenAnswer((_) async => true);

        // Act
        final result = await networkMatchUseCase.submitPlayerTurn(testState);

        // Assert
        expect(result.userPlayerId, equals('player2'));
        expect(result.userPlayerId, isNot(equals(testState.userPlayerId)));
      });
    });

    group('Server Message Handling', () {
      test('should handle other_player_turn_submission message with server response', () async {
        // Arrange
        final message = {
          'type': 'other_player_turn_submission',
          'matchId': 'test-match-123',
          'from_server': true,
          'other_player_turn': true,
          'other_player': 'player2',
        };

        // Act
        serverMessagesController.add(message);
        await Future.delayed(const Duration(milliseconds: 10)); // Allow message processing

        // Assert
        verify(() => mockLogger.info('Received server message of type: other_player_turn_submission')).called(1);
        verify(() => mockLogger.info('Processing server response with other player turn submission')).called(1);
        verify(() => mockLogger.info('Server indicated other player ID: player2')).called(1);
        verify(() => mockMatchRepository.submitDevOtherPlayerTurnActions('player2', any())).called(1);
      });

      test('should handle other_player_turn_submission message with simulated flag', () async {
        // Arrange
        when(() => mockMatchRepository.submittedActiveActions).thenReturn({'player1': []});
        final message = {
          'type': 'other_player_turn_submission',
          'matchId': 'test-match-123',
          'simulated': true,
        };

        // Act
        serverMessagesController.add(message);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockLogger.info('Simulating turn submission from other players (client-side)')).called(1);
        verify(() => mockMatchRepository.submitDevOtherPlayerTurnActions('player2', any())).called(1);
      });

      test('should handle other_player_turn_submission message with game_match data', () async {
        // Arrange
        when(() => mockMatchRepository.submittedActiveActions).thenReturn({'player1': []});
        final message = {
          'type': 'other_player_turn_submission',
          'matchId': 'test-match-123',
          'game_match': {'id': 'test-match-123'},
        };

        // Act
        serverMessagesController.add(message);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockLogger.info('Processing game_match data from server')).called(1);
        verify(() => mockMatchRepository.submitDevOtherPlayerTurnActions('player2', any())).called(1);
      });

      test('should handle server response missing other player ID', () async {
        // Arrange
        when(() => mockMatchRepository.submittedActiveActions).thenReturn({'player1': []});
        final message = {
          'type': 'other_player_turn_submission',
          'matchId': 'test-match-123',
          'from_server': true,
          'other_player_turn': true,
          // Missing 'other_player' field
        };

        // Act
        serverMessagesController.add(message);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockLogger.warn('Server response missing other player ID')).called(1);
        verify(() => mockMatchRepository.submitDevOtherPlayerTurnActions('player2', any())).called(1);
      });

      test('should handle unknown message types', () async {
        // Arrange
        final message = {
          'type': 'unknown_message_type',
          'data': 'some data',
        };

        // Act
        serverMessagesController.add(message);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockLogger.info('Received server message of type: unknown_message_type')).called(1);
        verify(() => mockLogger.info('Unhandled message type: unknown_message_type')).called(1);
      });

      test('should handle stream errors gracefully', () async {
        // Arrange
        const errorMessage = 'Stream error occurred';

        // Act
        serverMessagesController.addError(errorMessage);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockLogger.error('Error in server message stream: $errorMessage')).called(1);
      });
    });

    group('Player Simulation Logic', () {
      test('should simulate other player turn when no submitted actions exist', () async {
        // Arrange
        when(() => mockMatchRepository.submittedActiveActions).thenReturn({});
        final message = {
          'type': 'other_player_turn_submission',
          'matchId': 'test-match-123',
          'simulated': true,
        };

        // Act
        serverMessagesController.add(message);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockMatchRepository.submitDevOtherPlayerTurnActions('player2', any())).called(1);
        verify(() => mockLogger.info('Simulated turn submission processed for player: player2')).called(1);
      });

      test('should simulate other player turn when submitted actions exist', () async {
        // Arrange
        when(() => mockMatchRepository.submittedActiveActions).thenReturn({'player1': []});
        final message = {
          'type': 'other_player_turn_submission',
          'matchId': 'test-match-123',
          'simulated': true,
        };

        // Act
        serverMessagesController.add(message);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        verify(() => mockMatchRepository.submitDevOtherPlayerTurnActions('player2', any())).called(1);
        verify(() => mockLogger.info('Simulated turn submission processed for player: player2')).called(1);
      });
    });

    group('Resource Management', () {
      test('should dispose of stream subscription when dispose is called', () {
        // Act
        networkMatchUseCase.dispose();

        // Assert - No exception should be thrown
        expect(() => networkMatchUseCase.dispose(), returnsNormally);
      });

      test('should handle multiple dispose calls gracefully', () {
        // Act & Assert
        expect(() {
          networkMatchUseCase.dispose();
          networkMatchUseCase.dispose();
        }, returnsNormally);
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete turn submission workflow', () async {
        // Arrange
        final testState = GameMatchState(
          userPlayerId: 'player1',
          matchConfig: const MatchConfig(
            gameId: 'test-match-123',
            selectedGameMode: GameMode.hotSeat,
            hostId: 'test-host',
            matchId: 'test-match-123',
          ),
          hands: const {},
          resources: const {},
          loadedCardClasses: const {},
          currentTurnActions: [
            TargetedAction.genId(
              action: const CardAction(
                id: 'move-action',
                name: 'Move',
                type: ActionType.move,
              ),
              subjectCardId: 'card1',
            ),
          ],
          turnCount: 1,
        );

        when(() => mockServerRepository.submitTurnActions(any()))
            .thenAnswer((_) async => true);

        // Act - Submit turn
        final result = await networkMatchUseCase.submitPlayerTurn(testState);

        // Simulate server response
        final serverMessage = {
          'type': 'other_player_turn_submission',
          'matchId': 'test-match-123',
          'from_server': true,
          'other_player_turn': true,
          'other_player': 'player2',
        };
        serverMessagesController.add(serverMessage);
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        expect(result.userPlayerId, equals('player2'));
        verify(() => mockServerRepository.submitTurnActions(any())).called(1);
        verify(() => mockMatchRepository.submitDevOtherPlayerTurnActions('player2', any())).called(1);
      });
    });
  });
}
