import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/repositories/actions_repository.dart';
import 'package:dauntless/use_cases/actions_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockActionsRepository extends Mock implements ActionsRepository {}

void main() {
  group('ActionsUseCase', () {
    late ActionsUseCase actionsUseCase;
    late MockActionsRepository mockActionsRepository;

    setUp(() {
      mockActionsRepository = MockActionsRepository();
      actionsUseCase = ActionsUseCase(mockActionsRepository);
    });

    group('Constructor', () {
      test('should initialize with ActionsRepository dependency', () {
        expect(actionsUseCase, isA<ActionsUseCase>());
        expect(actionsUseCase, isA<ListObjectUseCase<CardAction>>());
      });

      test('should start with initial status', () {
        expect(actionsUseCase.status, equals(ProcessingStatus.start));
      });

      test('should start with empty objects list', () {
        expect(actionsUseCase.objects, isEmpty);
      });
    });

    group('init', () {
      test('should load actions successfully', () async {
        // Arrange
        final mockActions = [
          createTestCardAction(id: 'move', name: 'Move', type: ActionType.move),
          createTestCardAction(id: 'construct', name: 'Construct', type: ActionType.construct),
          createTestCardAction(id: 'changeGroup', name: 'Change Group', type: ActionType.changeGroup),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => mockActions);

        // Act
        await actionsUseCase.init();

        // Assert
        expect(actionsUseCase.status, equals(ProcessingStatus.loaded));
        expect(actionsUseCase.objects, hasLength(3));
        
        // Verify actions are loaded correctly
        final objects = actionsUseCase.objects;
        expect(objects.first.name, equals('Move'));
        expect(objects.first.type, equals(ActionType.move));
        expect(objects[1].name, equals('Construct'));
        expect(objects[1].type, equals(ActionType.construct));
        expect(objects.last.name, equals('Change Group'));
        expect(objects.last.type, equals(ActionType.changeGroup));
        
        verify(() => mockActionsRepository.get()).called(1);
      });

      test('should handle empty actions list', () async {
        // Arrange
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => []);

        // Act
        await actionsUseCase.init();

        // Assert
        expect(actionsUseCase.status, equals(ProcessingStatus.loaded));
        expect(actionsUseCase.objects, isEmpty);
        verify(() => mockActionsRepository.get()).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        when(() => mockActionsRepository.get())
            .thenThrow(Exception('Failed to load actions'));

        // Act & Assert
        expect(
          () => actionsUseCase.init(),
          throwsException,
        );
        expect(actionsUseCase.status, equals(ProcessingStatus.loading));
        verify(() => mockActionsRepository.get()).called(1);
      });

      test('should handle multiple init calls gracefully', () async {
        // Arrange
        final mockActions = [
          createTestCardAction(id: 'move', name: 'Move', type: ActionType.move),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return mockActions;
        });

        // Act - Start multiple concurrent init calls
        final futures = [
          actionsUseCase.init(),
          actionsUseCase.init(),
          actionsUseCase.init(),
        ];
        await Future.wait(futures);

        // Assert
        expect(actionsUseCase.status, equals(ProcessingStatus.loaded));
        expect(actionsUseCase.objects, hasLength(1));
        // Repository should only be called once due to the completer pattern
        verify(() => mockActionsRepository.get()).called(1);
      });

      test('should return existing future when already loading', () async {
        // Arrange
        final mockActions = [
          createTestCardAction(id: 'move', name: 'Move', type: ActionType.move),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return mockActions;
        });

        // Act - Start first init call
        final firstFuture = actionsUseCase.init();
        expect(actionsUseCase.status, equals(ProcessingStatus.loading));
        
        // Start second init call while first is still loading
        final secondFuture = actionsUseCase.init();
        
        // Wait for both to complete
        await Future.wait([firstFuture, secondFuture]);

        // Assert
        expect(actionsUseCase.status, equals(ProcessingStatus.loaded));
        verify(() => mockActionsRepository.get()).called(1);
      });
    });

    group('objects getter', () {
      test('should return loaded CardActions', () async {
        // Arrange
        final mockActions = [
          createTestCardAction(
            id: 'move-action',
            name: 'Move Action',
            type: ActionType.move,
          ),
          createTestCardAction(
            id: 'construct-action',
            name: 'Construct Action',
            type: ActionType.construct,
          ),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => mockActions);

        // Act
        await actionsUseCase.init();
        final objects = actionsUseCase.objects;

        // Assert
        expect(objects, hasLength(2));
        expect(objects, everyElement(isA<CardAction>()));
        expect(objects.first.name, equals('Move Action'));
        expect(objects.first.type, equals(ActionType.move));
        expect(objects.last.name, equals('Construct Action'));
        expect(objects.last.type, equals(ActionType.construct));
      });

      test('should return empty list before initialization', () {
        // Assert
        expect(actionsUseCase.objects, isEmpty);
      });
    });

    group('status getter', () {
      test('should track processing status correctly', () async {
        // Arrange
        final mockActions = [
          createTestCardAction(id: 'action1', name: 'Test Action', type: ActionType.move),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 10));
          return mockActions;
        });

        // Assert initial status
        expect(actionsUseCase.status, equals(ProcessingStatus.start));

        // Act - Start loading
        final initFuture = actionsUseCase.init();
        
        // Assert loading status
        expect(actionsUseCase.status, equals(ProcessingStatus.loading));
        
        // Wait for completion
        await initFuture;
        
        // Assert loaded status
        expect(actionsUseCase.status, equals(ProcessingStatus.loaded));
      });
    });

    group('selectObject', () {
      test('should handle object selection without implementation', () {
        // Arrange
        final testAction = createTestCardAction(id: 'test-action', name: 'Test Action');

        // Act & Assert - Should not throw
        expect(() => actionsUseCase.selectObject(testAction), returnsNormally);
      });

      test('should handle different action types', () {
        // Arrange
        final moveAction = createTestCardAction(id: 'move', name: 'Move', type: ActionType.move);
        final constructAction = createTestCardAction(id: 'construct', name: 'Construct', type: ActionType.construct);

        // Act & Assert - Should not throw
        expect(() => actionsUseCase.selectObject(moveAction), returnsNormally);
        expect(() => actionsUseCase.selectObject(constructAction), returnsNormally);
      });
    });

    group('Edge Cases', () {
      test('should handle large number of actions', () async {
        // Arrange
        final largeActionsList = List.generate(500, (index) => 
          createTestCardAction(
            id: 'action$index',
            name: 'Action $index',
            type: ActionType.values[index % ActionType.values.length],
          )
        );
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => largeActionsList);

        // Act
        await actionsUseCase.init();

        // Assert
        expect(actionsUseCase.status, equals(ProcessingStatus.loaded));
        expect(actionsUseCase.objects, hasLength(500));
      });

      test('should handle actions with special characters in names', () async {
        // Arrange
        final specialActions = [
          createTestCardAction(
            id: 'special-1',
            name: 'Move & Attack™',
            type: ActionType.move,
          ),
          createTestCardAction(
            id: 'special-2',
            name: 'Change Group (Advanced) 🔄',
            type: ActionType.changeGroup,
          ),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => specialActions);

        // Act
        await actionsUseCase.init();

        // Assert
        expect(actionsUseCase.objects, hasLength(2));
        expect(actionsUseCase.objects.first.name, contains('Move & Attack™'));
        expect(actionsUseCase.objects.last.name, contains('🔄'));
      });

      test('should handle actions with complex attributes', () async {
        // Arrange
        final complexActions = [
          CardAction(
            id: 'complex-action',
            name: 'Complex Action',
            type: ActionType.move,
            actionAttributes: [ActionAttribute.immediateEffect],
          ),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => complexActions);

        // Act
        await actionsUseCase.init();

        // Assert
        expect(actionsUseCase.objects, hasLength(1));
        expect(actionsUseCase.objects.first.actionAttributes, hasLength(1));
        expect(actionsUseCase.objects.first.actionAttributes, contains(ActionAttribute.immediateEffect));
      });

      test('should handle duplicate action IDs gracefully', () async {
        // Arrange
        final duplicateActions = [
          createTestCardAction(id: 'duplicate', name: 'Action 1', type: ActionType.move),
          createTestCardAction(id: 'duplicate', name: 'Action 2', type: ActionType.construct),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => duplicateActions);

        // Act
        await actionsUseCase.init();

        // Assert
        expect(actionsUseCase.objects, hasLength(2)); // Both actions should be present
        expect(actionsUseCase.objects.first.name, equals('Action 1'));
        expect(actionsUseCase.objects.last.name, equals('Action 2'));
      });
    });

    group('Integration Scenarios', () {
      test('should work in typical action loading workflow', () async {
        // Arrange - Simulate loading different types of actions
        final gameActions = [
          createTestCardAction(id: 'move', name: 'Move', type: ActionType.move),
          createTestCardAction(id: 'construct', name: 'Construct', type: ActionType.construct),
          createTestCardAction(id: 'changeGroup', name: 'Change Group', type: ActionType.changeGroup),
        ];
        
        when(() => mockActionsRepository.get())
            .thenAnswer((_) async => gameActions);

        // Act
        await actionsUseCase.init();

        // Assert - Verify we can access actions by type
        final actionNames = actionsUseCase.objects.map((a) => a.name).toList();
        expect(actionNames, contains('Move'));
        expect(actionNames, contains('Construct'));
        expect(actionNames, contains('Change Group'));

        // Verify we have different action types
        final actionTypes = actionsUseCase.objects.map((a) => a.type).toSet();
        expect(actionTypes, contains(ActionType.move));
        expect(actionTypes, contains(ActionType.construct));
        expect(actionTypes, contains(ActionType.changeGroup));
      });

      test('should handle re-initialization after error', () async {
        // Arrange - First call fails
        when(() => mockActionsRepository.get())
            .thenThrow(Exception('Network error'));

        // Act - First init fails
        expect(() => actionsUseCase.init(), throwsException);
        expect(actionsUseCase.status, equals(ProcessingStatus.loading));

        // Arrange - Second call succeeds (for new instance)
        final newMockRepository = MockActionsRepository();
        when(() => newMockRepository.get())
            .thenAnswer((_) async => [
              createTestCardAction(id: 'action1', name: 'Test Action', type: ActionType.move),
            ]);

        // Create new instance for retry (since status is stuck in loading)
        final retryUseCase = ActionsUseCase(newMockRepository);
        await retryUseCase.init();

        // Assert - Second attempt succeeds
        expect(retryUseCase.status, equals(ProcessingStatus.loaded));
        expect(retryUseCase.objects, hasLength(1));
      });
    });
  });
}

/// Test helper to create CardAction instances
CardAction createTestCardAction({
  String? id,
  String? name,
  ActionType? type,
  List<ActionAttribute>? actionAttributes,
}) {
  return CardAction(
    id: id ?? 'test-action-id',
    name: name ?? 'Test Action',
    type: type ?? ActionType.move,
    actionAttributes: actionAttributes ?? [],
  );
}
