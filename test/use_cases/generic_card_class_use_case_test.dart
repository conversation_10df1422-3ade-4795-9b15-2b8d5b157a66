import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/repositories/generic_card_class_repository.dart';
import 'package:dauntless/use_cases/generic_card_class_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

// Mock classes
class MockGenericCardClassRepository extends Mock implements GenericCardClassRepository {}

void main() {
  group('GenericCardClassUseCase', () {
    late GenericCardClassUseCase useCase;
    late MockGenericCardClassRepository mockRepository;

    setUp(() {
      mockRepository = MockGenericCardClassRepository();
      useCase = GenericCardClassUseCase(mockRepository);
    });

    group('getGameCardClasses', () {
      test('should return empty map when repository returns empty list', () async {
        // Arrange
        when(() => mockRepository.get()).thenAnswer((_) async => []);

        // Act
        final result = await useCase.getGameCardClasses();

        // Assert
        expect(result, isEmpty);
        verify(() => mockRepository.get()).called(1);
      });

      test('should return map with single card class', () async {
        // Arrange
        final cardClass = GameCardClass(
          id: 'card-class-1',
          name: 'Test Card Class',
          type: CardType.vehicle,
          attributes: {
            'cost': 10,
            'health': 5,
            'attack': 3,
            'description': 'A test card class',
          },
        );
        when(() => mockRepository.get()).thenAnswer((_) async => [cardClass]);

        // Act
        final result = await useCase.getGameCardClasses();

        // Assert
        expect(result, hasLength(1));
        expect(result.containsKey('card-class-1'), isTrue);
        expect(result['card-class-1'], equals(cardClass));
        verify(() => mockRepository.get()).called(1);
      });

      test('should return map with multiple card classes', () async {
        // Arrange
        final cardClasses = [
          GameCardClass(
            id: 'vehicle-1',
            name: 'Fighter Ship',
            type: CardType.vehicle,
            attributes: {
              'cost': 15,
              'health': 8,
              'attack': 6,
              'description': 'A fast fighter ship',
            },
          ),
          GameCardClass(
            id: 'location-1',
            name: 'Space Station',
            type: CardType.location,
            attributes: {
              'cost': 25,
              'health': 20,
              'attack': 0,
              'description': 'A defensive space station',
            },
          ),
          GameCardClass(
            id: 'building-1',
            name: 'Repair Station',
            type: CardType.building,
            attributes: {
              'cost': 5,
              'health': 0,
              'attack': 0,
              'description': 'Repair a damaged unit',
            },
          ),
        ];
        when(() => mockRepository.get()).thenAnswer((_) async => cardClasses);

        // Act
        final result = await useCase.getGameCardClasses();

        // Assert
        expect(result, hasLength(3));
        expect(result.containsKey('vehicle-1'), isTrue);
        expect(result.containsKey('location-1'), isTrue);
        expect(result.containsKey('building-1'), isTrue);
        expect(result['vehicle-1'], equals(cardClasses[0]));
        expect(result['location-1'], equals(cardClasses[1]));
        expect(result['building-1'], equals(cardClasses[2]));
        verify(() => mockRepository.get()).called(1);
      });

      test('should handle duplicate IDs by keeping the last one', () async {
        // Arrange
        final cardClasses = [
          GameCardClass(
            id: 'duplicate-id',
            name: 'First Card',
            type: CardType.vehicle,
            attributes: {
              'cost': 10,
              'health': 5,
              'attack': 3,
              'description': 'First card with duplicate ID',
            },
          ),
          GameCardClass(
            id: 'duplicate-id',
            name: 'Second Card',
            type: CardType.location,
            attributes: {
              'cost': 20,
              'health': 10,
              'attack': 0,
              'description': 'Second card with duplicate ID',
            },
          ),
        ];
        when(() => mockRepository.get()).thenAnswer((_) async => cardClasses);

        // Act
        final result = await useCase.getGameCardClasses();

        // Assert
        expect(result, hasLength(1));
        expect(result.containsKey('duplicate-id'), isTrue);
        expect(result['duplicate-id']!.name, equals('Second Card'));
        expect(result['duplicate-id']!.type, equals(CardType.location));
        verify(() => mockRepository.get()).called(1);
      });

      test('should handle repository errors gracefully', () async {
        // Arrange
        when(() => mockRepository.get()).thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(
          () => useCase.getGameCardClasses(),
          throwsA(isA<Exception>()),
        );
        verify(() => mockRepository.get()).called(1);
      });

      test('should handle large datasets efficiently', () async {
        // Arrange
        final cardClasses = List.generate(1000, (index) => GameCardClass(
          id: 'card-$index',
          name: 'Card $index',
          type: CardType.vehicle,
          attributes: {
            'cost': index % 50,
            'health': index % 20,
            'attack': index % 10,
            'description': 'Generated card $index',
          },
        ));
        when(() => mockRepository.get()).thenAnswer((_) async => cardClasses);

        // Act
        final result = await useCase.getGameCardClasses();

        // Assert
        expect(result, hasLength(1000));
        expect(result.containsKey('card-0'), isTrue);
        expect(result.containsKey('card-999'), isTrue);
        expect(result['card-500']!.name, equals('Card 500'));
        verify(() => mockRepository.get()).called(1);
      });

      test('should preserve all card class properties in the map', () async {
        // Arrange
        final cardClass = GameCardClass(
          id: 'detailed-card',
          name: 'Detailed Test Card',
          type: CardType.vehicle,
          attributes: {
            'cost': 42,
            'health': 15,
            'attack': 8,
            'description': 'A card with specific properties for testing',
          },
        );
        when(() => mockRepository.get()).thenAnswer((_) async => [cardClass]);

        // Act
        final result = await useCase.getGameCardClasses();

        // Assert
        final retrievedCard = result['detailed-card']!;
        expect(retrievedCard.id, equals('detailed-card'));
        expect(retrievedCard.name, equals('Detailed Test Card'));
        expect(retrievedCard.type, equals(CardType.vehicle));
        expect(retrievedCard.attributes['cost'], equals(42));
        expect(retrievedCard.attributes['health'], equals(15));
        expect(retrievedCard.attributes['attack'], equals(8));
        expect(retrievedCard.attributes['description'], equals('A card with specific properties for testing'));
      });

      test('should handle different card types correctly', () async {
        // Arrange
        final cardClasses = [
          GameCardClass(
            id: 'vehicle-test',
            name: 'Vehicle Card',
            type: CardType.vehicle,
            attributes: {
              'cost': 10,
              'health': 5,
              'attack': 3,
              'description': 'Vehicle type card',
            },
          ),
          GameCardClass(
            id: 'location-test',
            name: 'Location Card',
            type: CardType.location,
            attributes: {
              'cost': 15,
              'health': 10,
              'attack': 0,
              'description': 'Location type card',
            },
          ),
          GameCardClass(
            id: 'building-test',
            name: 'Building Card',
            type: CardType.building,
            attributes: {
              'cost': 5,
              'health': 0,
              'attack': 0,
              'description': 'Building type card',
            },
          ),
        ];
        when(() => mockRepository.get()).thenAnswer((_) async => cardClasses);

        // Act
        final result = await useCase.getGameCardClasses();

        // Assert
        expect(result['vehicle-test']!.type, equals(CardType.vehicle));
        expect(result['location-test']!.type, equals(CardType.location));
        expect(result['building-test']!.type, equals(CardType.building));
      });
    });

    group('Integration Scenarios', () {
      test('should work in a typical game loading workflow', () async {
        // Arrange
        final gameCardClasses = [
          GameCardClass(
            id: 'imperial-destroyer',
            name: 'Imperial Star Destroyer',
            type: CardType.vehicle,
            attributes: {
              'cost': 50,
              'health': 25,
              'attack': 15,
              'description': 'Massive Imperial warship',
            },
          ),
          GameCardClass(
            id: 'rebel-base',
            name: 'Rebel Base',
            type: CardType.location,
            attributes: {
              'cost': 40,
              'health': 30,
              'attack': 5,
              'description': 'Hidden rebel outpost',
            },
          ),
        ];
        when(() => mockRepository.get()).thenAnswer((_) async => gameCardClasses);

        // Act
        final cardClassMap = await useCase.getGameCardClasses();

        // Assert - Verify we can access cards by ID for game logic
        expect(cardClassMap['imperial-destroyer'], isNotNull);
        expect(cardClassMap['rebel-base'], isNotNull);

        // Verify we can use the map for game mechanics
        final destroyer = cardClassMap['imperial-destroyer']!;
        final base = cardClassMap['rebel-base']!;

        expect(destroyer.attributes['cost'] > base.attributes['cost'], isTrue); // Destroyer costs more
        expect(destroyer.attributes['attack'] > base.attributes['attack'], isTrue); // Destroyer has more attack
        expect(base.attributes['health'] > destroyer.attributes['health'], isTrue); // Base has more health
      });
    });
  });
}
