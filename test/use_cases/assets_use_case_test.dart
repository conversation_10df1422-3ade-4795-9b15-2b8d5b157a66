import 'package:dauntless/repositories/assets_repository.dart';
import 'package:dauntless/use_cases/assets_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockAssetsRepository extends Mock implements AssetsRepository {}

void main() {
  group('AssetsUseCase', () {
    late AssetsUseCase assetsUseCase;
    late MockAssetsRepository mockAssetsRepository;

    setUp(() {
      mockAssetsRepository = MockAssetsRepository();
      assetsUseCase = AssetsUseCase(mockAssetsRepository);
    });

    group('Constructor', () {
      test('should initialize with AssetsRepository dependency', () {
        expect(assetsUseCase, isA<AssetsUseCase>());
      });
    });

    group('loadAssetRefs', () {
      test('should return predefined asset references', () {
        // Arrange
        const basePath = '/test/path/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        expect(result, isA<Map<String, String>>());
        expect(result, isNotEmpty);
        
        // Verify specific asset references
        expect(result['imperial_fleet_icon'], equals('icons/imperial_fleet.png'));
        expect(result['alliance_fleet_icon'], equals('icons/alliance_fleet.png'));
        expect(result['neutral_building_icon'], equals('icons/neutral_building.png'));
        expect(result['alliance_building_icon'], equals('icons/alliance_building.png'));
        expect(result['imperial_building_icon'], equals('icons/imperial_building.png'));
        expect(result['neutral_battery_icon'], equals('icons/neutral_battery.png'));
        expect(result['alliance_battery_icon'], equals('icons/alliance_battery.png'));
        expect(result['imperial_battery_icon'], equals('icons/imperial_battery.png'));
        expect(result['imperial_missions_icon'], equals('icons/imperial_missions.png'));
        expect(result['alliance_missions_icon'], equals('icons/alliance_missions.png'));
      });

      test('should return same asset references regardless of base path', () {
        // Arrange
        const basePath1 = '/path1/';
        const basePath2 = '/completely/different/path/';
        const basePath3 = '';

        // Act
        final result1 = assetsUseCase.loadAssetRefs(basePath1);
        final result2 = assetsUseCase.loadAssetRefs(basePath2);
        final result3 = assetsUseCase.loadAssetRefs(basePath3);

        // Assert
        expect(result1, equals(result2));
        expect(result2, equals(result3));
        expect(result1, equals(result3));
      });

      test('should return exactly 10 asset references', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        expect(result.length, equals(10));
      });

      test('should return asset references with correct icon categories', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        // Check fleet icons
        final fleetIcons = result.keys.where((key) => key.contains('fleet')).toList();
        expect(fleetIcons.length, equals(2));
        expect(fleetIcons, contains('imperial_fleet_icon'));
        expect(fleetIcons, contains('alliance_fleet_icon'));

        // Check building icons
        final buildingIcons = result.keys.where((key) => key.contains('building')).toList();
        expect(buildingIcons.length, equals(3));
        expect(buildingIcons, contains('neutral_building_icon'));
        expect(buildingIcons, contains('alliance_building_icon'));
        expect(buildingIcons, contains('imperial_building_icon'));

        // Check battery icons
        final batteryIcons = result.keys.where((key) => key.contains('battery')).toList();
        expect(batteryIcons.length, equals(3));
        expect(batteryIcons, contains('neutral_battery_icon'));
        expect(batteryIcons, contains('alliance_battery_icon'));
        expect(batteryIcons, contains('imperial_battery_icon'));

        // Check mission icons
        final missionIcons = result.keys.where((key) => key.contains('missions')).toList();
        expect(missionIcons.length, equals(2));
        expect(missionIcons, contains('imperial_missions_icon'));
        expect(missionIcons, contains('alliance_missions_icon'));
      });

      test('should return asset references with PNG file extensions', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        for (final assetPath in result.values) {
          expect(assetPath, endsWith('.png'));
          expect(assetPath, startsWith('icons/'));
        }
      });

      test('should handle empty base path', () {
        // Arrange
        const basePath = '';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        expect(result, isNotEmpty);
        expect(result.length, equals(10));
      });

      test('should handle null-like base path', () {
        // Arrange
        const basePath = 'null';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        expect(result, isNotEmpty);
        expect(result.length, equals(10));
      });

      test('should handle very long base path', () {
        // Arrange
        const basePath = '/very/long/path/that/goes/on/and/on/and/on/and/on/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        expect(result, isNotEmpty);
        expect(result.length, equals(10));
      });

      test('should handle special characters in base path', () {
        // Arrange
        const basePath = '/path/with/special-chars_123/!@#\$%/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        expect(result, isNotEmpty);
        expect(result.length, equals(10));
      });
    });

    group('Asset Reference Validation', () {
      test('should have consistent naming pattern for faction-specific assets', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        // Imperial assets
        final imperialAssets = result.keys.where((key) => key.startsWith('imperial_')).toList();
        expect(imperialAssets.length, equals(4));
        for (final asset in imperialAssets) {
          expect(asset, endsWith('_icon'));
          expect(result[asset], startsWith('icons/imperial_'));
        }

        // Alliance assets
        final allianceAssets = result.keys.where((key) => key.startsWith('alliance_')).toList();
        expect(allianceAssets.length, equals(4));
        for (final asset in allianceAssets) {
          expect(asset, endsWith('_icon'));
          expect(result[asset], startsWith('icons/alliance_'));
        }

        // Neutral assets
        final neutralAssets = result.keys.where((key) => key.startsWith('neutral_')).toList();
        expect(neutralAssets.length, equals(2));
        for (final asset in neutralAssets) {
          expect(asset, endsWith('_icon'));
          expect(result[asset], startsWith('icons/neutral_'));
        }
      });

      test('should have unique asset keys', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        final keys = result.keys.toList();
        final uniqueKeys = keys.toSet().toList();
        expect(keys.length, equals(uniqueKeys.length));
      });

      test('should have unique asset paths', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        final paths = result.values.toList();
        final uniquePaths = paths.toSet().toList();
        expect(paths.length, equals(uniquePaths.length));
      });

      test('should not depend on repository for asset references', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert
        expect(result, isNotEmpty);
        // Verify that no repository methods were called
        verifyZeroInteractions(mockAssetsRepository);
      });
    });

    group('Performance', () {
      test('should return asset references quickly', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final stopwatch = Stopwatch()..start();
        final result = assetsUseCase.loadAssetRefs(basePath);
        stopwatch.stop();

        // Assert
        expect(result, isNotEmpty);
        expect(stopwatch.elapsedMilliseconds, lessThan(10)); // Should be very fast
      });

      test('should handle multiple calls efficiently', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final stopwatch = Stopwatch()..start();
        for (int i = 0; i < 1000; i++) {
          assetsUseCase.loadAssetRefs(basePath);
        }
        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should handle many calls quickly
      });
    });

    group('Integration Scenarios', () {
      test('should work in a typical asset loading workflow', () {
        // Arrange
        const gameBasePath = '/games/liberator/';

        // Act
        final assetRefs = assetsUseCase.loadAssetRefs(gameBasePath);

        // Assert - Verify we can access all expected game assets
        expect(assetRefs['imperial_fleet_icon'], isNotNull);
        expect(assetRefs['alliance_fleet_icon'], isNotNull);
        
        // Verify asset paths are valid for file system access
        for (final assetPath in assetRefs.values) {
          expect(assetPath, isNotEmpty);
          expect(assetPath, isNot(contains('//')));
          expect(assetPath, isNot(startsWith('/')));
        }
      });

      test('should provide assets for different game factions', () {
        // Arrange
        const basePath = '/test/';

        // Act
        final result = assetsUseCase.loadAssetRefs(basePath);

        // Assert - Verify we have assets for both major factions
        final imperialAssets = result.keys.where((k) => k.contains('imperial')).length;
        final allianceAssets = result.keys.where((k) => k.contains('alliance')).length;
        final neutralAssets = result.keys.where((k) => k.contains('neutral')).length;

        expect(imperialAssets, greaterThan(0));
        expect(allianceAssets, greaterThan(0));
        expect(neutralAssets, greaterThan(0));
        
        // Should have balanced representation
        expect((imperialAssets - allianceAssets).abs(), lessThanOrEqualTo(2));
      });
    });
  });
}
