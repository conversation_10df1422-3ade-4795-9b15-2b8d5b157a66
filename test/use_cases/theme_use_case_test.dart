import 'package:dauntless/repositories/theme_repository.dart';
import 'package:dauntless/use_cases/theme_use_case.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockThemeRepository extends Mock implements ThemeRepository {}

void main() {
  group('ThemeUseCase', () {
    late ThemeUseCase themeUseCase;
    late MockThemeRepository mockThemeRepository;

    setUp(() {
      mockThemeRepository = MockThemeRepository();
      themeUseCase = ThemeUseCase(mockThemeRepository);
    });

    group('Constructor', () {
      test('should initialize with null theme', () {
        expect(themeUseCase.theme, isNull);
      });

      test('should accept ThemeRepository dependency', () {
        expect(themeUseCase, isA<ThemeUseCase>());
      });
    });

    group('init', () {
      test('should load theme successfully', () async {
        // Arrange
        final expectedTheme = ThemeData.light();
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => expectedTheme);

        // Act
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, equals(expectedTheme));
        verify(() => mockThemeRepository.get()).called(1);
      });

      test('should handle null theme from repository', () async {
        // Arrange
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => null);

        // Act
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, isNull);
        verify(() => mockThemeRepository.get()).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        when(() => mockThemeRepository.get())
            .thenThrow(Exception('Failed to load theme'));

        // Act & Assert
        expect(
          () => themeUseCase.init(),
          throwsException,
        );
        verify(() => mockThemeRepository.get()).called(1);
      });

      test('should handle multiple init calls', () async {
        // Arrange
        final expectedTheme = ThemeData.dark();
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => expectedTheme);

        // Act
        await themeUseCase.init();
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, equals(expectedTheme));
        verify(() => mockThemeRepository.get()).called(2);
      });
    });

    group('theme getter', () {
      test('should return current theme', () async {
        // Arrange
        final expectedTheme = ThemeData.light();
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => expectedTheme);

        // Act
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, equals(expectedTheme));
        expect(themeUseCase.theme, isA<ThemeData>());
      });

      test('should return null before initialization', () {
        // Assert
        expect(themeUseCase.theme, isNull);
      });

      test('should return updated theme after re-initialization', () async {
        // Arrange
        final firstTheme = ThemeData.light();
        final secondTheme = ThemeData.dark();
        
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => firstTheme);

        // Act - First initialization
        await themeUseCase.init();
        expect(themeUseCase.theme, equals(firstTheme));

        // Arrange - Change repository response
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => secondTheme);

        // Act - Second initialization
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, equals(secondTheme));
        expect(themeUseCase.theme, isNot(equals(firstTheme)));
      });
    });

    group('Edge Cases', () {
      test('should handle complex ThemeData objects', () async {
        // Arrange
        final complexTheme = ThemeData(
          primaryColor: Colors.blue,
          brightness: Brightness.dark,
          fontFamily: 'Roboto',
          textTheme: const TextTheme(
            headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
            bodyLarge: TextStyle(fontSize: 16),
          ),
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        );

        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => complexTheme);

        // Act
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, equals(complexTheme));
        expect(themeUseCase.theme!.primaryColor, equals(Colors.blue));
        expect(themeUseCase.theme!.brightness, equals(Brightness.dark));
      });

      test('should handle repository timeout', () async {
        // Arrange
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return ThemeData.light();
        });

        // Act
        final stopwatch = Stopwatch()..start();
        await themeUseCase.init();
        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, greaterThan(90));
        expect(themeUseCase.theme, isNotNull);
      });

      test('should handle concurrent init calls', () async {
        // Arrange
        final expectedTheme = ThemeData.light();
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return expectedTheme;
        });

        // Act - Start multiple concurrent init calls
        final futures = [
          themeUseCase.init(),
          themeUseCase.init(),
          themeUseCase.init(),
        ];
        await Future.wait(futures);

        // Assert
        expect(themeUseCase.theme, equals(expectedTheme));
        verify(() => mockThemeRepository.get()).called(3);
      });

      test('should handle repository returning different theme types', () async {
        // Arrange - Test with Material 3 theme
        final material3Theme = ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        );
        
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => material3Theme);

        // Act
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, equals(material3Theme));
        expect(themeUseCase.theme!.useMaterial3, isTrue);
      });
    });

    group('Integration Scenarios', () {
      test('should work with real-world theme loading pattern', () async {
        // Arrange - Simulate loading a theme from JSON
        final jsonTheme = ThemeData(
          primaryColor: const Color(0xFF2196F3),
          scaffoldBackgroundColor: const Color(0xFFF5F5F5),
          cardColor: Colors.white,
        );
        
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => jsonTheme);

        // Act
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme, isNotNull);
        expect(themeUseCase.theme!.primaryColor, equals(const Color(0xFF2196F3)));
        expect(themeUseCase.theme!.scaffoldBackgroundColor, equals(const Color(0xFFF5F5F5)));
      });

      test('should handle theme switching scenario', () async {
        // Arrange - Light theme first
        final lightTheme = ThemeData.light();
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => lightTheme);

        // Act - Load light theme
        await themeUseCase.init();
        expect(themeUseCase.theme!.brightness, equals(Brightness.light));

        // Arrange - Switch to dark theme
        final darkTheme = ThemeData.dark();
        when(() => mockThemeRepository.get())
            .thenAnswer((_) async => darkTheme);

        // Act - Load dark theme
        await themeUseCase.init();

        // Assert
        expect(themeUseCase.theme!.brightness, equals(Brightness.dark));
        verify(() => mockThemeRepository.get()).called(2);
      });
    });
  });
}
