import 'dart:math';

import 'package:dauntless/repositories/locations_repository.dart';

final LocationsRepository locationsRepository = LocationsRepository();

main() async {
  final systems = await locationsRepository.get();
  final matrix = _generateSymmetricMatrix(
    numRows: 21,
    numCols: 21,
    randomMultiplier: 100.0,
    randomAddend: -10.0,
  );

  print(matrix);
}

void _getSectors() async {
  final sectors = await locationsRepository.getGroupings();

}

List<List<double>> _generateSymmetricMatrix({
  required int numRows,
  required int numCols,
  double randomMultiplier = 1.0,
  double randomAddend = 0.0,
}) {
  final matrix =
      List.generate(numRows, (index) => List.generate(numCols, (index) => 0.0));

  for (var i = 0; i < numRows; i++) {
    for (var j = 0; j <= i; j++) {
      matrix[i][j] = (Random().nextDouble() * randomMultiplier) + randomAddend;
    }
  }

  // Mirror the upper triangular part
  for (var i = 0; i < numRows; i++) {
    for (var j = i + 1; j < numCols; j++) {
      matrix[i][j] = matrix[j][i];
    }
  }

  return matrix;
}
