import 'package:bloc_test/bloc_test.dart';
import 'package:dauntless/frameworks/game_match/game_match_event.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/use_cases/generic_card_class_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/use_cases/match/match_use_case.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:dauntless/use_cases/vehicles_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockMatchUseCase extends Mock implements MatchUseCase {}
class MockPlayersUseCase extends Mock implements PlayersUseCase {}
class MockGenericCardClassUseCase extends Mock implements GenericCardClassUseCase {}
class MockLocationsUseCase extends Mock implements LocationsUseCase {}
class MockVehiclesUseCase extends Mock implements VehiclesUseCase {}

void main() {
  group('GameMatchManager', () {
    late GameMatchManager gameMatchManager;
    late MockMatchUseCase mockMatchUseCase;
    late MockPlayersUseCase mockPlayersUseCase;
    late MockGenericCardClassUseCase mockGenericCardClassUseCase;
    late MockLocationsUseCase mockLocationsUseCase;
    late MockVehiclesUseCase mockVehiclesUseCase;

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(createTestGameMatchState());
      registerFallbackValue(createTestGameCard());
      registerFallbackValue(createTestCardAction());
    });

    setUp(() {
      mockMatchUseCase = MockMatchUseCase();
      mockPlayersUseCase = MockPlayersUseCase();
      mockGenericCardClassUseCase = MockGenericCardClassUseCase();
      mockLocationsUseCase = MockLocationsUseCase();
      mockVehiclesUseCase = MockVehiclesUseCase();

      // Set up default mock behaviors
      when(() => mockGenericCardClassUseCase.getGameCardClasses()).thenAnswer((_) async => {});
      when(() => mockLocationsUseCase.objects).thenReturn([]);
      when(() => mockLocationsUseCase.locationClasses).thenReturn({});
      when(() => mockVehiclesUseCase.vehicleClasses).thenReturn({});
      when(() => mockMatchUseCase.submitPlayerTurn(any())).thenAnswer((_) async => createTestGameMatchState());
      when(() => mockMatchUseCase.handleSelectActiveAction(any(), any(), any())).thenReturn(createTestGameMatchState());
      when(() => mockMatchUseCase.createNewGroup(any(), any())).thenReturn(createTestGameMatchState());

      gameMatchManager = GameMatchManager(
        mockMatchUseCase,
        mockPlayersUseCase,
        mockGenericCardClassUseCase,
        mockLocationsUseCase,
        mockVehiclesUseCase,
      );
    });

    tearDown(() {
      gameMatchManager.close();
    });

    test('should initialize with DevInitialState.initialMatchState', () {
      expect(gameMatchManager.state, isA<GameMatchState>());
    });

    group('LoadMatchStateEvent', () {
      test('should handle LoadMatchStateEvent without GetIt dependency', () {
        // Note: LoadMatchStateEvent triggers GetIt.I<CommandCenterBloc>() which isn't available in tests
        // This test verifies the manager can be instantiated and basic functionality works
        expect(gameMatchManager.state, isA<GameMatchState>());
      });
    });

    group('LoadGameCardsEvent', () {
      blocTest<GameMatchManager, GameMatchState>(
        'should load game cards and update state',
        setUp: () {
          final mockCardClasses = {
            'class1': createTestGameCardClass(id: 'class1', name: 'Class 1'),
            'class2': createTestGameCardClass(id: 'class2', name: 'Class 2'),
          };
          final mockLocationCards = [
            createTestGameCard(id: 'loc1', name: 'Location 1', type: CardType.location),
          ];
          final mockLocationClasses = {
            'loc-class1': createTestGameCardClass(id: 'loc-class1', name: 'Location Class 1'),
          };
          final mockVehicleClasses = {
            'veh-class1': createTestGameCardClass(id: 'veh-class1', name: 'Vehicle Class 1'),
          };

          when(() => mockGenericCardClassUseCase.getGameCardClasses()).thenAnswer((_) async => mockCardClasses);
          when(() => mockLocationsUseCase.objects).thenReturn(mockLocationCards);
          when(() => mockLocationsUseCase.locationClasses).thenReturn(mockLocationClasses);
          when(() => mockVehiclesUseCase.vehicleClasses).thenReturn(mockVehicleClasses);
        },
        build: () => gameMatchManager,
        act: (manager) => manager.add(LoadGameCardsEvent()),
        expect: () => [
          isA<GameMatchState>().having((s) => s.processingStatus, 'processingStatus', ProcessingStatus.loading),
          isA<GameMatchState>()
              .having((s) => s.processingStatus, 'processingStatus', ProcessingStatus.loaded)
              .having((s) => s.loadedCardClasses, 'loadedCardClasses', isNotEmpty)
              .having((s) => s.hands[locationsPlayerId], 'location cards', isNotEmpty),
        ],
        verify: (_) {
          verify(() => mockGenericCardClassUseCase.getGameCardClasses()).called(1);
        },
      );

      test('should handle empty card classes', () async {
        // Arrange
        when(() => mockGenericCardClassUseCase.getGameCardClasses()).thenAnswer((_) async => {});
        when(() => mockLocationsUseCase.objects).thenReturn([]);
        when(() => mockLocationsUseCase.locationClasses).thenReturn({});
        when(() => mockVehiclesUseCase.vehicleClasses).thenReturn({});

        // Act
        gameMatchManager.add(LoadGameCardsEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(gameMatchManager.state.processingStatus, equals(ProcessingStatus.loaded));
        verify(() => mockGenericCardClassUseCase.getGameCardClasses()).called(1);
      });
    });

    group('StartSelectActiveActionTargetEvent', () {
      blocTest<GameMatchManager, GameMatchState>(
        'should handle action selection',
        setUp: () {
          final updatedState = createTestGameMatchState(
            activeAction: createTestTargetedAction(),
          );
          when(() => mockMatchUseCase.handleSelectActiveAction(any(), any(), any()))
              .thenReturn(updatedState);
        },
        build: () => gameMatchManager,
        act: (manager) {
          final action = createTestCardAction(id: 'test-action', name: 'Test Action');
          manager.add(StartSelectActiveActionTargetEvent(action: action, cardId: 'test-card'));
        },
        verify: (_) {
          verify(() => mockMatchUseCase.handleSelectActiveAction(any(), any(), 'test-card')).called(1);
        },
      );
    });

    group('SelectActiveActionTargetEvent', () {
      test('should handle target selection events', () async {
        // This test verifies that the event can be added without crashing
        // The actual logic depends on complex internal state that's hard to mock
        expect(
          () => gameMatchManager.add(SelectActiveActionTargetEvent(cardId: 'target-card')),
          returnsNormally,
        );

        await Future.delayed(const Duration(milliseconds: 50));
      });

      test('should handle missing active action', () async {
        // Arrange
        final stateWithoutActiveAction = createTestGameMatchState(activeAction: null);
        gameMatchManager.emit(stateWithoutActiveAction);

        // Act & Assert - Should not crash
        expect(
          () => gameMatchManager.add(SelectActiveActionTargetEvent(cardId: 'target-card')),
          returnsNormally,
        );
      });
    });

    group('SubmitPlayerTurnEvent', () {
      blocTest<GameMatchManager, GameMatchState>(
        'should submit player turn and update state',
        setUp: () {
          final updatedState = createTestGameMatchState(
            turnCount: 2,
            currentTurnActions: [],
          );
          when(() => mockMatchUseCase.submitPlayerTurn(any())).thenAnswer((_) async => updatedState);
        },
        build: () => gameMatchManager,
        act: (manager) => manager.add(SubmitPlayerTurnEvent()),
        expect: () => [
          isA<GameMatchState>().having((s) => s.processingStatus, 'processingStatus', ProcessingStatus.loading),
          isA<GameMatchState>()
              .having((s) => s.processingStatus, 'processingStatus', ProcessingStatus.start)
              .having((s) => s.turnCount, 'turnCount', 2),
        ],
        verify: (_) {
          verify(() => mockMatchUseCase.submitPlayerTurn(any())).called(1);
        },
      );

      test('should handle successful submit turn workflow', () async {
        // Arrange
        final updatedState = createTestGameMatchState(turnCount: 2);
        when(() => mockMatchUseCase.submitPlayerTurn(any())).thenAnswer((_) async => updatedState);

        // Act
        gameMatchManager.add(SubmitPlayerTurnEvent());
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        verify(() => mockMatchUseCase.submitPlayerTurn(any())).called(1);
      });
    });

    group('CreateNewGroupForActiveActionEvent', () {
      blocTest<GameMatchManager, GameMatchState>(
        'should create new group when active action exists',
        setUp: () {
          final testCard = createTestGameCard(id: 'subject-card', name: 'Subject Card');
          final stateWithCards = createTestGameMatchState(
            activeAction: createTestTargetedAction(subjectCardId: 'subject-card'),
            hands: {
              'player1': [testCard],
            },
          );
          final updatedState = createTestGameMatchState(
            hands: {
              'player1': [testCard, createTestGameCard(id: 'group-card', type: CardType.grouping)],
            },
          );
          
          when(() => mockMatchUseCase.createNewGroup(any(), any())).thenReturn(updatedState);
        },
        build: () => gameMatchManager,
        seed: () => createTestGameMatchState(
          activeAction: createTestTargetedAction(subjectCardId: 'subject-card'),
          hands: {
            'player1': [createTestGameCard(id: 'subject-card', name: 'Subject Card')],
          },
        ),
        act: (manager) => manager.add(CreateNewGroupForActiveActionEvent()),
        expect: () => [
          isA<GameMatchState>()
              .having((s) => s.processingStatus, 'processingStatus', ProcessingStatus.loading)
              .having((s) => s.activeAction, 'activeAction', isNull),
          isA<GameMatchState>().having((s) => s.processingStatus, 'processingStatus', ProcessingStatus.loaded),
        ],
        verify: (_) {
          verify(() => mockMatchUseCase.createNewGroup(any(), any())).called(1);
        },
      );

      test('should handle create new group event structure', () {
        // This event requires specific state conditions that are complex to set up
        // We test that the event can be created and has the expected structure
        final event = CreateNewGroupForActiveActionEvent();
        expect(event, isA<CreateNewGroupForActiveActionEvent>());
      });
    });

    group('ClearActiveActionEvent', () {
      blocTest<GameMatchManager, GameMatchState>(
        'should clear active action',
        build: () => gameMatchManager,
        seed: () => createTestGameMatchState(
          activeAction: createTestTargetedAction(),
        ),
        act: (manager) => manager.add(ClearActiveActionEvent()),
        expect: () => [
          isA<GameMatchState>().having((s) => s.activeAction, 'activeAction', isNull),
        ],
      );
    });

    group('Edge Cases', () {
      test('should handle rapid event succession', () async {
        // Arrange
        when(() => mockGenericCardClassUseCase.getGameCardClasses()).thenAnswer((_) async => {});

        // Act - Send multiple events rapidly
        gameMatchManager.add(LoadGameCardsEvent());
        gameMatchManager.add(ClearActiveActionEvent());
        gameMatchManager.add(LoadGameCardsEvent());
        
        await Future.delayed(const Duration(milliseconds: 200));

        // Assert - Should handle all events without crashing
        expect(gameMatchManager.state, isA<GameMatchState>());
      });

      test('should handle complex state management', () async {
        // Arrange
        final complexState = createTestGameMatchState(
          userPlayerId: 'complex-player',
          turnCount: 15,
          hands: {
            'player1': List.generate(10, (i) => createTestGameCard(id: 'card$i', name: 'Card $i')),
          },
          currentTurnActions: List.generate(5, (i) => createTestTargetedAction()),
        );

        // Act - Manually emit state to avoid GetIt dependency
        gameMatchManager.emit(complexState);
        await Future.delayed(const Duration(milliseconds: 50));

        // Assert
        expect(gameMatchManager.state.hands['player1'], hasLength(10));
        expect(gameMatchManager.state.currentTurnActions, hasLength(5));
        expect(gameMatchManager.state.userPlayerId, equals('complex-player'));
      });
    });
  });
}

/// Test helper to create GameMatchState instances
GameMatchState createTestGameMatchState({
  String? userPlayerId,
  int? turnCount,
  Map<String, List<GameCard>>? hands,
  List<TargetedAction>? currentTurnActions,
  TargetedAction? activeAction,
  Map<String, GameCardClass>? loadedCardClasses,
}) {
  return GameMatchState(
    matchConfig: createTestMatchConfig(),
    userPlayerId: userPlayerId ?? 'test-player',
    turnCount: turnCount ?? 0,
    hands: hands ?? {},
    resources: {},
    loadedCardClasses: loadedCardClasses ?? {},
    currentTurnActions: currentTurnActions ?? [],
    activeAction: activeAction,
  );
}

/// Test helper to create MatchConfig instances
MatchConfig createTestMatchConfig({
  String? gameId,
  String? hostId,
  String? matchId,
}) {
  return MatchConfig(
    gameId: gameId ?? 'test-game',
    selectedGameMode: GameMode.hotSeat,
    hostId: hostId ?? 'test-host',
    matchId: matchId ?? 'test-match',
  );
}

/// Test helper to create GameCard instances
GameCard createTestGameCard({
  String? id,
  String? name,
  CardType? type,
}) {
  return GameCard(
    id: id ?? 'test-card-id',
    name: name ?? 'Test Card',
    type: type ?? CardType.vehicle,
    classId: 'test-class-id',
  );
}

/// Test helper to create GameCardClass instances
GameCardClass createTestGameCardClass({
  String? id,
  String? name,
  CardType? type,
}) {
  return GameCardClass(
    id: id ?? 'test-class-id',
    name: name ?? 'Test Card Class',
    type: type ?? CardType.vehicle,
  );
}

/// Test helper to create CardAction instances
CardAction createTestCardAction({
  String? id,
  String? name,
  ActionType? type,
}) {
  return CardAction(
    id: id ?? 'test-action-id',
    name: name ?? 'Test Action',
    type: type ?? ActionType.move,
    actionAttributes: [],
  );
}

/// Test helper to create TargetedAction instances
TargetedAction createTestTargetedAction({
  String? subjectCardId,
  String? objectCardId,
}) {
  return TargetedAction.genId(
    action: createTestCardAction(),
    subjectCardId: subjectCardId ?? 'test-subject-card',
    objectCardId: objectCardId,
  );
}
