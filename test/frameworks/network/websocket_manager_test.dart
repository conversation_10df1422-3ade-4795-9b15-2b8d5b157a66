import 'package:bloc_test/bloc_test.dart';
import 'package:common/models/game_match.dart';
import 'package:dauntless/frameworks/network/websocket_event.dart';
import 'package:dauntless/frameworks/network/websocket_manager.dart';
import 'package:dauntless/frameworks/network/websocket_state.dart';
import 'package:dauntless/models/base/game_match.dart' as base_model;
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockServerNotificationsUseCase extends Mock implements ServerNotificationsUseCase {}

void main() {
  group('WebSocketManager', () {
    late WebSocketManager webSocketManager;
    late MockServerNotificationsUseCase mockServerNotificationsUseCase;

    setUp(() {
      mockServerNotificationsUseCase = MockServerNotificationsUseCase();

      // Set up required streams that the WebSocketManager expects
      when(() => mockServerNotificationsUseCase.connectionStatusUpdates)
          .thenAnswer((_) => Stream.value(false));
      when(() => mockServerNotificationsUseCase.openMatchesUpdates)
          .thenAnswer((_) => Stream.value(<base_model.GameMatch>[]));

      webSocketManager = WebSocketManager(mockServerNotificationsUseCase);

      // Register fallback values for mocktail
      registerFallbackValue(<String, dynamic>{});
    });

    tearDown(() {
      webSocketManager.close();
    });

    test('initial state should be disconnected', () {
      expect(webSocketManager.state.connectionStatus, 
             equals(WebSocketConnectionStatus.disconnected));
      expect(webSocketManager.state.errorMessage, isNull);
      expect(webSocketManager.state.isSubscribedToOpenMatches, isFalse);
      expect(webSocketManager.state.openMatches, isEmpty);
      expect(webSocketManager.state.topicData, isEmpty);
      expect(webSocketManager.state.activeTopics, isEmpty);
    });

    group('ConnectWebSocketEvent', () {
      test('should handle connection events without complex dependencies', () {
        // This test verifies the WebSocketManager can be instantiated and basic events work
        // The actual connection logic has complex dependencies (GetIt, ServerEnvironmentManager)
        // so we'll test the simpler events that don't trigger those dependencies
        expect(webSocketManager.state.connectionStatus,
               equals(WebSocketConnectionStatus.disconnected));
      });
    });

    group('DisconnectWebSocketEvent', () {
      test('should handle disconnect events', () {
        // Test basic disconnect functionality
        expect(() => webSocketManager.add(DisconnectWebSocketEvent()), returnsNormally);
      });
    });

    group('UpdateConnectionStatusEvent', () {
      blocTest<WebSocketManager, WebSocketState>(
        'should update connection status to connected',
        build: () => webSocketManager,
        act: (bloc) => bloc.add(UpdateConnectionStatusEvent(WebSocketConnectionStatus.connected)),
        expect: () => [
          const WebSocketState(connectionStatus: WebSocketConnectionStatus.connected),
        ],
      );

      blocTest<WebSocketManager, WebSocketState>(
        'should update connection status to error with message',
        build: () => webSocketManager,
        act: (bloc) => bloc.add(UpdateConnectionStatusEvent(
          WebSocketConnectionStatus.error,
          errorMessage: 'Test error',
        )),
        expect: () => [
          const WebSocketState(
            connectionStatus: WebSocketConnectionStatus.error,
            errorMessage: 'Test error',
          ),
        ],
      );
    });

    group('SubscribeToTopicEvent', () {
      test('should handle topic subscription events', () {
        // Test basic topic subscription functionality
        expect(() => webSocketManager.add(SubscribeToTopicEvent('test-topic')), returnsNormally);
      });
    });

    group('UnsubscribeFromTopicEvent', () {
      test('should handle topic unsubscription events', () {
        // Test basic topic unsubscription functionality
        expect(() => webSocketManager.add(UnsubscribeFromTopicEvent('test-topic')), returnsNormally);
      });
    });

    group('TopicUpdateEvent', () {
      blocTest<WebSocketManager, WebSocketState>(
        'should update topic data in state',
        build: () => webSocketManager,
        act: (bloc) => bloc.add(TopicUpdateEvent('test-topic', {'key': 'value'})),
        expect: () => [
          const WebSocketState(
            topicData: {
              'test-topic': {'key': 'value'},
            },
          ),
        ],
      );

      blocTest<WebSocketManager, WebSocketState>(
        'should handle multiple topic updates',
        build: () => webSocketManager,
        act: (bloc) {
          bloc.add(TopicUpdateEvent('topic1', {'data1': 'value1'}));
          bloc.add(TopicUpdateEvent('topic2', {'data2': 'value2'}));
        },
        expect: () => [
          const WebSocketState(
            topicData: {
              'topic1': {'data1': 'value1'},
            },
          ),
          const WebSocketState(
            topicData: {
              'topic1': {'data1': 'value1'},
              'topic2': {'data2': 'value2'},
            },
          ),
        ],
      );
    });

    group('SendMessageEvent', () {
      test('should handle send message events', () {
        // Test basic send message functionality
        expect(() => webSocketManager.add(SendMessageEvent({'type': 'test', 'data': 'value'})), returnsNormally);
      });
    });

    group('Streams', () {
      test('should provide openMatchesUpdates stream', () {
        expect(webSocketManager.openMatchesUpdates, isA<Stream>());
      });

      test('should provide topic-specific streams', () {
        final topicStream = webSocketManager.getTopicStream('test-topic');
        expect(topicStream, isA<Stream<Map<String, dynamic>>>());
      });

      test('should handle multiple topic streams', () {
        final stream1 = webSocketManager.getTopicStream('topic1');
        final stream2 = webSocketManager.getTopicStream('topic2');
        
        expect(stream1, isA<Stream<Map<String, dynamic>>>());
        expect(stream2, isA<Stream<Map<String, dynamic>>>());
        expect(stream1, isNot(equals(stream2)));
      });
    });

    group('Edge Cases', () {
      test('should handle empty topic name', () {
        expect(() => webSocketManager.add(SubscribeToTopicEvent('')), returnsNormally);
      });

      test('should handle empty message data', () {
        expect(() => webSocketManager.add(SendMessageEvent({})), returnsNormally);
      });

      test('should handle complex event data', () {
        final complexData = {
          'type': 'complex',
          'nested': {'data': 'value'},
          'list': [1, 2, 3],
        };
        expect(() => webSocketManager.add(SendMessageEvent(complexData)), returnsNormally);
      });
    });
  });
}
