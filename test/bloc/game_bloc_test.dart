import 'package:test/test.dart';

void main() {
  group('Test Infrastructure', () {
    test('should be able to run basic tests', () {
      // This test verifies that the test infrastructure is working
      expect(1 + 1, equals(2));
    });

    test('should handle string operations', () {
      const testString = 'Hello, World!';
      expect(testString.length, equals(13));
      expect(testString.contains('World'), isTrue);
    });

    test('should handle list operations', () {
      final testList = [1, 2, 3, 4, 5];
      expect(testList.length, equals(5));
      expect(testList.first, equals(1));
      expect(testList.last, equals(5));
    });

    test('should handle map operations', () {
      final testMap = {'key1': 'value1', 'key2': 'value2'};
      expect(testMap.length, equals(2));
      expect(testMap['key1'], equals('value1'));
      expect(testMap.containsKey('key2'), isTrue);
    });

    test('should handle async operations', () async {
      final future = Future.delayed(
        const Duration(milliseconds: 10),
        () => 'async result',
      );

      final result = await future;
      expect(result, equals('async result'));
    });

    test('should handle exceptions', () {
      expect(() => throw Exception('test exception'), throwsException);
    });
  });
}
