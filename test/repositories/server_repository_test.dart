import 'dart:async';

import 'package:common/models/game.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/api/dauntless_api.dart';
import 'package:dauntless/api/dtos/create_match_dto.dart';
import 'package:dauntless/api/dtos/join_match_dto.dart';
import 'package:dauntless/api/dtos/submit_turn_dto.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockDauntlessApi extends Mock implements DauntlessApi {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

/// Test helper to create GameMatch instances
GameMatch createTestGameMatch({
  String? id,
  String? gameTypeId,
  String? creatorId,
  String? gameName,
  MatchStatus? status,
}) {
  final now = DateTime.now().millisecondsSinceEpoch;
  return GameMatch(
    id: id ?? 'test-match-id',
    gameTypeId: gameTypeId ?? 'test-game-type',
    creatorId: creatorId ?? 'test-creator-id',
    gameName: gameName,
    createdAt: now,
    updatedAt: now,
    status: status ?? MatchStatus.open,
  );
}

/// Test helper to create PlayerSlot instances
PlayerSlot createTestPlayerSlot({
  String? id,
  String? playerId,
  String? name,
  PlayerType? type,
  String? playerClassId,
}) {
  return PlayerSlot(
    id: id ?? 'test-slot-id',
    playerId: playerId,
    name: name ?? 'Test Player Slot',
    type: type ?? PlayerType.humanLocal,
    playerClassId: playerClassId ?? 'test-class-id',
  );
}

void main() {
  group('ServerRepository', () {
    late ServerRepository repository;
    late MockDauntlessApi mockApi;
    late MockRemoteLogger mockLogger;

    setUp(() {
      mockApi = MockDauntlessApi();
      mockLogger = MockRemoteLogger();
      repository = ServerRepository(mockLogger, mockApi);

      // Register fallback values for mocktail
      registerFallbackValue(const CreateMatchDto(
        gameTypeId: 'test',
        creatorId: 'test',
      ));
      registerFallbackValue(const JoinMatchDto(
        id: 'test',
        playerId: 'test',
      ));
      registerFallbackValue(SubmitTurnDto(
        gameMatchId: 'test',
        playerId: 'test',
        move: [],
        turnNumber: 1,
      ));
    });

    group('submitTurnActions', () {
      test('should submit turn successfully and return true', () async {
        // Arrange
        final dto = SubmitTurnDto(
          gameMatchId: 'match123',
          playerId: 'player456',
          move: [
            TargetedAction.genId(
              action: const CardAction(
                id: 'move-action',
                name: 'Move',
                type: ActionType.move,
              ),
              subjectCardId: 'card1',
            ),
          ],
          turnNumber: 1,
        );

        when(() => mockApi.submitTurn(any(), any()))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.submitTurnActions(dto);

        // Assert
        expect(result, isTrue);
        verify(() => mockApi.submitTurn('match123', dto)).called(1);
        verify(() => mockLogger.info(any())).called(2); // Start and success logs
      });

      test('should handle API errors and return false', () async {
        // Arrange
        final dto = SubmitTurnDto(
          gameMatchId: 'match123',
          playerId: 'player456',
          move: [],
          turnNumber: 1,
        );

        when(() => mockApi.submitTurn(any(), any()))
            .thenThrow(Exception('API Error'));

        // Act
        final result = await repository.submitTurnActions(dto);

        // Assert
        expect(result, isFalse);
        verify(() => mockApi.submitTurn('match123', dto)).called(1);
        verify(() => mockLogger.error(any())).called(1);
      });

      test('should handle general exceptions and return false', () async {
        // Arrange
        final dto = SubmitTurnDto(
          gameMatchId: 'match123',
          playerId: 'player456',
          move: [],
          turnNumber: 1,
        );

        when(() => mockApi.submitTurn(any(), any()))
            .thenThrow(StateError('General error'));

        // Act
        final result = await repository.submitTurnActions(dto);

        // Assert
        expect(result, isFalse);
        verify(() => mockLogger.error(any())).called(1);
      });
    });

    group('fetchGameState', () {
      test('should fetch game state successfully', () async {
        // Arrange
        const matchId = 'match123';
        final mockMatch = createTestGameMatch(id: matchId);
        
        when(() => mockApi.getMatch(matchId))
            .thenAnswer((_) async => mockMatch);

        // Act
        final result = await repository.fetchGameState(matchId);

        // Assert
        expect(result, isNotNull);
        expect(result!['id'], equals(matchId));
        verify(() => mockApi.getMatch(matchId)).called(1);
      });

      test('should return null when API throws error', () async {
        // Arrange
        const matchId = 'match123';
        
        when(() => mockApi.getMatch(matchId))
            .thenThrow(Exception('Match not found'));

        // Act
        final result = await repository.fetchGameState(matchId);

        // Assert
        expect(result, isNull);
        verify(() => mockApi.getMatch(matchId)).called(1);
        verify(() => mockLogger.error(any())).called(1);
      });
    });

    group('createMatch', () {
      test('should create match successfully', () async {
        // Arrange
        const dto = CreateMatchDto(
          gameTypeId: 'liberator',
          creatorId: 'player123',
          gameName: 'Test Match',
        );
        final expectedMatch = createTestGameMatch(
          id: 'newmatch',
          gameName: 'Test Match',
        );

        when(() => mockApi.createMatch(dto))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await repository.createMatch(dto);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result!.gameName, equals('Test Match'));
        verify(() => mockApi.createMatch(dto)).called(1);
        verify(() => mockLogger.info(any())).called(greaterThan(0));
      });

      test('should return null when API throws error', () async {
        // Arrange
        const dto = CreateMatchDto(
          gameTypeId: 'liberator',
          creatorId: 'player123',
        );

        when(() => mockApi.createMatch(dto))
            .thenThrow(Exception('Creation failed'));

        // Act
        final result = await repository.createMatch(dto);

        // Assert
        expect(result, isNull);
        verify(() => mockApi.createMatch(dto)).called(1);
        verify(() => mockLogger.error(any())).called(1);
      });
    });

    group('fetchOpenMatches', () {
      test('should fetch open matches successfully', () async {
        // Arrange
        final expectedMatches = <GameMatch>[
          createTestGameMatch(id: 'match1'),
          createTestGameMatch(id: 'match2'),
        ];

        when(() => mockApi.getOpenMatches())
            .thenAnswer((_) async => expectedMatches);

        // Act
        final result = await repository.fetchOpenMatches();

        // Assert
        expect(result, equals(expectedMatches));
        expect(result.length, equals(2));
        verify(() => mockApi.getOpenMatches()).called(1);
        verify(() => mockLogger.info(any())).called(1);
      });

      test('should return empty list when API throws error', () async {
        // Arrange
        when(() => mockApi.getOpenMatches())
            .thenThrow(Exception('Network error'));

        // Act
        final result = await repository.fetchOpenMatches();

        // Assert
        expect(result, isEmpty);
        verify(() => mockApi.getOpenMatches()).called(1);
        verify(() => mockLogger.error(any())).called(1);
      });

      test('should handle gameName parameter', () async {
        // Arrange
        final expectedMatches = <GameMatch>[createTestGameMatch(id: 'match1')];
        when(() => mockApi.getOpenMatches())
            .thenAnswer((_) async => expectedMatches);

        // Act
        final result = await repository.fetchOpenMatches('Test Game');

        // Assert
        expect(result, equals(expectedMatches));
        verify(() => mockApi.getOpenMatches()).called(1);
      });
    });

    group('joinMatch', () {
      test('should join match successfully and return true', () async {
        // Arrange
        const matchId = 'match123';
        const playerId = 'player456';
        final expectedMatch = createTestGameMatch(id: matchId);

        when(() => mockApi.joinMatch(matchId, any()))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await repository.joinMatch(matchId, playerId);

        // Assert
        expect(result, isTrue);
        verify(() => mockApi.joinMatch(matchId, any())).called(1);
        verify(() => mockLogger.info(any())).called(1);
      });

      test('should return false when API throws error', () async {
        // Arrange
        const matchId = 'match123';
        const playerId = 'player456';

        when(() => mockApi.joinMatch(matchId, any()))
            .thenThrow(Exception('Join failed'));

        // Act
        final result = await repository.joinMatch(matchId, playerId);

        // Assert
        expect(result, isFalse);
        verify(() => mockApi.joinMatch(matchId, any())).called(1);
        verify(() => mockLogger.error(any())).called(1);
      });
    });

    group('leaveMatch', () {
      test('should leave match successfully and return true', () async {
        // Arrange
        const matchId = 'match123';
        const playerId = 'player456';
        final expectedMatch = createTestGameMatch(id: matchId);

        when(() => mockApi.leaveMatch(matchId, any()))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await repository.leaveMatch(matchId, playerId);

        // Assert
        expect(result, isTrue);
        verify(() => mockApi.leaveMatch(matchId, any())).called(1);
        verify(() => mockLogger.info(any())).called(1);
      });

      test('should return false when API throws error', () async {
        // Arrange
        const matchId = 'match123';
        const playerId = 'player456';

        when(() => mockApi.leaveMatch(matchId, any()))
            .thenThrow(Exception('Leave failed'));

        // Act
        final result = await repository.leaveMatch(matchId, playerId);

        // Assert
        expect(result, isFalse);
        verify(() => mockApi.leaveMatch(matchId, any())).called(1);
        verify(() => mockLogger.error(any())).called(1);
      });
    });

    group('testConnection', () {
      test('should test connection successfully', () async {
        // Arrange
        final mockMatches = <GameMatch>[createTestGameMatch(id: 'match1')];
        when(() => mockApi.getOpenMatches())
            .thenAnswer((_) async => mockMatches);

        // Act & Assert - should not throw
        await expectLater(
          () => repository.testConnection(),
          returnsNormally,
        );

        verify(() => mockApi.getOpenMatches()).called(1);
        verify(() => mockLogger.info(any())).called(1);
      });

      test('should rethrow API errors', () async {
        // Arrange
        when(() => mockApi.getOpenMatches())
            .thenThrow(Exception('Connection failed'));

        // Act & Assert
        await expectLater(
          () => repository.testConnection(),
          throwsException,
        );

        verify(() => mockApi.getOpenMatches()).called(1);
        verify(() => mockLogger.error(any())).called(1);
      });
    });

    group('updateMatchPlayerSlots', () {
      test('should handle update player slots operation', () async {
        // Arrange
        const matchId = 'match123';
        const updatedByPlayerId = 'player456';
        final playerSlots = <PlayerSlot>[
          createTestPlayerSlot(playerId: 'player1'),
          createTestPlayerSlot(playerId: 'player2'),
        ];

        // Act & Assert - This method might not be fully implemented yet
        // We'll just verify it doesn't crash and logs appropriately
        try {
          await repository.updateMatchPlayerSlots(
            matchId,
            playerSlots,
            updatedByPlayerId,
          );
          // If it succeeds, verify logging
          verify(() => mockLogger.info(any())).called(greaterThan(0));
        } catch (e) {
          // If it fails, verify error logging
          verify(() => mockLogger.error(any())).called(greaterThan(0));
        }
      });
    });

    group('serverMessages stream', () {
      test('should provide access to server messages stream', () {
        // Act
        final stream = repository.serverMessages;

        // Assert
        expect(stream, isA<Stream<Map<String, dynamic>>>());
      });

      test('should be a broadcast stream', () {
        // Act
        final stream = repository.serverMessages;

        // Assert
        expect(stream.isBroadcast, isTrue);
      });
    });

    group('Edge Cases', () {
      test('should handle empty match ID', () async {
        // Arrange
        const emptyMatchId = '';
        when(() => mockApi.getMatch(emptyMatchId))
            .thenThrow(Exception('Invalid match ID'));

        // Act
        final result = await repository.fetchGameState(emptyMatchId);

        // Assert
        expect(result, isNull);
        verify(() => mockLogger.error(any())).called(1);
      });

      test('should handle null game name in fetchOpenMatches', () async {
        // Arrange
        final expectedMatches = <GameMatch>[createTestGameMatch(id: 'match1')];
        when(() => mockApi.getOpenMatches())
            .thenAnswer((_) async => expectedMatches);

        // Act
        final result = await repository.fetchOpenMatches(null);

        // Assert
        expect(result, equals(expectedMatches));
        verify(() => mockApi.getOpenMatches()).called(1);
      });

      test('should handle empty player slots list', () async {
        // Arrange
        const matchId = 'match123';
        const updatedByPlayerId = 'player456';
        final emptyPlayerSlots = <PlayerSlot>[];

        // Act & Assert - This method might not be fully implemented yet
        try {
          await repository.updateMatchPlayerSlots(
            matchId,
            emptyPlayerSlots,
            updatedByPlayerId,
          );
          // If it succeeds, verify logging
          verify(() => mockLogger.info(any())).called(greaterThan(0));
        } catch (e) {
          // If it fails, verify error logging
          verify(() => mockLogger.error(any())).called(greaterThan(0));
        }
      });

      test('should handle complex TargetedAction in submitTurnActions', () async {
        // Arrange
        final dto = SubmitTurnDto(
          gameMatchId: 'match123',
          playerId: 'player456',
          move: [
            TargetedAction.genId(
              action: const CardAction(
                id: 'complex-move-action',
                name: 'Complex Move',
                type: ActionType.move,
              ),
              subjectCardId: 'card1',
              objectCardId: 'card2',
              reconciledAttributes: {'strength': 10, 'health': 5},
              remainingTurns: 3,
            ),
          ],
          turnNumber: 1,
          metadata: {'special': true, 'timestamp': '2023-01-01'},
        );

        when(() => mockApi.submitTurn(any(), any()))
            .thenAnswer((_) async => {});

        // Act
        final result = await repository.submitTurnActions(dto);

        // Assert
        expect(result, isTrue);
        verify(() => mockApi.submitTurn('match123', dto)).called(1);
      });
    });
  });
}
