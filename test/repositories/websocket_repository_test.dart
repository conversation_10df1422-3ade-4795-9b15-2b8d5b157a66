import 'dart:async';

import 'package:common/models/game_match.dart';
import 'package:dauntless/repositories/websocket_repository.dart';
import 'package:test/test.dart';

void main() {
  group('WebSocketRepository', () {
    late WebSocketRepository repository;

    setUp(() {
      repository = WebSocketRepository();
    });

    tearDown(() {
      try {
        repository.dispose();
      } catch (e) {
        // Ignore disposal errors in tests
      }
    });

    group('Constructor and Initial State', () {
      test('should initialize with disconnected state', () {
        expect(repository.isConnected, isFalse);
      });

      test('should provide connection status stream', () {
        expect(repository.connectionStatus, isA<Stream<bool>>());
        expect(repository.connectionStatusUpdates, isA<Stream<bool>>());
      });

      test('should provide match updates stream', () {
        expect(repository.matchUpdates, isA<Stream<GameMatch>>());
      });

      test('should not be subscribed to any topics initially', () {
        expect(repository.isSubscribedToOpenMatches, isFalse);
        expect(repository.isSubscribedToTopic('any_topic'), isFalse);
      });
    });

    group('Topic Management', () {
      test('should create topic stream when requested', () {
        final stream = repository.getTopicStream('test_topic');
        expect(stream, isA<Stream<dynamic>>());
      });

      test('should return different streams for different topics', () {
        final stream1 = repository.getTopicStream('topic1');
        final stream2 = repository.getTopicStream('topic2');
        expect(identical(stream1, stream2), isFalse);
      });

      test('should handle topic stream creation errors gracefully', () {
        // This test ensures the fallback mechanism works
        final stream = repository.getTopicStream('error_topic');
        expect(stream, isA<Stream<dynamic>>());
      });

      test('should track subscription status correctly', () {
        expect(repository.isSubscribedToTopic('test_topic'), isFalse);

        // Note: We can't easily test the actual subscription without mocking WebSocket
        // but we can test the tracking logic
      });
    });

    group('Connection Management', () {
      test('should handle disconnect gracefully', () {
        // Act
        repository.disconnect();

        // Assert
        expect(repository.isConnected, isFalse);
      });

      test('should maintain disconnected state initially', () {
        expect(repository.isConnected, isFalse);
      });
    });

    group('Message Handling', () {
      test('should handle sending messages when disconnected', () {
        // Arrange
        final message = {'type': 'test', 'data': 'test_data'};

        // Act & Assert
        expect(() => repository.sendMessage(message), returnsNormally);
      });

      test('should handle various message types', () {
        // Test different message structures
        expect(() => repository.sendMessage({'type': 'subscribe', 'topic': 'test'}), returnsNormally);
        expect(() => repository.sendMessage({'type': 'unsubscribe', 'topic': 'test'}), returnsNormally);
        expect(() => repository.sendMessage({'type': 'custom', 'data': {'nested': 'value'}}), returnsNormally);
      });
    });

    group('Match Factory Registration', () {
      test('should register match factory successfully', () {
        // Arrange
        GameMatch testFactory(Map<String, dynamic> data) {
          return GameMatch(
            id: data['id'] ?? 'test-id',
            gameTypeId: data['gameTypeId'] ?? 'test-game',
            creatorId: data['creatorId'] ?? 'test-creator',
            createdAt: DateTime.now().millisecondsSinceEpoch,
            updatedAt: DateTime.now().millisecondsSinceEpoch,
          );
        }

        // Act & Assert
        expect(() => repository.registerMatchFactory('test_match', testFactory), returnsNormally);
      });

      test('should handle multiple factory registrations', () {
        // Arrange
        GameMatch factory1(Map<String, dynamic> data) => GameMatch(
          id: 'factory1',
          gameTypeId: 'test-game',
          creatorId: 'test-creator',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        GameMatch factory2(Map<String, dynamic> data) => GameMatch(
          id: 'factory2',
          gameTypeId: 'test-game',
          creatorId: 'test-creator',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        // Act & Assert
        expect(() {
          repository.registerMatchFactory('type1', factory1);
          repository.registerMatchFactory('type2', factory2);
        }, returnsNormally);
      });
    });

    group('Subscription Management', () {
      test('should handle topic unsubscription gracefully', () {
        // Act & Assert
        expect(() => repository.unsubscribeFromTopic('test_topic'), returnsNormally);
      });

      test('should handle unsubscribing from non-subscribed topic', () {
        // Act & Assert
        expect(() => repository.unsubscribeFromTopic('non_existent_topic'), returnsNormally);
      });

      test('should track subscription state correctly', () {
        // Initially not subscribed
        expect(repository.isSubscribedToTopic('test_topic'), isFalse);
        expect(repository.isSubscribedToOpenMatches, isFalse);
      });
    });

    group('Error Handling', () {
      test('should handle WebSocket errors gracefully', () {
        // Test the global error handler
        expect(() {
          handlePotentialWebSocketError(() {
            throw Exception('Test error');
          });
        }, returnsNormally);
      });

      test('should handle socket exception guard without errors', () async {
        // Test the socket exception guard works normally
        var result = '';
        await runWithSocketExceptionGuard(() async {
          result = 'success';
        });
        expect(result, equals('success'));
      });
    });

    group('Resource Management', () {
      test('should handle disposal gracefully', () {
        // Arrange
        repository.getTopicStream('test_topic');

        // Act & Assert - Should not throw
        expect(() => repository.dispose(), returnsNormally);
      });

      test('should handle multiple topic streams before disposal', () {
        // Arrange
        repository.getTopicStream('topic1');
        repository.getTopicStream('topic2');
        repository.getTopicStream('topic3');

        // Act & Assert - Should not throw
        expect(() => repository.dispose(), returnsNormally);
      });
    });

    group('Integration Scenarios', () {
      test('should handle basic workflow without errors', () {
        // Arrange
        final topic = 'integration_test_topic';

        // Act & Assert - Basic workflow should not throw
        expect(() {
          final stream = repository.getTopicStream(topic);
          expect(stream, isNotNull);

          repository.sendMessage({'type': 'test', 'topic': topic});

          repository.unsubscribeFromTopic(topic);

          repository.disconnect();
        }, returnsNormally);
      });

      test('should handle multiple topic operations', () {
        // Act & Assert
        expect(() {
          repository.getTopicStream('topic1');
          repository.getTopicStream('topic2');
          repository.sendMessage({'type': 'test1'});
          repository.sendMessage({'type': 'test2'});
          repository.unsubscribeFromTopic('topic1');
          repository.unsubscribeFromTopic('topic2');
        }, returnsNormally);
      });
    });
  });
}
