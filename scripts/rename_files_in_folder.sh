# shell script to rename all files in a folder with a base string and a counter

# Usage: ./rename_files_in_folder.sh <folder_path> <file_base>

# Check if the number of arguments is less than 2

if [ $# -lt 2 ]; then
    echo "Usage: ./rename_files_in_folder.sh <folder_path> <file_base>"
    exit 1
fi

# Check if the folder exists

if [ ! -d $1 ]; then
    echo "Folder does not exist"
    exit 1
fi

# Change to the folder

cd "$1" || exit

# Get the file base

file_base=$2
counter=0

# Loop through all files in the folder
for file in *; do
    # Check if the file is a regular file
    if [ -f "$file" ]; then
        # Increment the counter
        counter=$((counter+1))
        # Get the file extension
        extension="${file##*.}"
        # Rename the file
        mv "$file" "$file_base"$counter."$extension"
    fi
done

echo "Files renamed successfully"
exit 0