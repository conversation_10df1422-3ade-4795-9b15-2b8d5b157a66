import 'dart:io';

/// A helper script to ensure build_runner is always run with the correct flags
Future<void> main(List<String> args) async {
  final isWatch = args.contains('--watch');
  final command = isWatch ? 'watch' : 'build';
  
  print('Running build_runner with class-modifiers experiment flag...');
  
  final result = await Process.run(
    'flutter',
    [
      'packages',
      'pub',
      'run',
      '--enable-experiment=class-modifiers',
      'build_runner',
      command,
      '--delete-conflicting-outputs',
    ],
    runInShell: true,
  );
  
  stdout.write(result.stdout);
  stderr.write(result.stderr);
  
  exit(result.exitCode);
}
