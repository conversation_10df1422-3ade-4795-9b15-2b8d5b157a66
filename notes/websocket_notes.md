# WebSocket Architecture Notes

## Current Architecture

The current WebSocket implementation uses a layered approach:
- `WebSocketRepository` handles the core WebSocket connection and message parsing
- `serverNotificationsUseCase` provides an interface to the repository
- `WebSocketManager` (bloc) manages state and exposes streams

This foundation is solid but needs enhancements to support different types of subscriptions beyond just open matches.

## Recommended Approach: Topic-Based Subscription System

### 1. Expand the Subscription System

The code already has the basics for topics with:
```dart
final message = jsonEncode({
  'type': 'subscribe',
  'topic': 'open_matches',
});
```

Expand this to support multiple topics with different callbacks:

```dart
// In WebSocketRepository
final Map<String, StreamController> _topicControllers = {};
final Set<String> _activeSubscriptions = {};

Future<void> subscribeToTopic(String topic) async {
  if (!_isConnected) await connect();
  
  if (_isConnected && !_activeSubscriptions.contains(topic)) {
    _activeSubscriptions.add(topic);
    
    // Create a controller if one doesn't exist
    _topicControllers[topic] ??= StreamController.broadcast();
    
    // Send subscribe message
    final message = jsonEncode({
      'type': 'subscribe',
      'topic': topic,
    });
    
    _channel?.sink.add(message);
    print('Subscribed to $topic');
  }
}

void unsubscribeFromTopic(String topic) {
  if (_isConnected && _activeSubscriptions.contains(topic)) {
    // Send unsubscribe message
    final message = jsonEncode({
      'type': 'unsubscribe',
      'topic': topic,
    });
    
    _channel?.sink.add(message);
    _activeSubscriptions.remove(topic);
    print('Unsubscribed from $topic');
  }
}

// Get stream for a specific topic
Stream<dynamic> getTopicStream(String topic) {
  _topicControllers[topic] ??= StreamController.broadcast();
  return _topicControllers[topic].stream;
}
```

### 2. Enhanced Message Handling

Modify the `_handleMessage` method to distribute messages to the appropriate topic controllers:

```dart
void _handleMessage(dynamic message) {
  if (message is! String) return;

  try {
    final data = jsonDecode(message) as Map<String, dynamic>;
    final type = data['type'] as String?;
    final topic = data['topic'] as String?;
    final messageData = data['data'] as Map<String, dynamic>?;

    if (messageData == null) return;

    // Handle different message types
    if (topic != null && _topicControllers.containsKey(topic)) {
      // Send the message to the appropriate topic controller
      _topicControllers[topic]!.add(messageData);
    } else if (type == 'match_update') {
      // Legacy handler for match updates
      // ...
    } else if (type == 'open_matches') {
      // Legacy handler for open matches
      // ...
    }
  } catch (e) {
    print('Error parsing WebSocket message: $e');
  }
}
```

### 3. Specialized Topic Streams in Use Cases and Managers

Create type-safe wrappers for topic streams:

```dart
// In serverNotificationsUseCase
Stream<List<GameMatch>> get openMatchesUpdates => 
    _repository.getTopicStream('open_matches')
        .map((data) => (data['matches'] as List<dynamic>)
            .map((match) => GameMatch.fromJson(match))
            .toList());

Stream<MatchAction> get matchActionsStream => 
    _repository.getTopicStream('match_actions')
        .map((data) => MatchAction.fromJson(data));
        
Future<void> subscribeToMatchActions(String matchId) async {
  await _repository.subscribeToTopic('match_actions:$matchId');
}

Future<void> sendMatchAction(String matchId, MatchAction action) async {
  final message = jsonEncode({
    'type': 'match_action',
    'match_id': matchId,
    'data': action.toJson(),
  });
  
  await _repository.sendMessage(message);
}
```

### 4. Implement Send Action Methods

Add methods to send actions from client to server:

```dart
// In WebSocketRepository
Future<void> sendMessage(String message) async {
  if (!_isConnected) await connect();
  
  if (_isConnected) {
    _channel?.sink.add(message);
  } else {
    throw Exception('Cannot send message - WebSocket not connected');
  }
}
```

## Benefits of This Approach

1. **Modular**: Each topic is handled separately but consistently
2. **Scalable**: Easy to add new topics/subscriptions
3. **Type-safe**: Streams can be transformed into specific object types
4. **Maintainable**: Clear separation between subscription and message handling

## Example Usage for Match Events

```dart
// Subscribe to a specific match's actions
await serverNotificationsUseCase.subscribeToTopic('match:${matchId}:actions');

// Listen for actions on that match
serverNotificationsUseCase.getTopicStream('match:${matchId}:actions')
    .map((data) => MatchAction.fromJson(data))
    .listen((action) {
      // Handle the action
      print('Received action: ${action.type} for match ${action.matchId}');
    });

// Send an action for a match
await serverNotificationsUseCase.sendMessage(jsonEncode({
  'type': 'match_action',
  'topic': 'match:${matchId}:actions',
  'data': {
    'actionType': 'submit_turn',
    'turnData': { /* turn data */ }
  }
}));
```

## Subscription Naming Conventions

Recommended topic naming convention:

- Global topics: `resource_name` (e.g., `open_matches`)
- Resource-specific topics: `resource:id:action` (e.g., `match:abc123:actions`)
- User-specific topics: `user:id:resource` (e.g., `user:xyz789:notifications`)

This allows for both broad subscriptions and targeted ones.

## Error Handling and Reconnection

The current reconnection logic is good. Consider adding:

1. Automatic resubscription to topics after reconnection
2. Buffering of unsent messages during disconnection periods
3. Notification to UI components about connection status changes

## Next Steps

1. Implement the topic-based subscription system
2. Define concrete message formats for match actions
3. Update the WebSocketManager to expose appropriate streams
4. Consider adding authentication tokens to WebSocket connections
5. Add logging for easier debugging
