# Setting Up Your MacBook Pro as a Server

## 1. System Preparation

- **Clean installation**: Consider a fresh macOS install for optimal performance
- **System updates**: Make sure macOS is updated to the latest compatible version
- **Power settings**: Configure your Mac to never sleep and restart automatically after power failures
  - System Settings → Battery/Energy Saver → "Prevent computer from sleeping automatically"
- **Set up automatic login**: Ensures server restarts without manual intervention

## 2. Network Configuration

- **Static IP address**: Configure a static local IP to ensure consistent addressing
  - System Settings → Network → Advanced → TCP/IP → Configure IPv4: Manually
- **Port forwarding**: Configure your router to forward necessary ports to your Mac
- **Dynamic DNS**: If you don't have a static public IP, set up a DDNS service (like no-ip.com or dyndns.org)
- my.noip.com; google: <EMAIL> login
  - dauntless.servegame.com

SSH:
ssh palpatine@************ -p 1138
ssh <EMAIL> -p 1138

## 3. Server Software

- **Install server management tools**:
  - Homebrew: `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`
  - Docker: `brew install --cask docker` (for containerization)

- **For your Dart/Flutter game server**:
  - Ensure Dart SDK is properly installed
  - Create startup scripts that include your `--enable-experiment=class-modifiers` flag

## 4. Continuous Operation

- **Launch agents**: Use macOS launchd to keep services running
  - Create plist files in `~/Library/LaunchAgents/` for user-level services

- **Process monitoring**: Consider tools like PM2 or supervisord

## 5. Security Considerations

- **Firewall**: Enable the built-in macOS firewall (System Settings → Network → Firewall)
- **User accounts**: Create separate accounts for services with limited permissions
- **Regular backups**: Implement a backup strategy for your server data

## 6. Performance Optimization

- **Disable unnecessary services**: Turn off features you don't need
- **Monitor resources**: Install monitoring tools like `htop` (`brew install htop`)

# Setting Up Remote Access for Your MacBook Server

## 1. Enable SSH Access (for administrative tasks)

```bash
# Enable SSH in System Settings
System Settings → Sharing → Remote Login
```

This allows you to connect to your Mac's terminal from anywhere using:
```bash
ssh username@your-ip-address
```

## 2. Network Configuration

### Set Up Port Forwarding on Your Router
1. Access your router's admin panel (typically *********** or similar)
2. Find "Port Forwarding" settings
3. Forward necessary ports to your Mac's internal IP:
   - SSH: Port 22
   - Your game server port(s)
   - Any other services you want accessible

### Get a Static Public IP or Dynamic DNS

If you don't have a static public IP (most home connections don't):
1. Sign up for a free Dynamic DNS service (like No-IP, DuckDNS, or Dynu)
2. Install their client on your Mac to keep your domain pointing to your changing IP
3. Use a command like this to install a DDNS client:

```bash
brew install ddclient
```

## 3. Security Considerations (Very Important)

### Strengthen SSH
```bash
# Edit SSH config
sudo nano /etc/ssh/sshd_config

# Make these changes:
# - Change default port (not 22)
# - Disable password authentication
# - Enable key-based authentication only
# - Disable root login
```

### Set Up SSH Keys
```bash
# On your client machine:
ssh-keygen -t ed25519
ssh-copy-id -i ~/.ssh/id_ed25519.pub username@server-ip
```

### Use a Firewall
```bash
# Enable and configure macOS firewall
sudo pfctl -e
```

## 4. Remote Access Options

### VPN for Secure Access
Consider setting up a VPN server for secure access to all services:
```bash
brew install wireguard-tools
```

### Web-Based Admin Interfaces
For easier administration, consider installing:
- Cockpit (`brew install cockpit`)
- Webmin

## 5. Testing Remote Access

1. Test from outside your network (mobile data)
2. Use: `ssh username@your-dynamic-dns-address`
3. Verify your services are accessible



# Connecting to a Dart Frog Server on Your MacBook

To connect to your Dart Frog development server running on your MacBook from a remote client, you'll need to follow these steps:

## 1. Ensure Your Dart Frog Server Is Properly Configured

By default, a Dart Frog dev server listens only on localhost (127.0.0.1), which means it's not accessible from external networks. You need to:

```bash
# Run Dart Frog on all interfaces (0.0.0.0) instead of just localhost
dart_frog dev --hostname 0.0.0.0 --port 8080
```

The port number (8080 in this example) can be any port you prefer, but make sure it's not being used by another service.

## 2. Configure Port Forwarding on Your Router

You'll need to:
- Set up port forwarding in your router to forward external requests to your MacBook's internal IP
- Forward the port your Dart Frog server is using (e.g., 8080)

## 3. Configure MacBook Firewall

Make sure your MacBook's firewall allows incoming connections on your Dart Frog server port:
- Go to System Preferences → Security & Privacy → Firewall
- Click "Firewall Options" and ensure the Dart Frog application is allowed

## 4. Connect from Your Remote App

Now you can connect to your server using:

```
http://dauntless.servegame.com:8080
```

Replace 8080 with whatever port your Dart Frog server is running on.

## Additional Considerations

1. **Security**: Running a dev server is not recommended for production. Consider:
   - Setting up HTTPS with a proper certificate
   - Implementing authentication mechanisms

2. **Production Deployment**: For a proper setup, consider:
   - Using a reverse proxy like Nginx
   - Running your Dart Frog app in production mode
   - Setting up proper TLS/SSL certificates

3. **Persistent Running**: To keep your server running when you close your terminal, consider using tools like:
   - `screen` or `tmux`
   - A service manager like `systemd` or `launchd` (macOS)