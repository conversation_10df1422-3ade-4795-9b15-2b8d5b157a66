This is a common issue when a Mac loses internet connectivity. The problem is likely that the DDNS client or network services aren't automatically reconnecting properly after an outage. Here are several solutions to fix this:

## 1. **Check and Fix DDNS Client Auto-Start**

First, ensure your DDNS client starts automatically and runs continuously:

**If using a built-in DDNS client:**
- Make sure it's configured to start on boot
- Consider switching to a more reliable command-line client

**If using a third-party client, create a LaunchDaemon:**

Create `/Library/LaunchDaemons/com.ddns.updater.plist`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.ddns.updater</string>
    <key>ProgramArguments</key>
    <array>
        <string>/path/to/your/ddns/client</string>
        <string>--config</string>
        <string>/path/to/config</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardErrorPath</key>
    <string>/var/log/ddns.log</string>
</dict>
</plist>
```

## 2. **Create a Network Recovery Script**

Create a script that monitors and restarts network services:

Create `/usr/local/bin/network-recovery.sh`:
```bash
#!/bin/bash

# Wait for network to be available
while ! ping -c 1 ******* &> /dev/null; do
    echo "$(date): Waiting for network..."
    sleep 30
done

echo "$(date): Network detected, restarting services..."

# Restart DDNS client (adjust for your specific client)
sudo pkill -f "your-ddns-client"
sleep 5
sudo /path/to/your/ddns/client &

# Restart SSH (if needed)
sudo launchctl unload /System/Library/LaunchDaemons/ssh.plist
sudo launchctl load /System/Library/LaunchDaemons/ssh.plist

echo "$(date): Services restarted"
```

Make it executable:
```bash
sudo chmod +x /usr/local/bin/network-recovery.sh
```

## 3. **Create a LaunchDaemon for the Recovery Script**

Create `/Library/LaunchDaemons/com.network.recovery.plist`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.network.recovery</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/local/bin/network-recovery.sh</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>WatchPaths</key>
    <array>
        <string>/Library/Preferences/SystemConfiguration</string>
    </array>
    <key>StandardErrorPath</key>
    <string>/var/log/network-recovery.log</string>
    <key>StandardOutPath</key>
    <string>/var/log/network-recovery.log</string>
</dict>
</plist>
```

Load it:
```bash
sudo launchctl load /Library/LaunchDaemons/com.network.recovery.plist
```

## 4. **Alternative: Use ddclient with Cron**

Install `ddclient` via Homebrew and set up a cron job:

```bash
# Install ddclient
brew install ddclient

# Edit root's crontab
sudo crontab -e

# Add this line to check/update every 5 minutes
*/5 * * * * /usr/local/bin/ddclient -daemon=0 -syslog -noquiet
```

## 5. **Ensure SSH Starts Automatically**

Verify SSH is enabled in System Preferences:
- Go to System Preferences → Sharing
- Ensure "Remote Login" is checked
- Or via command line: `sudo systemsetup -setremotelogin on`

## 6. **Network Location Reset (if needed)**

Sometimes the network location gets confused. Create a script to reset it:

```bash
#!/bin/bash
# Reset network location
sudo networksetup -switchtolocation Automatic
```

## 7. **Monitor and Test**

Create a simple monitoring script to test connectivity:

```bash
#!/bin/bash
# Test script - run manually to verify everything works
echo "Testing external connectivity..."
curl -s http://checkip.amazonaws.com
echo "Testing DDNS resolution..."
nslookup your-ddns-hostname.com
echo "Testing SSH service..."
sudo launchctl list | grep ssh
```

## Quick Troubleshooting Steps:

1. **Check what's running:** `sudo launchctl list | grep -E "(ssh|ddns)"`
2. **Check logs:** `tail -f /var/log/system.log`
3. **Test DDNS manually:** Run your DDNS client with verbose logging
4. **Verify network services:** `networksetup -listallnetworkservices`

The most likely culprit is that your DDNS client isn't automatically restarting after the network comes back up, so your external IP address isn't being updated with the DDNS service. The LaunchDaemon approach should solve this by ensuring services restart automatically when the network recovers.