Network Architecture

### Game Network Flow
1. Client connects to server: websocket
2. <PERSON><PERSON> creates match: server request
3. Server creates match with 'open' status
4. Other client players join match: server request
5. Server updates match to 'closed' status
6. Match host client starts match: server request
7. Server updates match to 'in_progress' status
8. Server updates all clients in match with 'in_progress' status
9. Match host clients make turns
10. When all turns are submitted, server sends turns to host client for processing
11. Host client processes turns
12. Host client sends processed turns to server
13. Server updates match with processed turns
14. Server updates all clients in match with processed turns
15. Next turn begins

## WebSocket Protocol

### Connection Management
1. Client attempts to connect to WebSocket server (`ws://dauntless.servegame.com:2187/ws`)
2. If connection succeeds, client enters connected state and listens for messages
3. If connection fails or times out (10 seconds), client schedules reconnection with exponential backoff
4. Client monitors connection and automatically attempts reconnection when disconnected
5. After multiple failed attempts, client may fall back to HTTP polling (if implemented)

### Message Format
```json
{
  "type": "message_type",
  "data": { /* message-specific data */ }
}
```

Common message types:
- `match_update`: Updates about match state changes
- `subscribe_match`: Request to subscribe to a specific match
- `unsubscribe_match`: Request to unsubscribe from a match

## Game Match Flow

1. **Connection Establishment**
   - Client connects to server via WebSocket
   - Client receives connection status updates

2. **Match Creation**
   - Client sends match creation request
   - Server creates match with 'open' status
   - Server broadcasts match creation to subscribed clients

3. **Match Joining**
   - Clients discover open matches through match listing
   - Client sends join match request
   - Server validates join request and updates match
   - Server broadcasts updated match data to all subscribed clients
   - Client who joined subscribes to match updates (`subscribeToMatch(matchId)`)

4. **Match Preparation**
   - When enough players join, host can close match enrollment
   - Server updates match to 'closed' status
   - Server broadcasts updated status to all subscribed clients

5. **Match Start**
   - Host client sends start match request
   - Server validates request and updates match to 'in_progress'
   - Server broadcasts 'in_progress' status to all subscribed clients

6. **Turn Processing**
   - Each client submits their turn actions
   - Server collects turn submissions from all players
   - When all turns are submitted, server sends batch to host for processing
   - Host processes turns and computes game state update
   - Host sends processed turn results back to server
   - Server validates and updates match with new state
   - Server broadcasts processed turns to all clients in match
   - Next turn begins

7. **Match Conclusion**
   - When victory/defeat conditions are met, host client notifies server
   - Server updates match to 'completed' status
   - Server broadcasts match results to all subscribed clients
   - Clients unsubscribe from match updates

8. **Error Handling**
   - Connection lost during match: client attempts reconnection
   - Player disconnection: server holds match state for reconnection window
   - Timeout on turn submission: implement configurable fallback behavior
   - Host disconnection: potential host migration or match suspension

## Implementation Notes

- WebSocket reconnection uses exponential backoff (5s, 10s, 15s... up to 60s)
- Match data is typed and deserialized using registered factory functions
- Clients maintain active subscriptions to matches they're participating in
- Heartbeat messages may be implemented to detect connection health
