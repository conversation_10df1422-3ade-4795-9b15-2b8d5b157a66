# Dauntless Match Management Architecture

This document outlines the architecture and flow for the match management system, focusing on match creation, WebSocket communication, and client-server interactions.

## System Overview

The Dauntless match management system uses a client-server architecture with real-time WebSocket communication for updates. This allows clients to see new matches, join existing matches, and receive updates without polling or refreshing.

```
┌────────────────┐                                  ┌────────────────┐
│                │                                  │                │
│    Client      │◄──── WebSocket Connection ─────►│    Server      │
│  (Flutter App) │                                  │  (Dart Server) │
│                │                                  │                │
└────────────────┘                                  └────────────────┘
```

## Key Components

### Client-Side Components

1. **CreateMatchBloc**
   - Manages the UI state for match creation
   - Handles player slot configuration
   - Creates matches via the MatchUseCase

2. **MatchSelectionBloc**
   - Handles the list of open matches
   - Subscribes to WebSocket for match updates
   - Updates the UI when matches change

3. **WebSocketClient**
   - Manages the WebSocket connection to the server
   - Handles message serialization/deserialization
   - Routes messages to appropriate handlers

### Server-Side Components

1. **WebSocketManager**
   - Maintains active client connections
   - Manages topic subscriptions
   - Broadcasts updates to subscribed clients

2. **GameMatchRepository**
   - Stores and retrieves match data
   - Handles match creation and updates
   - Triggers WebSocket broadcasts when matches change

3. **WebSocket Route Handlers**
   - Process incoming WebSocket messages
   - Route requests to appropriate services
   - Send responses back to clients

## Match Creation Flow

```
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│            │     │            │     │            │     │            │
│   Create   │────►│  Match     │────►│  Game      │────►│  WebSocket │
│  Match UI  │     │  Use Case  │     │  Match     │     │  Manager   │
│            │     │            │     │  Repository│     │            │
└────────────┘     └────────────┘     └────────────┘     └────────────┘
                                                                 │
                                                                 ▼
┌────────────┐     ┌────────────┐     ┌────────────┐     ┌────────────┐
│            │     │            │     │            │     │            │
│  Match     │◄────│  Client    │◄────│  WebSocket │◄────│ Broadcast  │
│ Selection  │     │  WebSocket │     │  Message   │     │ to         │
│    UI      │     │  Client    │     │            │     │ Subscribers│
└────────────┘     └────────────┘     └────────────┘     └────────────┘
```

### Detailed Flow

1. **Match Creation**:
   - User configures a match in the Create Match UI
   - CreateMatchBloc validates inputs and creates a MatchConfig
   - CreateMatchBloc calls MatchUseCase.createMatch()
   - MatchUseCase forwards the request to the server
   - GameMatchRepository creates and stores the match
   - If "open for players" is enabled, match is marked as open for joining

2. **WebSocket Updates**:
   - GameMatchRepository calls _broadcastOpenMatchesUpdate()
   - WebSocketManager broadcasts to all subscribed clients
   - Clients receive the update and update their UI accordingly

3. **Match Joining**:
   - User selects a match from the Match Selection UI
   - MatchSelectionBloc sends a join request
   - Server processes the join request and updates the match
   - Server broadcasts the updated match state to all players in the match

## WebSocket Communication Protocol

### Subscription Messages

```json
// Client -> Server: Subscribe to open matches
{
  "type": "subscribe",
  "topic": "open_matches"
}

// Server -> Client: Subscription confirmation
{
  "type": "subscription_success",
  "topic": "open_matches"
}
```

### Update Messages

```json
// Server -> Client: Open matches update
{
  "type": "open_matches_update",
  "matches": [
    {
      "id": "match_id_1",
      "name": "Game 1 (4 players)",
      "is_open_for_joining": true,
      "status": "open",
      "player_count": 1,
      "max_players": 4
    },
    ...
  ]
}

// Server -> Client: Individual match update
{
  "type": "match_update",
  "match_id": "match_id_1",
  "match": { ... match data ... }
}
```

## Implementation Details

### CreateMatchBloc

The CreateMatchBloc handles the UI state for creating matches, with key events:

- **InitializeCreateMatchEvent**: Loads available game configurations
- **UpdatePlayerTypeEvent**: Changes a player slot's type
- **CreateMatchSubmitEvent**: Creates a match and, if enabled, opens it for joining

### WebSocketManager

The WebSocketManager maintains a list of clients and their subscriptions:

```dart
class WebSocketManager {
  final Map<String, WebSocket> _clients = {};
  final Set<String> _openMatchesSubscribers = {};
  
  void subscribeClientToOpenMatches(String clientId) {
    _openMatchesSubscribers.add(clientId);
    // Notify the client of successful subscription
  }
  
  void broadcastOpenMatchesUpdate(List<Map<String, dynamic>> matchesData) {
    final message = jsonEncode({
      'type': 'open_matches_update',
      'matches': matchesData,
    });
    
    for (final clientId in _openMatchesSubscribers) {
      final client = _clients[clientId];
      if (client != null) {
        client.sink.add(message);
      }
    }
  }
}
```

### GameMatchRepository

The GameMatchRepository manages match data and triggers WebSocket updates:

```dart
class GameMatchRepository {
  // Create a match and broadcast updates
  Future<Map<String, dynamic>> createMatch(MatchConfig config, {bool isOpenForJoining = false}) async {
    // Create match logic
    
    if (isOpenForJoining) {
      await openMatchForJoining(match['id']);
    }
    
    return match;
  }
  
  Future<Map<String, dynamic>> openMatchForJoining(String id) async {
    // Open match logic
    
    // Broadcast updates
    WebSocketManager().broadcastMatchUpdate(match['id'].toString(), match);
    _broadcastOpenMatchesUpdate();
    
    return match;
  }
  
  Future<void> _broadcastOpenMatchesUpdate() async {
    // Get all open matches and broadcast them
    WebSocketManager().broadcastOpenMatchesUpdate(openMatches);
  }
}
```

## Testing the System

1. Start the server (`dart bin/server.dart`)
2. Launch the client application
3. Create a match with "Open for players" enabled
4. Verify the match appears in the "Open Matches" list of other clients
5. Join the match from another client
6. Verify both clients receive updates when match state changes

## Best Practices

1. **Error Handling**: Implement robust error handling at all levels
2. **Logging**: Use consistent logging to track system behavior
3. **State Management**: Keep BLoCs focused on specific responsibilities
4. **WebSocket Reliability**: Handle connection drops and reconnection gracefully
5. **Security**: Validate all client inputs on the server side

## Future Improvements

1. Implement authentication for WebSocket connections
2. Add support for match spectators
3. Enhance error recovery for dropped connections
4. Optimize WebSocket message payloads for larger match data
5. Implement match result persistence and statistics
