// A simple test script to verify leave match functionality works end-to-end
// Run with: dart test_scripts/leave_match_test.dart

import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  // You may need to adjust these values based on your test environment
  final baseUrl = 'http://localhost:8080'; 
  final testMatchId = ''; // Update with a valid match ID
  final testPlayerId = ''; // Update with a valid player ID
  
  print('=== Leave Match Test ===');
  
  if (testMatchId.isEmpty || testPlayerId.isEmpty) {
    print('ERROR: Please provide valid testMatchId and testPlayerId values');
    return;
  }
  
  // Step 1: Verify the match exists
  try {
    final getMatchResponse = await http.get(
      Uri.parse('$baseUrl/matches/$testMatchId'),
    );
    
    if (getMatchResponse.statusCode != 200) {
      print('ERROR: Match not found or server error');
      print('Status code: ${getMatchResponse.statusCode}');
      print('Response: ${getMatchResponse.body}');
      return;
    }
    
    print('Match exists: $testMatchId');
    final matchData = jsonDecode(getMatchResponse.body);
    print('Match data: $matchData');
  } catch (e) {
    print('ERROR getting match: $e');
    return;
  }
  
  // Step 2: Test leave match endpoint
  try {
    final leaveMatchResponse = await http.post(
      Uri.parse('$baseUrl/matches/$testMatchId/leave'),
      headers: {
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'playerId': testPlayerId,
      }),
    );
    
    print('Leave match status code: ${leaveMatchResponse.statusCode}');
    print('Leave match response: ${leaveMatchResponse.body}');
    
    if (leaveMatchResponse.statusCode == 200) {
      print('SUCCESS: Player successfully left the match');
    } else {
      print('ERROR: Failed to leave match');
    }
  } catch (e) {
    print('ERROR leaving match: $e');
  }
  
  // Step 3: Verify player was removed from the match
  try {
    final getUpdatedMatchResponse = await http.get(
      Uri.parse('$baseUrl/matches/$testMatchId'),
    );
    
    if (getUpdatedMatchResponse.statusCode != 200) {
      print('ERROR: Match not found after leave operation');
      return;
    }
    
    final updatedMatchData = jsonDecode(getUpdatedMatchResponse.body);
    print('Updated match data: $updatedMatchData');
    
    // Check if player is no longer in the match
    final playerSlots = updatedMatchData['playerSlots'] as List;
    bool playerFound = false;
    
    for (final slot in playerSlots) {
      if (slot['playerId'] == testPlayerId) {
        playerFound = true;
        break;
      }
    }
    
    if (!playerFound) {
      print('SUCCESS: Player was properly removed from the match');
    } else {
      print('ERROR: Player still found in match after leave operation');
    }
  } catch (e) {
    print('ERROR verifying player removal: $e');
  }
  
  print('=== Test Complete ===');
}
