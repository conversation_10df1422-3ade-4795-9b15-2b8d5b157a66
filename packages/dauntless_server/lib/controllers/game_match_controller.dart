import 'package:dauntless_server/repositories/game_match_repository.dart';

/// Controller to handle game_match-related business logic
class GameMatchController {
  /// Constructor for MatchController
  GameMatchController({GameMatchRepository? matchRepository})
      : _matchRepository = matchRepository ?? GameMatchRepository();

  final GameMatchRepository _matchRepository;

  /// Get all active matches
  Future<List<Map<String, dynamic>>> getMatches() async {
    return _matchRepository.getMatches();
  }
  
  /// Get all open matches that are available for joining
  Future<List<Map<String, dynamic>>> getOpenMatches() async {
    return _matchRepository.getOpenMatches();
  }

  /// Get a specific game_match by ID
  Future<Map<String, dynamic>?> getMatch(String id) async {
    return _matchRepository.getMatch(id);
  }

  /// Create a new game_match
  Future<Map<String, dynamic>> createMatch(
      Map<String, dynamic> matchData) async {
    return _matchRepository.createMatch(matchData);
  }

  /// Join an existing game_match
  Future<Map<String, dynamic>?> joinMatch(
    String id,
    Map<String, dynamic> playerData,
  ) async {
    return _matchRepository.joinMatch(id, playerData);
  }

  /// Submit a turn for a game_match
  Future<Map<String, dynamic>?> submitTurn(
    String id,
    Map<String, dynamic> turnData,
  ) async {
    return _matchRepository.submitTurn(id, turnData);
  }

  /// Update game_match status
  Future<Map<String, dynamic>?> updateMatchStatus(
    String id,
    Map<String, dynamic> statusData,
  ) async {
    return _matchRepository.updateMatchStatus(id, statusData);
  }
  
  /// Open a match for joining by other players
  Future<Map<String, dynamic>?> openMatchForJoining(String id) async {
    return _matchRepository.openMatchForJoining(id);
  }
  
  /// Delete a match by ID
  Future<bool> deleteMatch(String id) async {
    return _matchRepository.deleteMatch(id);
  }
  
  /// Leave an existing match
  Future<Map<String, dynamic>?> leaveMatch(
    String id,
    Map<String, dynamic> playerData,
  ) async {
    return _matchRepository.leaveMatch(id, playerData);
  }
  
  /// Update the game name for a match
  Future<Map<String, dynamic>?> updateGameName(
    String id,
    Map<String, dynamic> nameData,
  ) async {
    final gameName = nameData['gameName'] as String;
    return _matchRepository.updateGameName(id, gameName);
  }

  /// Get the stream of changes to matches data
  Stream<void> get onMatchesChange => _matchRepository.onChange;
}
