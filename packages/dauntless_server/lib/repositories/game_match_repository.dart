import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:dauntless_server/services/event_bus.dart';
import 'package:dauntless_server/websockets/websocket_manager.dart';
import 'package:common/models/match_status.dart';

/// Repository to handle game_match-related data operations
class GameMatchRepository {
  /// Constructor for MatchRepository
  GameMatchRepository({File? matchesFile})
      : _matchesFile =
            matchesFile ?? File('${Directory.current.path}/data/matches.json');

  final File _matchesFile;
  // Access the WebSocketManager singleton when needed rather than storing an instance

  final _changeController = StreamController<void>.broadcast();

  /// Stream that emits when matches data changes
  Stream<void> get onChange => _changeController.stream;

  /// Get all active matches
  Future<List<Map<String, dynamic>>> getMatches() async {
    return _readMatchesData();
  }
  
  /// Get all open matches that are available for joining
  Future<List<Map<String, dynamic>>> getOpenMatches() async {
    final matches = await _readMatchesData();
    return matches.where((match) {
      // Check first if match is open
      if (match['status'] != MatchStatus.open.name) {
        return false;
      }
      
      // Check if match is open for joining using top-level flag only
      return true;
    }).toList();
  }

  /// Helper method to broadcast open matches updates to all subscribers
  Future<void> _broadcastOpenMatchesUpdate() async {
    try {
      // Fetch all matches that are open for joining
      print('_broadcastOpenMatchesUpdate: Reading matches data');
      final matches = await _readMatchesData();
      print('_broadcastOpenMatchesUpdate: Found ${matches.length} total matches');
      
      // Log each match ID and its open status for debugging
      for (var match in matches) {
        print('_broadcastOpenMatchesUpdate: Match ID: ${match['id']}, is_open_for_joining: ${match['is_open_for_joining']}, status: ${match['status']}');
      }
      
      // Check if matches have open status and are open for joining
      // Allow for null is_open_for_joining field and treat as true if status is open
      final openMatches = matches.where((match) {
        final status = match['status'] as String?;
        final isOpenForJoining = match['is_open_for_joining'] as bool?;
        
        // If is_open_for_joining is null but status is open, treat it as true
        // This ensures backward compatibility
        return (isOpenForJoining == true || (isOpenForJoining == null && status == MatchStatus.open.name)) && 
               status == MatchStatus.open.name;
      }).toList();
      
      print('_broadcastOpenMatchesUpdate: Found ${openMatches.length} open matches after filtering');
      
      // Only broadcast if we have open matches
      if (openMatches.isNotEmpty) {
        print('=== OPEN MATCHES BROADCAST ===');
        print('Broadcasting ${openMatches.length} open matches to subscribers');
        
        // Print details of each match for debugging
        for (var match in openMatches) {
          print(' - Match ID: ${match['id']}');
          print('   Game Name: ${match['gameName'] ?? 'Unknown'}');
          print('   Status: ${match['status']}, Open for joining: ${match['is_open_for_joining']}');
          
          // Print player slots to debug join status
          if (match['playerSlots'] != null) {
            print('   Player Slots:');
            final slots = match['playerSlots'] as List;
            for (var i = 0; i < slots.length; i++) {
              final slot = slots[i] as Map<String, dynamic>;
              print('     Slot $i: ${slot['id']}, playerId=${slot['playerId']}');
            }
          }
        }
        print('============================');
        
        // Publish the open matches changed event to the EventBus
        print('GameMatchRepository: Publishing open_matches_changed event');
        final event = GameEvent.openMatchesChanged(openMatches);
        EventBus().publish(event);
        
        // For backward compatibility during transition, also use the direct method
        // This can be removed once EventBus implementation is complete
        print('GameMatchRepository: Calling WebSocketManager().broadcastOpenMatchesUpdate');
        WebSocketManager().broadcastOpenMatchesUpdate(openMatches);
        print('GameMatchRepository: WebSocket broadcast completed');
      } else {
        print('_broadcastOpenMatchesUpdate: No open matches found, skipping broadcast');
      }
    } catch (e) {
      print('Error broadcasting open matches update: $e');
    }
  }

  /// Get a specific game_match by ID
  Future<Map<String, dynamic>?> getMatch(String id) async {
    try {
      final matches = await _readMatchesData();
      return matches.firstWhere(
        (match) => match['id'] == id,
        orElse: () => <String, dynamic>{},
      );
    } catch (e) {
      print('Error getting game_match: $e');
      return null;
    }
  }

  /// Create a new game_match
  Future<Map<String, dynamic>> createMatch(
      Map<String, dynamic> matchData) async {
    try {
      final matches = await _readMatchesData();

      // Add ID and timestamps to the game_match data
      final newMatch = {
        ...matchData,
        'id': _generateId(),
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
        'status': MatchStatus.open.name,
        'is_open_for_joining': true, // Explicitly set to true for new matches
      };

      matches.add(newMatch);
      await _writeMatchesData(matches);
      _changeController.add(null);

      // Publish match created event to the EventBus
      print('GameMatchRepository: Publishing match_created event');
      final createdEvent = GameEvent.matchCreated(newMatch);
      EventBus().publish(createdEvent);

      // For compatibility during transition, also use the direct method
      // This can be removed once EventBus implementation is complete
      WebSocketManager().broadcastMatchUpdate(newMatch['id'].toString(), newMatch);
      
      // Also broadcast updated open matches list to all subscribers
      _broadcastOpenMatchesUpdate();

      return newMatch;
    } catch (e) {
      print('Error creating game_match: $e');
      rethrow;
    }
  }

  /// Join an existing game_match
  Future<Map<String, dynamic>?> joinMatch(
    String id,
    Map<String, dynamic> playerData,
  ) async {
    try {
      final matches = await _readMatchesData();
      final matchIndex = matches.indexWhere((match) => match['id'] == id);

      if (matchIndex == -1) return null;

      final match = matches[matchIndex];

      // Verify game_match is open for joining
      if (match['status'] != MatchStatus.open.name) {
        throw Exception('Match is not open for joining');
      }

      // Check if playerData contains id (required for join logic)
      if (!playerData.containsKey('id') || playerData['id'] == null) {
        throw Exception('Player ID is required to join a match');
      }
      
      final playerId = playerData['id'];

      // Check if player already joined this match
      if (match['players'] != null) {
        final playersList = match['players'] as List;
        for (final playerItem in playersList) {
          final player = playerItem as Map<String, dynamic>;
          if (player['id'] == playerId) {
            // Player is already in this match, return the match without changes
            print('Player $playerId is already in match $id');
            return match;
          }
        }
      }
      
      // Remove player from any other matches they might be in
      for (var i = 0; i < matches.length; i++) {
        if (i != matchIndex && matches[i]['players'] != null) {
          final otherMatch = matches[i];
          final otherPlayers = otherMatch['players'] as List;
          
          bool playerRemoved = false;
          final updatedOtherPlayers = <Map<String, dynamic>>[];
          
          // Find and remove the player from other match
          for (final playerItem in otherPlayers) {
            final player = playerItem as Map<String, dynamic>;
            if (player['id'] != playerId) {
              updatedOtherPlayers.add(player);
            } else {
              playerRemoved = true;
              print('Removing player $playerId from match ${otherMatch['id']}');
            }
          }
          
          // If player was removed, also update player slots and save changes
          if (playerRemoved) {
            otherMatch['players'] = updatedOtherPlayers;
            
            // Also update player slots
            if (otherMatch['playerSlots'] != null) {
              final otherPlayerSlots = otherMatch['playerSlots'] as List;
              for (var j = 0; j < otherPlayerSlots.length; j++) {
                final slot = otherPlayerSlots[j] as Map<String, dynamic>;
                if (slot['playerId'] == playerId) {
                  // Clear the player ID from this slot
                  slot['playerId'] = '';
                  otherPlayerSlots[j] = slot;
                }
              }
              otherMatch['playerSlots'] = otherPlayerSlots;
            }
            
            otherMatch['updated_at'] = DateTime.now().millisecondsSinceEpoch;
            matches[i] = otherMatch;
            
            // Broadcast update for match player left
            WebSocketManager().broadcastMatchUpdate(otherMatch['id'].toString(), otherMatch);
          }
        }
      }

      // Add player to the new match
      final players = <Map<String, dynamic>>[];
      if (match['players'] != null) {
        final playersList = match['players'] as List;
        for (final player in playersList) {
          players.add(player as Map<String, dynamic>);
        }
      }

      // Add the player to the players array
      players.add(playerData);
      match['players'] = players;
      
      // IMPORTANT: Also update the player slot with the playerId
      if (match['playerSlots'] != null) {
        // Find an available player slot to assign this player to
        final playerSlots = match['playerSlots'] as List;

        // Look for the first available slot (one without a playerId)
        bool slotFound = false;
        for (var i = 0; i < playerSlots.length; i++) {
          final slot = playerSlots[i] as Map<String, dynamic>;

          // If this slot has no player ID yet, assign this player to it
          if (slot['playerId'] == null || slot['playerId'] == '') {
            // Update the slot with the playerId
            slot['playerId'] = playerId;
            playerSlots[i] = slot;
            slotFound = true;
            break;
          }
        }
        
        if (!slotFound) {
          print('Warning: No available slot found for player $playerId in match $id');
        }
        
        // Update the playerSlots in the match
        match['playerSlots'] = playerSlots;
      }
      
      match['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    
    // Ensure is_open_for_joining is properly set (might be null in existing matches)
    // If the match is open for status, it should also be open for joining unless at max capacity
    if (match['status'] == MatchStatus.open.name) {
      // Check if the match is at max capacity by comparing playerSlots with non-empty slots
      bool isAtCapacity = false;
      if (match['playerSlots'] != null) {
        final playerSlots = match['playerSlots'] as List;
        final filledSlots = playerSlots.where((slot) => 
          slot['playerId'] != null && slot['playerId'] != '').length;
        final totalSlots = playerSlots.length;
        isAtCapacity = filledSlots >= totalSlots;
      }
      
      // Set is_open_for_joining based on capacity
      match['is_open_for_joining'] = !isAtCapacity;
    } else {
      // If match is not open, it's definitely not open for joining
      match['is_open_for_joining'] = false;
    }

    matches[matchIndex] = match;
    await _writeMatchesData(matches);
    _changeController.add(null);

      // Broadcast updated match details via WebSocket
      WebSocketManager().broadcastMatchUpdate(match['id'].toString(), match);
      
      // Also broadcast open matches update to refresh the list for all clients
      print('GameMatchRepository: About to broadcast open_matches_update after join');
      _broadcastOpenMatchesUpdate();
      print('GameMatchRepository: Completed open_matches_update broadcast after join');

      return match;
    } catch (e) {
      print('Error joining game_match: $e');
      rethrow;
    }
  }

  /// Leave an existing match - remove player from the match
  Future<Map<String, dynamic>?> leaveMatch(
    String id,
    Map<String, dynamic> playerData,
  ) async {
    try {
      final matches = await _readMatchesData();
      final matchIndex = matches.indexWhere((match) => match['id'] == id);

      if (matchIndex == -1) {
        print('Match $id not found');
        return null;
      }

      // Retrieve the match data
      final matchData = matches[matchIndex];

      // Check if playerData contains id (required for leave logic)
      if (!playerData.containsKey('id') || playerData['id'] == null) {
        print('Player ID is required for leaving a match');
        return null;
      }

      final playerId = playerData['id'] as String;

      // Check if the player is already in the match
      final playersList = (matchData['players'] ?? <Map<String, dynamic>>[]) as List<dynamic>;
      
      // Log the full players list to debug
      print('DEBUG - Players in match $id:');
      print('DEBUG - Raw playersList: $playersList');
      print('DEBUG - playersList type: ${playersList.runtimeType}');
      print('DEBUG - playersList length: ${playersList.length}');
      
      // Check if playersList items are Maps or Strings
      if (playersList.isNotEmpty) {
        print('DEBUG - First player item type: ${playersList.first.runtimeType}');
        print('DEBUG - First player item content: ${playersList.first}');
      }
      
      for (final player in playersList) {
        if (player is Map) {
          print(' - Player: ${player['id'] ?? 'null'} (checking against requested player: $playerId)');
        } else {
          print(' - Player (non-map): $player (type: ${player.runtimeType})');
        }
      }

      // Check against 'id' property
      final playerIndex = playersList.indexWhere(
            (dynamic p) => p['id'] == playerId,
          );

      if (playerIndex != -1) {
        print('Attempting to remove player $playerId from match $id');

        // Remove player from the match
        print('Removing player $playerId from match $id');
        playersList.removeAt(playerIndex);
        matchData['players'] = playersList;

        // Find the player's slot and clear it
        if (matchData['playerSlots'] != null) {
          final playerSlots = matchData['playerSlots'] as List;
          final slotIndex = playerSlots.indexWhere(
            (dynamic slot) => slot['playerId'] == playerId,
          );

          if (slotIndex != -1) {
            final slot = playerSlots[slotIndex] as Map<String, dynamic>;
            // Clear player ID but keep other slot data
            slot['playerId'] = null;
            slot['playerName'] = null;
            print('Cleared player slot for $playerId in match $id');
          }
        }

        // Update match timestamp
        matchData['updated_at'] = DateTime.now().millisecondsSinceEpoch;

        // Update the match in the list
        matches[matchIndex] = matchData;

        // Persist changes
        await _writeMatchesData(matches);
        _changeController.add(null);

        // Broadcast an update to all WebSocket clients
        WebSocketManager().broadcastMatchUpdate(id, matchData);

        // Also broadcast an open matches update to ensure UI shows correct join status
        await _broadcastOpenMatchesUpdate();

        return matchData;
      } else {
        print('Player $playerId is not in match $id');
        
        // Add detailed diagnostic info
        print('DEBUG - Could not find player in match. Dumping match data:');
        print(' - Match ID: $id');
        print(' - Player slots: ${matchData['playerSlots'] ?? 'none'}');
        print(' - Player IDs in slots:');
        
        if (matchData['playerSlots'] != null) {
          final slots = matchData['playerSlots'] as List;
          for (final slot in slots) {
            final slotData = slot as Map<String, dynamic>;
            print('   * Slot ${slotData['id']}: playerId=${slotData['playerId']}');
          }
        }
        
        return matchData;
      }
    } catch (e) {
      print('Error leaving match: $e');
      return null;
    }
  }
      
  /// Update the game name of a match
  Future<Map<String, dynamic>?> updateGameName(
    String id,
    String gameName,
  ) async {
    try {
      final matches = await _readMatchesData();
      final matchIndex = matches.indexWhere((match) => match['id'] == id);

      if (matchIndex == -1) {
        print('Match $id not found');
        return null;
      }

      // Retrieve the match data
      final matchData = matches[matchIndex];
      
      print('Updating game name for match $id to "$gameName"');
      matchData['gameName'] = gameName;

      // Update timestamp
      matchData['updated_at'] = DateTime.now().millisecondsSinceEpoch;

      // Update the match in the list
      matches[matchIndex] = matchData;

      // Persist changes
      await _writeMatchesData(matches);
      WebSocketManager().broadcastMatchUpdate(id, matchData);

      // Also broadcast an open matches update to ensure the name is updated in UI lists
      await _broadcastOpenMatchesUpdate();
      print('Game name updated successfully');
      return matchData;
    } catch (e) {
      print('Error updating game name: $e');
      return null;
    }
  }
  
  /// Submit a turn for a game_match
  Future<Map<String, dynamic>?> submitTurn(
    String id,
    Map<String, dynamic> turnData,
  ) async {
    try {
      final matches = await _readMatchesData();
      final matchIndex = matches.indexWhere((match) => match['id'] == id);

      if (matchIndex == -1) return null;

      final match = matches[matchIndex];

      // Extract the player ID and turn number from the turn data
      final submittingPlayerId = turnData['id'] as String?;
      final clientTurnNumber = turnData['turnNumber'] as int?;

      if (submittingPlayerId == null || clientTurnNumber == null) {
        throw Exception('Turn data must include id and turnNumber');
      }

      // Initialize turnsForNumber if it doesn't exist
      if (match['turns_by_number'] == null) {
        match['turns_by_number'] = <String, dynamic>{};
      }

      final turnsByNumber = match['turns_by_number'] as Map<String, dynamic>;

      // Initialize for this turn number if it doesn't exist
      final turnNumberKey = clientTurnNumber.toString();
      if (!turnsByNumber.containsKey(turnNumberKey)) {
        turnsByNumber[turnNumberKey] = <Map<String, dynamic>>[];
      }

      // Add timestamp to turn data
      final newTurn = {
        ...turnData,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Add this turn to the appropriate turn number collection
      final turnsForNumber = turnsByNumber[turnNumberKey] as List;

      // Check if this player has already submitted a turn for this turn number
      final playerAlreadySubmitted = turnsForNumber.any(
        (turn) => (turn as Map<String, dynamic>)['id'] == submittingPlayerId,
      );

      if (playerAlreadySubmitted) {
        // Update the existing turn
        final existingTurnIndex = turnsForNumber.indexWhere(
          (turn) => (turn as Map<String, dynamic>)['id'] == submittingPlayerId,
        );
        turnsForNumber[existingTurnIndex] = newTurn;
      } else {
        // Add the new turn
        turnsForNumber.add(newTurn);
      }

      // Update the match
      match['turns_by_number'] = turnsByNumber;
      match['updated_at'] = DateTime.now().millisecondsSinceEpoch;
      // match['current_turn'] = clientTurnNumber;

      // Check if all players have submitted turns for this turn number
      final allPlayersSubmitted = _haveAllPlayersSubmittedTurn(match, clientTurnNumber);

      // Save changes
      matches[matchIndex] = match;
      await _writeMatchesData(matches);
      _changeController.add(null);

      // If all players have submitted, notify the game host
      if (allPlayersSubmitted) {
        // Find the host player
        final hostPlayerId = match['host_id'] as String?;

        // Get all turns for this turn number
        final turnsForCurrentNumber = turnsByNumber[turnNumberKey] as List;
        final turnData = List<Map<String, dynamic>>.from(
          turnsForCurrentNumber.map((item) => item as Map<String, dynamic>),
        );

        final turnCompletionData = {
          'type': 'turn_processing_required',
          'match_id': id,
          'turnNumber': clientTurnNumber,
          'turns': turnData,
        };

        // Check if there's a host to notify
        if (hostPlayerId != null) {
          // Send all turn data to host client for processing
          WebSocketManager().sendToClient(
            hostPlayerId,
            {
              'type': 'turn_processing_required',
              'data': turnCompletionData,
            },
          );
        }

        return {
          'success': true,
          'game_match': match,
          'all_turns_submitted': true,
          'turnNumber': clientTurnNumber,
          'type': 'turn_completion',
        };
      }

      return {
        'success': true,
        'game_match': match,
        'all_turns_submitted': false,
        'turnNumber': clientTurnNumber,
        'type': 'turn_submission',
      };
    } catch (e) {
      print('Error submitting turn: $e');
      rethrow;
    }
  }

  /// Update game_match status (close/reopen)
  Future<Map<String, dynamic>?> updateMatchStatus(
    String id,
    Map<String, dynamic> statusData,
  ) async {
    try {
      final matches = await _readMatchesData();
      final matchIndex = matches.indexWhere((match) => match['id'] == id);

      if (matchIndex >= 0) {
        final match = matches[matchIndex];
        
        // Update status
        if (statusData.containsKey('status')) {
          match['status'] = statusData['status'];
        }

        // Update player slots if provided
        if (statusData.containsKey('playerSlots')) {
          print('Updating playerSlots for match $id with: ${statusData['playerSlots']}');
          match['playerSlots'] = statusData['playerSlots'];
        }
        
        // Update updatedBy if provided
        if (statusData.containsKey('updatedBy')) {
          match['updatedBy'] = statusData['updatedBy'];
        }
        
        // Update other fields if needed
        if (statusData.containsKey('updated_at')) {
          match['updated_at'] = statusData['updated_at'];
        } else {
          match['updated_at'] = DateTime.now().millisecondsSinceEpoch;
        }
        
        // Save changes
        matches[matchIndex] = match;
        await _writeMatchesData(matches);
        _changeController.add(null);
        
        // Broadcast update to WebSocket clients
        print('Broadcasting match update with updated playerSlots');
        WebSocketManager().broadcastMatchUpdate(match['id'].toString(), match);
        
        // Also update open matches list if this is an open match
        if (match['status'] == MatchStatus.open.name) {
          print('Also broadcasting open matches update since this is an open match');
          _broadcastOpenMatchesUpdate();
        }

        return match;
      } else {
        return null;
      }
    } catch (e) {
      print('Error updating game_match status: $e');
      return null;
    }
  }
  
  /// Open a match for joining by other players
  Future<Map<String, dynamic>?> openMatchForJoining(String id) async {
    try {
      final matches = await _readMatchesData();
      final matchIndex = matches.indexWhere((match) => match['id'] == id);

      if (matchIndex != -1) {
        final match = matches[matchIndex];
        
        // Set the top-level flag only
        match['is_open_for_joining'] = true;
        match['updated_at'] = DateTime.now().millisecondsSinceEpoch;
        
        // Make sure the match status is 'open'
        match['status'] = MatchStatus.open.name;
        
        // Save changes
        matches[matchIndex] = match;
        await _writeMatchesData(matches);
        _changeController.add(null);
        
        // Broadcast update to WebSocket clients
        WebSocketManager().broadcastMatchUpdate(match['id'].toString(), match);
        
        // Also broadcast updated open matches list to all subscribers
        _broadcastOpenMatchesUpdate();
        
        print('Match $id opened for joining');

        return match;
      } else {
        print('Match $id not found');
        return null;
      }
    } catch (e) {
      print('Error opening match for joining: $e');
      return null;
    }
  }

  /// Delete a match by ID
  Future<bool> deleteMatch(String id) async {
    try {
      final matches = await _readMatchesData();
      final matchIndex = matches.indexWhere((match) => match['id'] == id);

      if (matchIndex == -1) {
        print('Match $id not found for deletion');
        return false;
      }

      final deletedMatch = matches[matchIndex];
      // Remove the match
      matches.removeAt(matchIndex);
      
      // Save the updated matches list
      await _writeMatchesData(matches);
      _changeController.add(null);
      
      // Log the deletion
      print('Match $id successfully deleted');
      
      // Publish match deleted event to the EventBus
      print('GameMatchRepository: Publishing match_deleted event');
      final deletedEvent = GameEvent.matchDeleted(id, deletedMatch);
      EventBus().publish(deletedEvent);
      
      // Simply broadcast an open matches update which will indirectly
      // inform clients of the deletion by showing the updated matches list
      print('Broadcasting open matches update after deletion');
      WebSocketManager().broadcastOpenMatchesUpdate(await getOpenMatches());
      
      // Also call our helper method to broadcast updates
      _broadcastOpenMatchesUpdate();
      
      return true;
    } catch (e) {
      print('Error deleting match: $e');
      return false;
    }
  }

  /// Read matches data from file
  Future<List<Map<String, dynamic>>> _readMatchesData() async {
    try {
      if (!_matchesFile.existsSync()) {
        // Initialize with empty array if file doesn't exist
        await _ensureDirectoryExists();
        await _matchesFile.writeAsString('[]');
        return [];
      }

      final jsonString = await _matchesFile.readAsString();
      final jsonData = jsonDecode(jsonString);
      if (jsonData is List) {
        return List<Map<String, dynamic>>.from(
          jsonData.map((item) => item as Map<String, dynamic>),
        );
      }
      return [];
    } catch (e) {
      print('Error reading matches data: $e');
      return [];
    }
  }

  /// Write matches data to file
  Future<void> _writeMatchesData(List<Map<String, dynamic>> matches) async {
    await _ensureDirectoryExists();
    await _matchesFile.writeAsString(jsonEncode(matches));
  }

  /// Generate a unique ID for matches
  String _generateId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomString = List.generate(
      8,
      (_) => random.nextInt(16).toRadixString(16),
    ).join();
    return '$timestamp-$randomString';
  }

  /// Ensure the data directory exists
  Future<void> _ensureDirectoryExists() async {
    final directory = _matchesFile.parent;
    if (!directory.existsSync()) {
      await directory.create(recursive: true);
    }
  }

  /// Check if all players have submitted their turns for a given turn number
  bool _haveAllPlayersSubmittedTurn(Map<String, dynamic> match, int turnNumber) {
    // First, check if there are players in the match
    if (!match.containsKey('players') || match['players'] == null) {
      return false;
    }
    
    final playersList = match['players'] as List;
    final playerCount = playersList.length;
    
    // Now check if all players have submitted a turn
    if (!match.containsKey('turns_by_number') || match['turns_by_number'] == null) {
      return false;
    }
    
    final turnsByNumber = match['turns_by_number'] as Map<String, dynamic>;
    final turnNumberKey = turnNumber.toString();
    
    // If no turns exist for this turn number, clearly not all have submitted
    if (!turnsByNumber.containsKey(turnNumberKey)) {
      return false;
    }
    
    final turnsForNumber = turnsByNumber[turnNumberKey] as List;
    
    // If fewer turns than players, not all have submitted
    if (turnsForNumber.length < playerCount) {
      return false;
    }
    
    // Get all unique player IDs who have submitted turns
    final playerIdsWithTurns = <String>{};
    for (final turn in turnsForNumber) {
      final turnData = turn as Map<String, dynamic>;
      final playerId = turnData['id'] as String?;
      if (playerId != null) {
        playerIdsWithTurns.add(playerId);
      }
    }
    
    // Check if all players have submitted a turn
    for (final playerItem in playersList) {
      final player = playerItem as Map<String, dynamic>;
      final playerId = player['id'] as String?;
      if (playerId != null && !playerIdsWithTurns.contains(playerId)) {
        return false; // Found a player who hasn't submitted a turn
      }
    }
    
    return true; // All players have submitted a turn
  }
}
