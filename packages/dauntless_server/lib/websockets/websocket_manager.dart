import 'dart:async';
import 'dart:convert';

import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:dauntless_server/services/event_bus.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// Manager class for handling WebSocket connections
class WebSocketManager {

  /// Using a global instance for the singleton
  static WebSocketManager? _globalInstance;

  /// Factory constructor to return or create the singleton instance
  factory WebSocketManager() {
    print('WebSocketManager: Factory called...');
    // If global instance exists, return it
    if (_globalInstance != null) {
      print('WebSocketManager: Returning existing instance');
      return _globalInstance!;
    }
    // Create new instance if needed
    print('WebSocketManager: Creating new instance');
    _globalInstance = WebSocketManager._internal();
    return _globalInstance!;
  }

  /// Private constructor
  WebSocketManager._internal() {
    print('WebSocketManager: Singleton instance initialized');
    // Print debug memory address of this instance
    print('WebSocketManager: Instance ID: ${identityHashCode(this)}');
    
    // Subscribe to events from the EventBus
    _subscribeToEvents();
  }
  
  /// Subscribe to relevant events from the EventBus
  void _subscribeToEvents() {
    final eventBus = EventBus();
    print('WebSocketManager: Subscribing to EventBus events');
    
    // Listen for open matches changed events
    eventBus.filterByType('open_matches_changed').listen(_handleOpenMatchesChanged);
    
    // Listen for match update events
    eventBus.filterByType('match_updated').listen(_handleMatchUpdated);
    
    // Listen for match created events
    eventBus.filterByType('match_created').listen(_handleMatchCreated);
    
    print('WebSocketManager: Successfully subscribed to EventBus events');
  }
  
  /// Handle open matches changed events from the EventBus
  void _handleOpenMatchesChanged(GameEvent event) {
    print('WebSocketManager: Handling open_matches_changed event');
    
    try {
      final matchesData = event.data['matches'] as List<dynamic>;
      print('WebSocketManager: Broadcasting ${matchesData.length} matches to clients');
      
      // Use the existing broadcast method to send to all clients
      broadcastOpenMatchesUpdate(
        matchesData.cast<Map<String, dynamic>>().toList()
      );
    } catch (e) {
      print('WebSocketManager: Error handling open_matches_changed event: $e');
    }
  }
  
  /// Handle match updated events from the EventBus
  void _handleMatchUpdated(GameEvent event) {
    print('WebSocketManager: Handling match_updated event');
    
    try {
      final matchData = event.data;
      final matchId = matchData['id'] as String?;
      
      if (matchId != null) {
        print('WebSocketManager: Broadcasting match update for match $matchId');
        
        // Broadcast to match-specific subscribers
        if (_matchSubscribers.containsKey(matchId)) {
          final subscribers = _matchSubscribers[matchId] ?? {};
          
          if (subscribers.isNotEmpty) {
            final message = jsonEncode({
              'type': 'match_update',
              'data': matchData,
            });
            
            for (final clientId in subscribers) {
              final client = _clients[clientId];
              if (client != null) {
                try {
                  client.sink.add(message);
                } catch (e) {
                  print('WebSocketManager: Error sending match update to client $clientId: $e');
                }
              }
            }
          }
        }
      }
    } catch (e) {
      print('WebSocketManager: Error handling match_updated event: $e');
    }
  }
  
  /// Handle match created events from the EventBus
  void _handleMatchCreated(GameEvent event) {
    print('WebSocketManager: Handling match_created event');
    
    try {
      final matchData = event.data;
      final status = matchData['status'] as String?;
      
      // If this is an open match, trigger open matches update
      if (status == 'open') {
        print('WebSocketManager: New open match created, updating open matches list');
        
        // Set is_open_for_joining to true since we know this is a new match with open status
        // This is needed because getOpenMatches() checks for both status and is_open_for_joining
        matchData['is_open_for_joining'] = true;
        
        // Get all matches and update the new one to ensure it appears in the list
        GameMatchController().getMatches().then((allMatches) {
          final matchIndex = allMatches.indexWhere((m) => m['id'] == matchData['id']);
          if (matchIndex != -1) {
            allMatches[matchIndex]['is_open_for_joining'] = true;
            // Broadcast all open matches
            final openMatches = allMatches.where((match) => 
              match['status'] == 'open' && (match['is_open_for_joining'] == true || match['id'] == matchData['id'])
            ).toList();
            
            broadcastOpenMatchesUpdate(openMatches);
          }
        });
      }
    } catch (e) {
      print('WebSocketManager: Error handling match_created event: $e');
    }
  }

  /// Map of connected WebSocket clients
  /// Key is client id, value is the WebSocket channel
  final Map<String, WebSocketChannel> _clients = {};

  /// Map of game_match subscribers
  /// Key is game_match id, value is list of client ids
  final Map<String, Set<String>> _matchSubscribers = {};
  
  /// Set of clients subscribed to open matches updates
  final Set<String> _openMatchesSubscribers = {};

  /// Stream controller for game_match updates
  final _matchController = StreamController<Map<String, dynamic>>.broadcast();

  /// Stream of game_match updates
  Stream<Map<String, dynamic>> get matchStream => _matchController.stream;

  /// Add a new WebSocket client
  void addClient(String clientId, WebSocketChannel socket) {
    _clients[clientId] = socket;
    print('WebSocket client added: $clientId');
  }

  /// Remove a WebSocket client
  void removeClient(String clientId) {
    // Remove client from all subscriptions
    for (final matchId in _matchSubscribers.keys.toList()) {
      _matchSubscribers[matchId]?.remove(clientId);
    }
    
    // Remove from open matches subscribers
    _openMatchesSubscribers.remove(clientId);
    
    // Clean up empty sets
    _matchSubscribers.removeWhere((_, subscribers) => subscribers.isEmpty);
    
    // Close the client connection
    _clients[clientId]?.sink.close();
    
    // Remove the client
    _clients.remove(clientId);
    
    print('WebSocket client removed: $clientId');
  }

  /// Subscribe a client to a game_match
  void subscribeClientToMatch(String clientId, String matchId) {
    _matchSubscribers.putIfAbsent(matchId, () => {});
    _matchSubscribers[matchId]!.add(clientId);
    
    print('Client $clientId subscribed to game_match $matchId');
    
    // Notify client of successful subscription
    _clients[clientId]?.sink.add(jsonEncode({
      'type': 'subscription_success',
      'match_id': matchId,
    }));
  }

  /// Unsubscribe a client from a game_match
  void unsubscribeClientFromMatch(String clientId, String matchId) {
    _matchSubscribers[matchId]?.remove(clientId);
    
    // Clean up empty sets
    if (_matchSubscribers[matchId]?.isEmpty ?? false) {
      _matchSubscribers.remove(matchId);
    }
    
    print('Client $clientId unsubscribed from game_match $matchId');
  }

  /// Send a message to a specific client
  void sendToClient(String clientId, Map<String, dynamic> message) {
    final client = _clients[clientId];
    if (client != null) {
      client.sink.add(jsonEncode(message));
    }
  }
      
  /// Broadcast a game_match update to all subscribed clients
  void broadcastMatchUpdate(String matchId, Map<String, dynamic> matchData) {
    // Add game_match update to stream
    _matchController.add({
      'match_id': matchId,
      'data': matchData,
    });
    
    // Get subscribers for this game_match
    final subscribers = _matchSubscribers[matchId] ?? {};
    
    if (subscribers.isEmpty) {
      return; // No subscribers for this game_match
    }
    
    // Prepare the message
    final message = jsonEncode({
      'topic': 'match:$matchId:update',
      'data': matchData,
    });
    
    // Send to all subscribers
    for (final clientId in subscribers) {
      final client = _clients[clientId];
      if (client != null) {
        client.sink.add(message);
      }
    }
  }

  /// Subscribe a client to open matches updates
  void subscribeClientToOpenMatches(String clientId) {
    // Add client to the subscribers list if not already present
    if (!_openMatchesSubscribers.contains(clientId)) {
      _openMatchesSubscribers.add(clientId);
      print('WebSocketManager: Client $clientId subscribed to open matches updates');
      print('WebSocketManager: Total subscribers: ${_openMatchesSubscribers.length}');
    } else {
      print('WebSocketManager: Client $clientId already subscribed to open matches');
    }
    
    // Notify client of successful subscription
    _clients[clientId]?.sink.add(jsonEncode({
      'topic': 'subscription_success',
      'data': {
        'topic': 'open_matches'
      },
    }));
  }

  /// Unsubscribe a client from open matches updates
  void unsubscribeClientFromOpenMatches(String clientId) {
    _openMatchesSubscribers.remove(clientId);
    print('Client $clientId unsubscribed from open matches updates');
  }

  /// Broadcast open matches updates to all subscribed clients
  void broadcastOpenMatchesUpdate(List<Map<String, dynamic>> matchesData) {
    if (_openMatchesSubscribers.isEmpty) {
      print('WebSocketManager: No subscribers for open matches updates, skipping broadcast');
      return;
    }
    
    // Prepare the message
    final message = jsonEncode({
      'topic': 'open_matches',
      'data': {
        'matches': matchesData,
      },
    });
    
    print('WebSocketManager: Broadcasting ${matchesData.length} open matches to ${_openMatchesSubscribers.length} subscribed clients');
    
    // Print match IDs being broadcast
    if (matchesData.isNotEmpty) {
      final matchIds = matchesData.map((m) => m['id']).join(', ');
      print('WebSocketManager: Match IDs being broadcast: $matchIds');
    }
    
    // Count of successful messages sent
    var sentCount = 0;
    
    // Send to all subscribers
    for (final clientId in _openMatchesSubscribers) {
      final client = _clients[clientId];
      if (client != null) {
        try {
          client.sink.add(message);
          sentCount++;
        } catch (e) {
          print('WebSocketManager: Error sending to client $clientId: $e');
        }
      } else {
        print('WebSocketManager: Client $clientId not found in active connections');
      }
    }
    
    print('WebSocketManager: Successfully sent updates to $sentCount/${_openMatchesSubscribers.length} clients');
  }

  /// Close all connections and cleanup resources
  void dispose() {
    for (final client in _clients.values) {
      client.sink.close();
    }
    _clients.clear();
    _matchSubscribers.clear();
    _openMatchesSubscribers.clear();
    _matchController.close();
  }
}
