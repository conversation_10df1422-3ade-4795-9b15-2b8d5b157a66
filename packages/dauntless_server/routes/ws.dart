import 'dart:convert';
import 'package:dart_frog/dart_frog.dart';
import 'package:dart_frog_web_socket/dart_frog_web_socket.dart';
import 'package:dauntless_server/websockets/websocket_manager.dart';

/// WebSocket route handler
<PERSON><PERSON> get onRequest => webSocketHandler((channel, protocol) {
  print('WebSocket client connected with protocol: $protocol');
  final clientId = DateTime.now().millisecondsSinceEpoch.toString();
  
  // Add client to WebSocket manager
  WebSocketManager().addClient(clientId, channel);
  
  // Handle incoming messages
  channel.stream.listen(
    (message) {
      if (message is! String) return;
      
      try {
        print('Received WebSocket message: $message');
        final data = jsonDecode(message) as Map<String, dynamic>;
        final type = data['type'] as String?;
        
        switch (type) {
          case 'subscribe':
            final topic = data['topic'] as String?;
            if (topic == 'open_matches') {
              print('Client $clientId subscribing to open_matches');
              WebSocketManager().subscribeClientToOpenMatches(clientId);
            }
            break;
            
          case 'unsubscribe':
            final topic = data['topic'] as String?;
            if (topic == 'open_matches') {
              print('Client $clientId unsubscribing from open_matches');
              WebSocketManager().unsubscribeClientFromOpenMatches(clientId);
            }
            break;
            
          case 'subscribe_match':
            final matchId = data['match_id'] as String?;
            if (matchId != null) {
              WebSocketManager().subscribeClientToMatch(clientId, matchId);
            }
            break;
            
          case 'unsubscribe_match':
            final matchId = data['match_id'] as String?;
            if (matchId != null) {
              WebSocketManager().unsubscribeClientFromMatch(clientId, matchId);
            }
            break;
        }
      } catch (e) {
        print('Error processing WebSocket message: $e');
      }
    },
    onDone: () {
      // Remove client when connection closes
      print('WebSocket client disconnected: $clientId');
      WebSocketManager().removeClient(clientId);
    },
    onError: (error) {
      print('WebSocket error: $error');
      WebSocketManager().removeClient(clientId);
    },
  );
});