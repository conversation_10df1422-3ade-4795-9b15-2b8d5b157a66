import 'package:dart_frog/dart_frog.dart';

/// Middleware for matches routes to extract game_match ID from the request
Handler middleware(Handler handler) {
  return handler.use(
    provider<String>((context) {
      final params = context.request.uri.pathSegments;
      // Matches URL pattern: /matches/{id}
      // pathSegments would be ['matches', '{id}']
      if (params.length >= 2) {
        return params[1]; // Return the ID segment
      }
      return ''; // Return empty string if no ID found
    }),
  );
}
