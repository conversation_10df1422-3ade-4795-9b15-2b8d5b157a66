import 'dart:async';
import 'dart:convert';

import 'package:dart_frog/dart_frog.dart';
import 'package:dauntless_server/controllers/game_match_controller.dart';

/// Handler for /matches endpoint
Future<Response> onRequest(RequestContext context) async {
  final controller = context.read<GameMatchController>();

  switch (context.request.method) {
    case HttpMethod.get:
      return _handleGetMatches(controller, context);
    case HttpMethod.post:
      return _handleCreateMatch(controller, context);
    default:
      return Response(statusCode: 405, body: 'Method not allowed');
  }
}

Future<Response> _handleGetMatches(
    GameMatchController controller, RequestContext context) async {
  try {
    final queryParams = context.request.uri.queryParameters;
    final showAll = queryParams['showAll'] == 'true';

    if (showAll) {
      // Show all matches when showAll=true
      final matches = await controller.getMatches();
      return Response.json(body: matches);
    } else {
      // Default to showing only open matches
      final openMatches = await controller.getOpenMatches();
      return Response.json(body: openMatches);
    }
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to retrieve matches: ${e.toString()}'},
    );
  }
}

Future<Response> _handleCreateMatch(
  GameMatchController controller,
  RequestContext context,
) async {
  try {
    final requestBody = await context.request.body();
    final matchData = jsonDecode(requestBody) as Map<String, dynamic>;

    final match = await controller.createMatch(matchData);
    return Response.json(
      statusCode: 201, // Created
      body: match,
    );
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to create game_match: ${e.toString()}'},
    );
  }
}
