import 'dart:async';
import 'dart:convert';

import 'package:dart_frog/dart_frog.dart';

import 'package:dauntless_server/controllers/game_match_controller.dart';

/// Handler for /matches/{id}/status endpoint
Future<Response> onRequest(RequestContext context, String id) async {
  final controller = context.read<GameMatchController>();
  
  if (context.request.method != HttpMethod.put) {
    return Response(statusCode: 405, body: 'Method not allowed');
  }
  
  return _handleUpdateMatchStatus(controller, id, context);
}

Future<Response> _handleUpdateMatchStatus(
  GameMatchController controller,
  String id,
  RequestContext context,
) async {
  try {
    final requestBody = await context.request.body();
    final statusData = jsonDecode(requestBody) as Map<String, dynamic>;
    
    final match = await controller.updateMatchStatus(id, statusData);
    
    if (match == null) {
      return Response.json(
        statusCode: 404,
        body: {'error': 'Match not found'},
      );
    }
    
    return Response.json(body: match);
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to update game_match status: ${e.toString()}'},
    );
  }
}
