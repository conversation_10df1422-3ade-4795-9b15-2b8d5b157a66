import 'dart:async';
import 'dart:convert';

import 'package:dart_frog/dart_frog.dart';
import 'package:dauntless_server/controllers/game_match_controller.dart';

/// Handler for /matches/{id}/name endpoint
Future<Response> onRequest(RequestContext context, String id) async {
  final controller = context.read<GameMatchController>();
  
  if (context.request.method != HttpMethod.post) {
    return Response(statusCode: 405, body: 'Method not allowed');
  }
  
  return _handleUpdateGameName(controller, id, context);
}

Future<Response> _handleUpdateGameName(
  GameMatchController controller,
  String id,
  RequestContext context,
) async {
  try {
    final requestBody = await context.request.body();
    final nameData = jsonDecode(requestBody) as Map<String, dynamic>;
    
    // Validate required fields
    if (!nameData.containsKey('gameName')) {
      return Response.json(
        statusCode: 400,
        body: {'error': 'gameName is required'},
      );
    }
    
    final match = await controller.updateGameName(id, nameData);
    
    if (match == null) {
      return Response.json(
        statusCode: 404,
        body: {'error': 'Match not found'},
      );
    }
    
    return Response.json(body: match);
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to update game name: ${e.toString()}'},
    );
  }
}
