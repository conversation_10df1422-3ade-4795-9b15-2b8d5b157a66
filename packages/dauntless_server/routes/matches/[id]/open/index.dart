import 'dart:async';

import 'package:dart_frog/dart_frog.dart';
import 'package:dauntless_server/controllers/game_match_controller.dart';

/// Handler for /matches/{id}/open endpoint
Future<Response> onRequest(RequestContext context, String id) async {
  final controller = context.read<GameMatchController>();

  if (context.request.method != HttpMethod.put) {
    return Response(statusCode: 405, body: 'Method not allowed');
  }

  return _handleOpenMatch(controller, id);
}

Future<Response> _handleOpenMatch(
  GameMatchController controller,
  String id,
) async {
  try {
    final match = await controller.openMatchForJoining(id);

    if (match == null) {
      return Response.json(
        statusCode: 404,
        body: {'error': 'Match not found'},
      );
    }

    return Response.json(body: match);
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to open match for joining: ${e.toString()}'},
    );
  }
}
