import 'dart:async';
import 'dart:convert';

import 'package:dart_frog/dart_frog.dart';

import 'package:dauntless_server/controllers/game_match_controller.dart';

/// Handler for /matches/{id} endpoint
Future<Response> onRequest(RequestContext context, String id) async {
  final controller = context.read<GameMatchController>();
  
  switch (context.request.method) {
    case HttpMethod.get:
      return _handleGetMatch(controller, id);
    case HttpMethod.put:
      return _handleUpdateMatchStatus(controller, id, context);
    case HttpMethod.delete:
      return _handleDeleteMatch(controller, id);
    default:
      return Response(statusCode: 405, body: 'Method not allowed');
  }
}

Future<Response> _handleGetMatch(
  GameMatchController controller,
  String id,
) async {
  try {
    final match = await controller.getMatch(id);
    
    if (match == null || match.isEmpty) {
      return Response.json(
        statusCode: 404,
        body: {'error': 'Match not found'},
      );
    }
    
    return Response.json(body: match);
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to retrieve game_match: ${e.toString()}'},
    );
  }
}

Future<Response> _handleUpdateMatchStatus(
  GameMatchController controller,
  String id,
  RequestContext context,
) async {
  try {
    final requestBody = await context.request.body();
    final data = jsonDecode(requestBody) as Map<String, dynamic>;
    
    final match = await controller.updateMatchStatus(id, data);
    
    if (match == null) {
      return Response.json(
        statusCode: 404,
        body: {'error': 'Match not found'},
      );
    }
    
    return Response.json(body: match);
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to update game_match status: ${e.toString()}'},
    );
  }
}

Future<Response> _handleDeleteMatch(
  GameMatchController controller,
  String id,
) async {
  try {
    final success = await controller.deleteMatch(id);
    
    if (!success) {
      return Response.json(
        statusCode: 404,
        body: {'error': 'Match not found or could not be deleted'},
      );
    }
    
    return Response.json(
      body: {
        'success': true,
        'message': 'Match successfully deleted',
        'id': id,
      },
    );
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to delete game_match: ${e.toString()}'},
    );
  }
}
