import 'dart:async';
import 'dart:convert';

import 'package:dart_frog/dart_frog.dart';
import 'package:dauntless_server/controllers/game_match_controller.dart';

/// Handler for /matches/{id}/turns endpoint
Future<Response> onRequest(RequestContext context, String id) async {
  final controller = context.read<GameMatchController>();
  
  if (context.request.method != HttpMethod.post) {
    return Response(statusCode: 405, body: 'Method not allowed');
  }
  
  return _handleSubmitTurn(controller, id, context);
}

Future<Response> _handleSubmitTurn(
  GameMatchController controller,
  String id,
  RequestContext context,
) async {
  try {
    final requestBody = await context.request.body();
    final turnData = jsonDecode(requestBody) as Map<String, dynamic>;
    
    final match = await controller.submitTurn(id, turnData);
    
    if (match == null) {
      return Response.json(
        statusCode: 404,
        body: {'error': 'Match not found'},
      );
    }
    
    return Response.json(body: match);
  } catch (e) {
    return Response.json(
      statusCode: 500,
      body: {'error': 'Failed to submit turn: ${e.toString()}'},
    );
  }
}
