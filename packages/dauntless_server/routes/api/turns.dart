import 'dart:convert';

import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:dart_frog/dart_frog.dart';

Future<Response> onRequest(RequestContext context) async {
  final request = context.request;
  
  // Only accept POST requests for turn submission
  if (request.method != HttpMethod.post) {
    return Response(statusCode: 405, body: 'Method not allowed');
  }
  
  try {
    // Parse the request body
    final body = await request.body();
    final data = jsonDecode(body) as Map<String, dynamic>;
    
    // Validate required fields
    if (!data.containsKey('type') || data['type'] != 'turn_submission' || 
        !data.containsKey('playerId') || !data.containsKey('actions') || 
        !data.containsKey('matchId')) {
      return Response(
        statusCode: 400,
        body: jsonEncode({
          'error': 'Invalid request format',
          'message': 'Request must include type, playerId, actions, and matchId'
        }),
      );
    }
    
    final matchId = data['matchId'] as String;
    final controller = GameMatchController();
    
    // Format turn data for the server
    final turnData = {
      'playerId': data['playerId'],
      'actions': data['actions'],
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    // Submit the turn
    final result = await controller.submitTurn(matchId, turnData);
    
    if (result == null) {
      return Response(
        statusCode: 404,
        body: jsonEncode({
          'error': 'Match not found',
          'message': 'No game_match found with ID: $matchId',
        }),
      );
    }
    
    return Response(
      body: jsonEncode({
        'success': true,
        'game_match': result,
      }),
    );
  } catch (e) {
    return Response(
      statusCode: 500,
      body: jsonEncode({
        'error': 'Server error',
        'message': e.toString(),
      }),
    );
  }
}
