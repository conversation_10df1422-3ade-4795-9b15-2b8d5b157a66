import 'dart:async';

import 'package:dauntless_server/services/event_bus.dart';
import 'package:test/test.dart';

void main() {
  group('GameEvent', () {
    test('should create basic event with type and data', () {
      // Arrange
      const type = 'test_event';
      final data = {'key': 'value'};

      // Act
      final event = GameEvent(type, data);

      // Assert
      expect(event.type, equals(type));
      expect(event.data, equals(data));
      expect(event.timestamp, isA<DateTime>());
    });

    test('should create event with custom timestamp', () {
      // Arrange
      final customTimestamp = DateTime(2023, 1, 1);
      const type = 'test_event';
      final data = {'key': 'value'};

      // Act
      final event = GameEvent(type, data, timestamp: customTimestamp);

      // Assert
      expect(event.timestamp, equals(customTimestamp));
    });

    test('should create match created event', () {
      // Arrange
      final matchData = {'id': 'match123', 'status': 'open'};

      // Act
      final event = GameEvent.matchCreated(matchData);

      // Assert
      expect(event.type, equals('match_created'));
      expect(event.data, equals(matchData));
    });

    test('should create match updated event', () {
      // Arrange
      final matchData = {'id': 'match123', 'status': 'active'};

      // Act
      final event = GameEvent.matchUpdated(matchData);

      // Assert
      expect(event.type, equals('match_updated'));
      expect(event.data, equals(matchData));
    });

    test('should create open matches changed event', () {
      // Arrange
      final matches = [
        {'id': 'match1', 'status': 'open'},
        {'id': 'match2', 'status': 'open'},
      ];

      // Act
      final event = GameEvent.openMatchesChanged(matches);

      // Assert
      expect(event.type, equals('open_matches_changed'));
      expect(event.data['matches'], equals(matches));
    });

    test('should create player joined event', () {
      // Arrange
      const matchId = 'match123';
      final playerData = {'id': 'player456', 'name': 'Test Player'};

      // Act
      final event = GameEvent.playerJoined(matchId, playerData);

      // Assert
      expect(event.type, equals('player_joined'));
      expect(event.data['match_id'], equals(matchId));
      expect(event.data['player'], equals(playerData));
    });

    test('should create turn submitted event', () {
      // Arrange
      const matchId = 'match123';
      final turnData = {'playerId': 'player456', 'move': 'test-move'};

      // Act
      final event = GameEvent.turnSubmitted(matchId, turnData);

      // Assert
      expect(event.type, equals('turn_submitted'));
      expect(event.data['match_id'], equals(matchId));
      expect(event.data['turn'], equals(turnData));
    });

    test('should create match deleted event', () {
      // Arrange
      const matchId = 'match123';
      final matchData = {'id': matchId, 'status': 'deleted'};

      // Act
      final event = GameEvent.matchDeleted(matchId, matchData);

      // Assert
      expect(event.type, equals('match_deleted'));
      expect(event.data['match_id'], equals(matchId));
      expect(event.data['match'], equals(matchData));
    });

    test('should have meaningful toString', () {
      // Arrange
      final event = GameEvent('test_event', {'key': 'value'});

      // Act
      final result = event.toString();

      // Assert
      expect(result, contains('GameEvent'));
      expect(result, contains('test_event'));
      expect(result, contains('timestamp'));
    });
  });

  group('EventBus', () {
    // Note: EventBus is a singleton, so we need to be careful with tests
    // Each test will get the same instance

    test('should be singleton', () {
      // Act
      final eventBus1 = EventBus();
      final eventBus2 = EventBus();

      // Assert
      expect(identical(eventBus1, eventBus2), isTrue);
    });

    test('should publish and receive events', () async {
      // Arrange
      final eventBus = EventBus();
      final receivedEvents = <GameEvent>[];
      final subscription = eventBus.events.listen(receivedEvents.add);

      final event = GameEvent('test_event_unique_1', {'key': 'value'});

      // Act
      eventBus.publish(event);

      // Wait for event to be processed
      await Future.delayed(Duration.zero);

      // Assert
      expect(receivedEvents, hasLength(1));
      expect(receivedEvents.first.type, equals('test_event_unique_1'));
      expect(receivedEvents.first.data['key'], equals('value'));

      await subscription.cancel();
    });

    test('should filter events by type', () async {
      // Arrange
      final eventBus = EventBus();
      final matchCreatedEvents = <GameEvent>[];
      final matchUpdatedEvents = <GameEvent>[];

      final subscription1 = eventBus.filterByType('match_created_unique').listen(matchCreatedEvents.add);
      final subscription2 = eventBus.filterByType('match_updated_unique').listen(matchUpdatedEvents.add);

      // Act
      eventBus.publish(GameEvent('match_created_unique', {'id': 'match1'}));
      eventBus.publish(GameEvent('match_updated_unique', {'id': 'match2'}));
      eventBus.publish(GameEvent('match_created_unique', {'id': 'match3'}));

      // Wait for events to be processed
      await Future.delayed(Duration.zero);

      // Assert
      expect(matchCreatedEvents, hasLength(2));
      expect(matchUpdatedEvents, hasLength(1));
      expect(matchCreatedEvents.first.data['id'], equals('match1'));
      expect(matchCreatedEvents.last.data['id'], equals('match3'));
      expect(matchUpdatedEvents.first.data['id'], equals('match2'));

      await subscription1.cancel();
      await subscription2.cancel();
    });

    test('should handle multiple subscribers', () async {
      // Arrange
      final eventBus = EventBus();
      final subscriber1Events = <GameEvent>[];
      final subscriber2Events = <GameEvent>[];

      final subscription1 = eventBus.events.listen(subscriber1Events.add);
      final subscription2 = eventBus.events.listen(subscriber2Events.add);

      final event = GameEvent('test_event_multi', {'key': 'value'});

      // Act
      eventBus.publish(event);

      // Wait for events to be processed
      await Future.delayed(Duration.zero);

      // Assert
      expect(subscriber1Events, hasLength(1));
      expect(subscriber2Events, hasLength(1));
      expect(subscriber1Events.first.type, equals('test_event_multi'));
      expect(subscriber2Events.first.type, equals('test_event_multi'));

      await subscription1.cancel();
      await subscription2.cancel();
    });

    test('should handle basic event publishing without conflicts', () async {
      // Arrange
      final eventBus = EventBus();
      final receivedEvents = <GameEvent>[];
      final subscription = eventBus.events.listen(receivedEvents.add);

      // Act
      eventBus.publish(GameEvent('simple_test', {'data': 'test'}));

      // Wait for event to be processed
      await Future.delayed(Duration.zero);

      // Assert
      expect(receivedEvents, hasLength(1));
      expect(receivedEvents.first.type, equals('simple_test'));

      await subscription.cancel();
    });
  });
}
