import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:mocktail/mocktail.dart';

/// Mock implementation of GameMatchController for testing
class MockGameMatchController extends Mo<PERSON> implements GameMatchController {}

/// Test data factory for creating sample match data
class TestMatchData {
  /// Create a sample match data map
  static Map<String, dynamic> createMatch({
    String? id,
    String? gameTypeId,
    String? creatorId,
    String? gameName,
    int? createdAt,
    int? updatedAt,
    String? status,
    bool? isOpenForJoining,
    List<Map<String, dynamic>>? playerSlots,
    List<Map<String, dynamic>>? players,
    List<Map<String, dynamic>>? turns,
    int? currentTurn,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return {
      'id': id ?? 'test-match-id',
      'gameTypeId': gameTypeId ?? 'test-game-type',
      'creatorId': creatorId ?? 'test-creator-id',
      if (gameName != null) 'gameName': gameName,
      'created_at': createdAt ?? now,
      'updated_at': updatedAt ?? now,
      'status': status ?? 'open',
      'is_open_for_joining': isOpenForJoining ?? true,
      'playerSlots': playerSlots ?? [],
      'players': players ?? [],
      'turns': turns ?? [],
      'currentTurn': currentTurn ?? 0,
    };
  }

  /// Create sample open matches list
  static List<Map<String, dynamic>> createOpenMatches({int count = 3}) {
    return List.generate(
      count,
      (index) => createMatch(
        id: 'open-match-$index',
        gameName: 'Open Match $index',
        status: 'open',
        isOpenForJoining: true,
      ),
    );
  }

  /// Create sample all matches list (including non-open)
  static List<Map<String, dynamic>> createAllMatches({int count = 5}) {
    final matches = <Map<String, dynamic>>[];
    
    // Add some open matches
    matches.addAll(createOpenMatches(count: 2));
    
    // Add some active matches
    for (int i = 0; i < count - 2; i++) {
      matches.add(createMatch(
        id: 'active-match-$i',
        gameName: 'Active Match $i',
        status: 'active',
        isOpenForJoining: false,
      ));
    }
    
    return matches;
  }

  /// Create sample match creation request data
  static Map<String, dynamic> createMatchRequest({
    String? gameTypeId,
    String? creatorId,
    String? gameName,
    List<Map<String, dynamic>>? playerSlots,
  }) {
    return {
      'gameTypeId': gameTypeId ?? 'test-game-type',
      'creatorId': creatorId ?? 'test-creator-id',
      if (gameName != null) 'gameName': gameName,
      if (playerSlots != null) 'playerSlots': playerSlots,
    };
  }

  /// Create sample player data
  static Map<String, dynamic> createPlayer({
    String? id,
    String? name,
    String? type,
    Map<String, dynamic>? metadata,
  }) {
    return {
      'id': id ?? 'test-player-id',
      if (name != null) 'name': name,
      'type': type ?? 'humanNetwork',
      'metadata': metadata ?? {},
    };
  }

  /// Create sample turn data
  static Map<String, dynamic> createTurn({
    String? playerId,
    String? move,
    String? timestamp,
    int? turnNumber,
    Map<String, dynamic>? metadata,
  }) {
    return {
      'playerId': playerId ?? 'test-player-id',
      'move': move ?? 'test-move-data',
      'timestamp': timestamp ?? DateTime.now().toIso8601String(),
      'turnNumber': turnNumber ?? 1,
      'metadata': metadata ?? {},
    };
  }
}
