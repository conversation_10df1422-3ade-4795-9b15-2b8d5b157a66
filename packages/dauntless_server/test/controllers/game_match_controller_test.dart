import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:dauntless_server/repositories/game_match_repository.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../test_helpers/mock_controllers.dart';

class _MockGameMatchRepository extends Mo<PERSON> implements GameMatchRepository {}

void main() {
  group('GameMatchController', () {
    late GameMatchController controller;
    late _MockGameMatchRepository mockRepository;

    setUp(() {
      mockRepository = _MockGameMatchRepository();
      controller = GameMatchController(matchRepository: mockRepository);
    });

    group('getMatches', () {
      test('should return all matches from repository', () async {
        // Arrange
        final expectedMatches = TestMatchData.createAllMatches(count: 3);
        when(() => mockRepository.getMatches()).thenAnswer((_) async => expectedMatches);

        // Act
        final result = await controller.getMatches();

        // Assert
        expect(result, equals(expectedMatches));
        verify(() => mockRepository.getMatches()).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        when(() => mockRepository.getMatches()).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(() => controller.getMatches(), throwsException);
        verify(() => mockRepository.getMatches()).called(1);
      });

      test('should return empty list when no matches', () async {
        // Arrange
        when(() => mockRepository.getMatches()).thenAnswer((_) async => []);

        // Act
        final result = await controller.getMatches();

        // Assert
        expect(result, isEmpty);
        verify(() => mockRepository.getMatches()).called(1);
      });
    });

    group('getOpenMatches', () {
      test('should return open matches from repository', () async {
        // Arrange
        final expectedMatches = TestMatchData.createOpenMatches(count: 2);
        when(() => mockRepository.getOpenMatches()).thenAnswer((_) async => expectedMatches);

        // Act
        final result = await controller.getOpenMatches();

        // Assert
        expect(result, equals(expectedMatches));
        expect(result.length, equals(2));
        verify(() => mockRepository.getOpenMatches()).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        when(() => mockRepository.getOpenMatches()).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(() => controller.getOpenMatches(), throwsException);
        verify(() => mockRepository.getOpenMatches()).called(1);
      });
    });

    group('getMatch', () {
      test('should return specific match by ID', () async {
        // Arrange
        const matchId = 'test-match-123';
        final expectedMatch = TestMatchData.createMatch(id: matchId);
        when(() => mockRepository.getMatch(matchId)).thenAnswer((_) async => expectedMatch);

        // Act
        final result = await controller.getMatch(matchId);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result!['id'], equals(matchId));
        verify(() => mockRepository.getMatch(matchId)).called(1);
      });

      test('should return null when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        when(() => mockRepository.getMatch(matchId)).thenAnswer((_) async => null);

        // Act
        final result = await controller.getMatch(matchId);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.getMatch(matchId)).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        const matchId = 'error-match';
        when(() => mockRepository.getMatch(matchId)).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(() => controller.getMatch(matchId), throwsException);
        verify(() => mockRepository.getMatch(matchId)).called(1);
      });
    });

    group('createMatch', () {
      test('should create new match successfully', () async {
        // Arrange
        final matchData = TestMatchData.createMatchRequest(
          gameTypeId: 'liberator',
          creatorId: 'player-123',
          gameName: 'Test Match',
        );
        final expectedMatch = TestMatchData.createMatch(
          id: 'new-match-id',
          gameTypeId: 'liberator',
          creatorId: 'player-123',
          gameName: 'Test Match',
        );
        when(() => mockRepository.createMatch(matchData)).thenAnswer((_) async => expectedMatch);

        // Act
        final result = await controller.createMatch(matchData);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result['gameTypeId'], equals('liberator'));
        expect(result['gameName'], equals('Test Match'));
        verify(() => mockRepository.createMatch(matchData)).called(1);
      });

      test('should handle repository errors during creation', () async {
        // Arrange
        final matchData = TestMatchData.createMatchRequest();
        when(() => mockRepository.createMatch(matchData)).thenThrow(Exception('Creation failed'));

        // Act & Assert
        expect(() => controller.createMatch(matchData), throwsException);
        verify(() => mockRepository.createMatch(matchData)).called(1);
      });

      test('should handle empty match data', () async {
        // Arrange
        final emptyData = <String, dynamic>{};
        when(() => mockRepository.createMatch(emptyData)).thenThrow(Exception('Invalid data'));

        // Act & Assert
        expect(() => controller.createMatch(emptyData), throwsException);
        verify(() => mockRepository.createMatch(emptyData)).called(1);
      });
    });

    group('joinMatch', () {
      test('should join match successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        final playerData = TestMatchData.createPlayer(id: 'player-456');
        final expectedMatch = TestMatchData.createMatch(
          id: matchId,
          players: [playerData],
        );
        when(() => mockRepository.joinMatch(matchId, playerData))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await controller.joinMatch(matchId, playerData);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result!['players'], hasLength(1));
        verify(() => mockRepository.joinMatch(matchId, playerData)).called(1);
      });

      test('should return null when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        final playerData = TestMatchData.createPlayer();
        when(() => mockRepository.joinMatch(matchId, playerData))
            .thenAnswer((_) async => null);

        // Act
        final result = await controller.joinMatch(matchId, playerData);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.joinMatch(matchId, playerData)).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        const matchId = 'error-match';
        final playerData = TestMatchData.createPlayer();
        when(() => mockRepository.joinMatch(matchId, playerData))
            .thenThrow(Exception('Join failed'));

        // Act & Assert
        expect(() => controller.joinMatch(matchId, playerData), throwsException);
        verify(() => mockRepository.joinMatch(matchId, playerData)).called(1);
      });
    });

    group('submitTurn', () {
      test('should submit turn successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        final turnData = TestMatchData.createTurn(playerId: 'player-456');
        final expectedResult = {
          'success': true,
          'game_match': TestMatchData.createMatch(id: matchId),
          'all_turns_submitted': false,
          'turnNumber': 1,
          'type': 'turn_submission',
        };
        when(() => mockRepository.submitTurn(matchId, turnData))
            .thenAnswer((_) async => expectedResult);

        // Act
        final result = await controller.submitTurn(matchId, turnData);

        // Assert
        expect(result, equals(expectedResult));
        expect(result!['success'], isTrue);
        verify(() => mockRepository.submitTurn(matchId, turnData)).called(1);
      });

      test('should return null when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        final turnData = TestMatchData.createTurn();
        when(() => mockRepository.submitTurn(matchId, turnData))
            .thenAnswer((_) async => null);

        // Act
        final result = await controller.submitTurn(matchId, turnData);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.submitTurn(matchId, turnData)).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        const matchId = 'error-match';
        final turnData = TestMatchData.createTurn();
        when(() => mockRepository.submitTurn(matchId, turnData))
            .thenThrow(Exception('Turn submission failed'));

        // Act & Assert
        expect(() => controller.submitTurn(matchId, turnData), throwsException);
        verify(() => mockRepository.submitTurn(matchId, turnData)).called(1);
      });
    });

    group('updateMatchStatus', () {
      test('should update match status successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        final statusData = {'status': 'active'};
        final expectedMatch = TestMatchData.createMatch(id: matchId, status: 'active');
        when(() => mockRepository.updateMatchStatus(matchId, statusData))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await controller.updateMatchStatus(matchId, statusData);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result!['status'], equals('active'));
        verify(() => mockRepository.updateMatchStatus(matchId, statusData)).called(1);
      });

      test('should return null when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        final statusData = {'status': 'active'};
        when(() => mockRepository.updateMatchStatus(matchId, statusData))
            .thenAnswer((_) async => null);

        // Act
        final result = await controller.updateMatchStatus(matchId, statusData);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.updateMatchStatus(matchId, statusData)).called(1);
      });
    });

    group('deleteMatch', () {
      test('should delete match successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        when(() => mockRepository.deleteMatch(matchId)).thenAnswer((_) async => true);

        // Act
        final result = await controller.deleteMatch(matchId);

        // Assert
        expect(result, isTrue);
        verify(() => mockRepository.deleteMatch(matchId)).called(1);
      });

      test('should return false when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        when(() => mockRepository.deleteMatch(matchId)).thenAnswer((_) async => false);

        // Act
        final result = await controller.deleteMatch(matchId);

        // Assert
        expect(result, isFalse);
        verify(() => mockRepository.deleteMatch(matchId)).called(1);
      });

      test('should handle repository errors', () async {
        // Arrange
        const matchId = 'error-match';
        when(() => mockRepository.deleteMatch(matchId)).thenThrow(Exception('Delete failed'));

        // Act & Assert
        expect(() => controller.deleteMatch(matchId), throwsException);
        verify(() => mockRepository.deleteMatch(matchId)).called(1);
      });
    });

    group('leaveMatch', () {
      test('should leave match successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        final playerData = TestMatchData.createPlayer(id: 'player-456');
        final expectedMatch = TestMatchData.createMatch(id: matchId, players: []);
        when(() => mockRepository.leaveMatch(matchId, playerData))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await controller.leaveMatch(matchId, playerData);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result!['players'], isEmpty);
        verify(() => mockRepository.leaveMatch(matchId, playerData)).called(1);
      });

      test('should return null when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        final playerData = TestMatchData.createPlayer();
        when(() => mockRepository.leaveMatch(matchId, playerData))
            .thenAnswer((_) async => null);

        // Act
        final result = await controller.leaveMatch(matchId, playerData);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.leaveMatch(matchId, playerData)).called(1);
      });
    });

    group('updateGameName', () {
      test('should update game name successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        const newGameName = 'Updated Game Name';
        final nameData = {'gameName': newGameName};
        final expectedMatch = TestMatchData.createMatch(id: matchId, gameName: newGameName);
        when(() => mockRepository.updateGameName(matchId, newGameName))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await controller.updateGameName(matchId, nameData);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result!['gameName'], equals(newGameName));
        verify(() => mockRepository.updateGameName(matchId, newGameName)).called(1);
      });

      test('should return null when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        final nameData = {'gameName': 'New Name'};
        when(() => mockRepository.updateGameName(matchId, 'New Name'))
            .thenAnswer((_) async => null);

        // Act
        final result = await controller.updateGameName(matchId, nameData);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.updateGameName(matchId, 'New Name')).called(1);
      });
    });

    group('openMatchForJoining', () {
      test('should open match for joining successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        final expectedMatch = TestMatchData.createMatch(
          id: matchId,
          isOpenForJoining: true,
        );
        when(() => mockRepository.openMatchForJoining(matchId))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await controller.openMatchForJoining(matchId);

        // Assert
        expect(result, equals(expectedMatch));
        expect(result!['is_open_for_joining'], isTrue);
        verify(() => mockRepository.openMatchForJoining(matchId)).called(1);
      });

      test('should return null when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        when(() => mockRepository.openMatchForJoining(matchId))
            .thenAnswer((_) async => null);

        // Act
        final result = await controller.openMatchForJoining(matchId);

        // Assert
        expect(result, isNull);
        verify(() => mockRepository.openMatchForJoining(matchId)).called(1);
      });
    });

    group('onChange stream', () {
      test('should expose repository onChange stream', () {
        // Arrange
        final mockStream = Stream<void>.empty();
        when(() => mockRepository.onChange).thenAnswer((_) => mockStream);

        // Act
        final result = controller.onMatchesChange;

        // Assert
        expect(result, equals(mockStream));
        verify(() => mockRepository.onChange).called(1);
      });
    });
  });
}
