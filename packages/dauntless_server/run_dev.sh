#!/bin/bash

# Kill any existing dart_frog processes
echo "Checking for existing dart_frog processes..."
PIDS=$(ps aux | grep "dart_frog dev" | grep -v grep | awk '{print $2}')

if [ -n "$PIDS" ]; then
  echo "Killing existing dart_frog processes: $PIDS"
  kill $PIDS
  # Give processes time to shut down
  sleep 2
  
  # Check if any processes are still running and force kill if necessary
  REMAINING=$(ps aux | grep "dart_frog dev" | grep -v grep | awk '{print $2}')
  if [ -n "$REMAINING" ]; then
    echo "Force killing remaining processes: $REMAINING"
    kill -9 $REMAINING
    sleep 1
  fi
else
  echo "No existing dart_frog processes found."
fi

# Start the server with required language feature flags
export DART_VM_OPTIONS="--enable-experiment=class-modifiers"
echo "Starting dart_frog server on port 2187..."
nohup dart_frog dev --hostname 0.0.0.0 --port 2187 < /dev/null > dart_frog.log 2>&1 &
# dart_frog dev --port 2187

# Print the new process ID for reference
NEW_PID=$!
echo "Server started with PID: $NEW_PID"
echo "Logs available at: $(pwd)/dart_frog.log"

