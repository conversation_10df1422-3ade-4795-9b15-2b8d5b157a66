import 'package:json_annotation/json_annotation.dart';

/// Converter that provides the current timestamp if none is provided
class DefaultTimestampConverter implements JsonConverter<int, Object?> {
  const DefaultTimestampConverter();

  @override
  int fromJson([Object? json]) {
    if (json is int) {
      return json;
    }
    // Default to current time in milliseconds since epoch
    return DateTime.now().millisecondsSinceEpoch;
  }

  @override
  Object toJson(int object) {
    return object;
  }
}
