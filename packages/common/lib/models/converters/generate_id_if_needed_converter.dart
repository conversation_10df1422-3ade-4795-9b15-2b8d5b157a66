import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';

class GenerateIdIfNeededConverter implements JsonConverter<String, Object?> {
  const GenerateIdIfNeededConverter();

  @override
  String fromJson([Object? json]) {
    if (json is String) {
      return json;
    }
    return const Uuid().v4();
  }

  @override
  Object toJson(String object) {
    return object;
  }
}
