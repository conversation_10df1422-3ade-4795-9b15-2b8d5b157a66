// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'player_slot.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PlayerSlot {
  String get id;
  String? get playerId;
  @JsonKey(fromJson: _playerTypeFromJson, toJson: _playerTypeToJson)
  PlayerType get type;
  String get playerClassId;
  String? get name;

  /// Create a copy of PlayerSlot
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PlayerSlotCopyWith<PlayerSlot> get copyWith =>
      _$PlayerSlotCopyWithImpl<PlayerSlot>(this as PlayerSlot, _$identity);

  /// Serializes this PlayerSlot to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PlayerSlot &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.playerClassId, playerClassId) ||
                other.playerClassId == playerClassId) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, playerId, type, playerClassId, name);

  @override
  String toString() {
    return 'PlayerSlot(id: $id, playerId: $playerId, type: $type, playerClassId: $playerClassId, name: $name)';
  }
}

/// @nodoc
abstract mixin class $PlayerSlotCopyWith<$Res> {
  factory $PlayerSlotCopyWith(
          PlayerSlot value, $Res Function(PlayerSlot) _then) =
      _$PlayerSlotCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? playerId,
      @JsonKey(fromJson: _playerTypeFromJson, toJson: _playerTypeToJson)
      PlayerType type,
      String playerClassId,
      String? name});
}

/// @nodoc
class _$PlayerSlotCopyWithImpl<$Res> implements $PlayerSlotCopyWith<$Res> {
  _$PlayerSlotCopyWithImpl(this._self, this._then);

  final PlayerSlot _self;
  final $Res Function(PlayerSlot) _then;

  /// Create a copy of PlayerSlot
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? playerId = freezed,
    Object? type = null,
    Object? playerClassId = null,
    Object? name = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as PlayerType,
      playerClassId: null == playerClassId
          ? _self.playerClassId
          : playerClassId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PlayerSlot implements PlayerSlot {
  const _PlayerSlot(
      {required this.id,
      this.playerId,
      @JsonKey(fromJson: _playerTypeFromJson, toJson: _playerTypeToJson)
      required this.type,
      required this.playerClassId,
      this.name});
  factory _PlayerSlot.fromJson(Map<String, dynamic> json) =>
      _$PlayerSlotFromJson(json);

  @override
  final String id;
  @override
  final String? playerId;
  @override
  @JsonKey(fromJson: _playerTypeFromJson, toJson: _playerTypeToJson)
  final PlayerType type;
  @override
  final String playerClassId;
  @override
  final String? name;

  /// Create a copy of PlayerSlot
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PlayerSlotCopyWith<_PlayerSlot> get copyWith =>
      __$PlayerSlotCopyWithImpl<_PlayerSlot>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PlayerSlotToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PlayerSlot &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.playerClassId, playerClassId) ||
                other.playerClassId == playerClassId) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, playerId, type, playerClassId, name);

  @override
  String toString() {
    return 'PlayerSlot(id: $id, playerId: $playerId, type: $type, playerClassId: $playerClassId, name: $name)';
  }
}

/// @nodoc
abstract mixin class _$PlayerSlotCopyWith<$Res>
    implements $PlayerSlotCopyWith<$Res> {
  factory _$PlayerSlotCopyWith(
          _PlayerSlot value, $Res Function(_PlayerSlot) _then) =
      __$PlayerSlotCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? playerId,
      @JsonKey(fromJson: _playerTypeFromJson, toJson: _playerTypeToJson)
      PlayerType type,
      String playerClassId,
      String? name});
}

/// @nodoc
class __$PlayerSlotCopyWithImpl<$Res> implements _$PlayerSlotCopyWith<$Res> {
  __$PlayerSlotCopyWithImpl(this._self, this._then);

  final _PlayerSlot _self;
  final $Res Function(_PlayerSlot) _then;

  /// Create a copy of PlayerSlot
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? playerId = freezed,
    Object? type = null,
    Object? playerClassId = null,
    Object? name = freezed,
  }) {
    return _then(_PlayerSlot(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as PlayerType,
      playerClassId: null == playerClassId
          ? _self.playerClassId
          : playerClassId // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
