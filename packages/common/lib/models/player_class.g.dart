// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'player_class.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PlayerClass _$PlayerClassFromJson(Map<String, dynamic> json) => _PlayerClass(
      id: json['id'] as String,
      name: json['name'] as String,
      defaultPlayerName: json['defaultPlayerName'] as String?,
      numAllowed: (json['numAllowed'] as num?)?.toInt(),
      numRequired: (json['numRequired'] as num?)?.toInt(),
      icon: json['icon'] as String?,
      colorScheme: json['colorScheme'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PlayerClassToJson(_PlayerClass instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'defaultPlayerName': instance.defaultPlayerName,
      'numAllowed': instance.numAllowed,
      'numRequired': instance.numRequired,
      'icon': instance.icon,
      'colorScheme': instance.colorScheme,
    };
