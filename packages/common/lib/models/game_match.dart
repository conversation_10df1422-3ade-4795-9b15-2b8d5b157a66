import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:common/models/converters/default_timestamp_converter.dart';
import 'package:common/models/player_slot.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'game.dart';
import 'match_status.dart';
import 'player.dart';
import 'turn.dart';

part 'game_match.freezed.dart';
part 'game_match.g.dart';

/// Model representing a game_match instance
/// NOTE: named 'GameMatch' instead of 'Match' b/c 'Match' is regularly used for RegExp type matches
@freezed
abstract class GameMatch with _$GameMatch {
  /// Default constructor
  const factory GameMatch({
    /// Unique identifier for the game_match
    @GenerateIdIfNeededConverter() required GameMatchId id,

    /// Type of game this game_match is for
    @JsonKey(name: 'gameTypeId') required GameTypeId gameTypeId,

    /// ID of the player who created the game_match
    required String creatorId,

    /// Optional custom name for the game
    String? gameName,

    /// Created timestamp
    @DefaultTimestampConverter()
    @JsonKey(name: 'created_at') required int createdAt,

    /// Last updated timestamp
    @DefaultTimestampConverter()
    @JsonKey(name: 'updated_at') required int updatedAt,

    /// Current status of the game_match
    @JsonKey(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
    @Default(MatchStatus.open)
    MatchStatus status,

    /// Flag indicating if the match is open for new players to join
    @JsonKey(name: 'is_open_for_joining') @Default(false) bool isOpenForJoining,

    /// List of player slots in the match (from server)
    @Default([]) List<PlayerSlot> playerSlots,

    /// List of players in the game_match
    @Default([]) List<Player> players,

    /// List of turns that have occurred in the game_match
    @Default([]) List<Turn> turns,

    /// Current turn number
    @JsonKey(name: 'currentTurn', defaultValue: 0) @Default(0) int currentTurn,
  }) = _GameMatch;

  /// Create from JSON
  factory GameMatch.fromJson(Map<String, dynamic> json) =>
      _$GameMatchFromJson(json);
}

// Helper function to convert string status to enum
MatchStatus _statusFromJson(String status) {
  switch (status) {
    case 'active':
      return MatchStatus.active;
    case 'closed':
      return MatchStatus.closed;
    case 'open':
    default:
      return MatchStatus.open;
  }
}

// Helper function to convert enum status to string
String _statusToJson(MatchStatus status) {
  switch (status) {
    case MatchStatus.active:
      return 'active';
    case MatchStatus.closed:
      return 'closed';
    case MatchStatus.open:
      return 'open';
  }
}
