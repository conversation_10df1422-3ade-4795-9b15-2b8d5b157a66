// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'turn.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Turn {
  /// ID of the player making the turn
  String get playerId;

  /// The actual move data
  String get move;

  /// Timestamp when the turn was submitted
  String get timestamp;

  /// Turn number in the sequence
  int get turnNumber;

  /// Additional turn metadata
  Map<String, dynamic> get metadata;

  /// Create a copy of Turn
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TurnCopyWith<Turn> get copyWith =>
      _$TurnCopyWithImpl<Turn>(this as Turn, _$identity);

  /// Serializes this Turn to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Turn &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.move, move) || other.move == move) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.turnNumber, turnNumber) ||
                other.turnNumber == turnNumber) &&
            const DeepCollectionEquality().equals(other.metadata, metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, playerId, move, timestamp,
      turnNumber, const DeepCollectionEquality().hash(metadata));

  @override
  String toString() {
    return 'Turn(playerId: $playerId, move: $move, timestamp: $timestamp, turnNumber: $turnNumber, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class $TurnCopyWith<$Res> {
  factory $TurnCopyWith(Turn value, $Res Function(Turn) _then) =
      _$TurnCopyWithImpl;
  @useResult
  $Res call(
      {String playerId,
      String move,
      String timestamp,
      int turnNumber,
      Map<String, dynamic> metadata});
}

/// @nodoc
class _$TurnCopyWithImpl<$Res> implements $TurnCopyWith<$Res> {
  _$TurnCopyWithImpl(this._self, this._then);

  final Turn _self;
  final $Res Function(Turn) _then;

  /// Create a copy of Turn
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? playerId = null,
    Object? move = null,
    Object? timestamp = null,
    Object? turnNumber = null,
    Object? metadata = null,
  }) {
    return _then(_self.copyWith(
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String,
      move: null == move
          ? _self.move
          : move // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as String,
      turnNumber: null == turnNumber
          ? _self.turnNumber
          : turnNumber // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: null == metadata
          ? _self.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Turn implements Turn {
  const _Turn(
      {required this.playerId,
      required this.move,
      required this.timestamp,
      required this.turnNumber,
      final Map<String, dynamic> metadata = const {}})
      : _metadata = metadata;
  factory _Turn.fromJson(Map<String, dynamic> json) => _$TurnFromJson(json);

  /// ID of the player making the turn
  @override
  final String playerId;

  /// The actual move data
  @override
  final String move;

  /// Timestamp when the turn was submitted
  @override
  final String timestamp;

  /// Turn number in the sequence
  @override
  final int turnNumber;

  /// Additional turn metadata
  final Map<String, dynamic> _metadata;

  /// Additional turn metadata
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  /// Create a copy of Turn
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TurnCopyWith<_Turn> get copyWith =>
      __$TurnCopyWithImpl<_Turn>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TurnToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Turn &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.move, move) || other.move == move) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.turnNumber, turnNumber) ||
                other.turnNumber == turnNumber) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, playerId, move, timestamp,
      turnNumber, const DeepCollectionEquality().hash(_metadata));

  @override
  String toString() {
    return 'Turn(playerId: $playerId, move: $move, timestamp: $timestamp, turnNumber: $turnNumber, metadata: $metadata)';
  }
}

/// @nodoc
abstract mixin class _$TurnCopyWith<$Res> implements $TurnCopyWith<$Res> {
  factory _$TurnCopyWith(_Turn value, $Res Function(_Turn) _then) =
      __$TurnCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String playerId,
      String move,
      String timestamp,
      int turnNumber,
      Map<String, dynamic> metadata});
}

/// @nodoc
class __$TurnCopyWithImpl<$Res> implements _$TurnCopyWith<$Res> {
  __$TurnCopyWithImpl(this._self, this._then);

  final _Turn _self;
  final $Res Function(_Turn) _then;

  /// Create a copy of Turn
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? playerId = null,
    Object? move = null,
    Object? timestamp = null,
    Object? turnNumber = null,
    Object? metadata = null,
  }) {
    return _then(_Turn(
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String,
      move: null == move
          ? _self.move
          : move // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as String,
      turnNumber: null == turnNumber
          ? _self.turnNumber
          : turnNumber // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: null == metadata
          ? _self._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

// dart format on
