// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_match.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GameMatch _$GameMatchFromJson(Map<String, dynamic> json) => _GameMatch(
      id: const GenerateIdIfNeededConverter().fromJson(json['id']),
      gameTypeId: json['gameTypeId'] as String,
      creatorId: json['creatorId'] as String,
      gameName: json['gameName'] as String?,
      createdAt: const DefaultTimestampConverter().fromJson(json['created_at']),
      updatedAt: const DefaultTimestampConverter().from<PERSON>son(json['updated_at']),
      status: json['status'] == null
          ? MatchStatus.open
          : _statusFromJson(json['status'] as String),
      isOpenForJoining: json['is_open_for_joining'] as bool? ?? false,
      playerSlots: (json['playerSlots'] as List<dynamic>?)
              ?.map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      players: (json['players'] as List<dynamic>?)
              ?.map((e) => Player.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      turns: (json['turns'] as List<dynamic>?)
              ?.map((e) => Turn.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      currentTurn: (json['currentTurn'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$GameMatchToJson(_GameMatch instance) =>
    <String, dynamic>{
      'id': const GenerateIdIfNeededConverter().toJson(instance.id),
      'gameTypeId': instance.gameTypeId,
      'creatorId': instance.creatorId,
      'gameName': instance.gameName,
      'created_at':
          const DefaultTimestampConverter().toJson(instance.createdAt),
      'updated_at':
          const DefaultTimestampConverter().toJson(instance.updatedAt),
      'status': _statusToJson(instance.status),
      'is_open_for_joining': instance.isOpenForJoining,
      'playerSlots': instance.playerSlots,
      'players': instance.players,
      'turns': instance.turns,
      'currentTurn': instance.currentTurn,
    };
