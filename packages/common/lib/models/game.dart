import 'package:freezed_annotation/freezed_annotation.dart';

part 'game.freezed.dart';
part 'game.g.dart';

typedef GameMatchId = String;
typedef GameTypeId = String;

/// Model representing a game configuration
@freezed
abstract class Game with _$Game {
  /// Default constructor
  const factory Game({
    /// Unique identifier for the game
    required String id,
    /// Name of the game
    required String name,
    /// Description of the game
    String? description,
    /// Configuration options for the game
    required Map<String, dynamic> config,
  }) = _Game;

  /// Create from JSON
  factory Game.fromJson(Map<String, dynamic> json) => _$GameFrom<PERSON>son(json);
}
