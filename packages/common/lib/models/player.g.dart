// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'player.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Player _$Player<PERSON>rom<PERSON>son(Map<String, dynamic> json) => _Player(
      id: json['id'] as String,
      name: json['name'] as String?,
      type: $enumDecodeNullable(_$PlayerTypeEnumMap, json['type']) ??
          PlayerType.humanNetwork,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$PlayerTo<PERSON>son(_Player instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$PlayerTypeEnumMap[instance.type]!,
      'metadata': instance.metadata,
    };

const _$PlayerTypeEnumMap = {
  PlayerType.humanLocal: 'humanLocal',
  PlayerType.humanNetwork: 'humanNetwork',
  PlayerType.botLocal: 'botLocal',
  PlayerType.botNetwork: 'botNetwork',
};
