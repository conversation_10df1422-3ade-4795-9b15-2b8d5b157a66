// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'player_class.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PlayerClass {
  String get id;
  String get name;
  String? get defaultPlayerName;
  int? get numAllowed;
  int? get numRequired;
  String? get icon;
  Map<String, dynamic>? get colorScheme;

  /// Create a copy of PlayerClass
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PlayerClassCopyWith<PlayerClass> get copyWith =>
      _$PlayerClassCopyWithImpl<PlayerClass>(this as PlayerClass, _$identity);

  /// Serializes this PlayerClass to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PlayerClass &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.defaultPlayerName, defaultPlayerName) ||
                other.defaultPlayerName == defaultPlayerName) &&
            (identical(other.numAllowed, numAllowed) ||
                other.numAllowed == numAllowed) &&
            (identical(other.numRequired, numRequired) ||
                other.numRequired == numRequired) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            const DeepCollectionEquality()
                .equals(other.colorScheme, colorScheme));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      defaultPlayerName,
      numAllowed,
      numRequired,
      icon,
      const DeepCollectionEquality().hash(colorScheme));

  @override
  String toString() {
    return 'PlayerClass(id: $id, name: $name, defaultPlayerName: $defaultPlayerName, numAllowed: $numAllowed, numRequired: $numRequired, icon: $icon, colorScheme: $colorScheme)';
  }
}

/// @nodoc
abstract mixin class $PlayerClassCopyWith<$Res> {
  factory $PlayerClassCopyWith(
          PlayerClass value, $Res Function(PlayerClass) _then) =
      _$PlayerClassCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String name,
      String? defaultPlayerName,
      int? numAllowed,
      int? numRequired,
      String? icon,
      Map<String, dynamic>? colorScheme});
}

/// @nodoc
class _$PlayerClassCopyWithImpl<$Res> implements $PlayerClassCopyWith<$Res> {
  _$PlayerClassCopyWithImpl(this._self, this._then);

  final PlayerClass _self;
  final $Res Function(PlayerClass) _then;

  /// Create a copy of PlayerClass
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? defaultPlayerName = freezed,
    Object? numAllowed = freezed,
    Object? numRequired = freezed,
    Object? icon = freezed,
    Object? colorScheme = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      defaultPlayerName: freezed == defaultPlayerName
          ? _self.defaultPlayerName
          : defaultPlayerName // ignore: cast_nullable_to_non_nullable
              as String?,
      numAllowed: freezed == numAllowed
          ? _self.numAllowed
          : numAllowed // ignore: cast_nullable_to_non_nullable
              as int?,
      numRequired: freezed == numRequired
          ? _self.numRequired
          : numRequired // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _self.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      colorScheme: freezed == colorScheme
          ? _self.colorScheme
          : colorScheme // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PlayerClass implements PlayerClass {
  const _PlayerClass(
      {required this.id,
      required this.name,
      this.defaultPlayerName,
      this.numAllowed,
      this.numRequired,
      this.icon,
      final Map<String, dynamic>? colorScheme})
      : _colorScheme = colorScheme;
  factory _PlayerClass.fromJson(Map<String, dynamic> json) =>
      _$PlayerClassFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? defaultPlayerName;
  @override
  final int? numAllowed;
  @override
  final int? numRequired;
  @override
  final String? icon;
  final Map<String, dynamic>? _colorScheme;
  @override
  Map<String, dynamic>? get colorScheme {
    final value = _colorScheme;
    if (value == null) return null;
    if (_colorScheme is EqualUnmodifiableMapView) return _colorScheme;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of PlayerClass
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PlayerClassCopyWith<_PlayerClass> get copyWith =>
      __$PlayerClassCopyWithImpl<_PlayerClass>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PlayerClassToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PlayerClass &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.defaultPlayerName, defaultPlayerName) ||
                other.defaultPlayerName == defaultPlayerName) &&
            (identical(other.numAllowed, numAllowed) ||
                other.numAllowed == numAllowed) &&
            (identical(other.numRequired, numRequired) ||
                other.numRequired == numRequired) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            const DeepCollectionEquality()
                .equals(other._colorScheme, _colorScheme));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      defaultPlayerName,
      numAllowed,
      numRequired,
      icon,
      const DeepCollectionEquality().hash(_colorScheme));

  @override
  String toString() {
    return 'PlayerClass(id: $id, name: $name, defaultPlayerName: $defaultPlayerName, numAllowed: $numAllowed, numRequired: $numRequired, icon: $icon, colorScheme: $colorScheme)';
  }
}

/// @nodoc
abstract mixin class _$PlayerClassCopyWith<$Res>
    implements $PlayerClassCopyWith<$Res> {
  factory _$PlayerClassCopyWith(
          _PlayerClass value, $Res Function(_PlayerClass) _then) =
      __$PlayerClassCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String? defaultPlayerName,
      int? numAllowed,
      int? numRequired,
      String? icon,
      Map<String, dynamic>? colorScheme});
}

/// @nodoc
class __$PlayerClassCopyWithImpl<$Res> implements _$PlayerClassCopyWith<$Res> {
  __$PlayerClassCopyWithImpl(this._self, this._then);

  final _PlayerClass _self;
  final $Res Function(_PlayerClass) _then;

  /// Create a copy of PlayerClass
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? defaultPlayerName = freezed,
    Object? numAllowed = freezed,
    Object? numRequired = freezed,
    Object? icon = freezed,
    Object? colorScheme = freezed,
  }) {
    return _then(_PlayerClass(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      defaultPlayerName: freezed == defaultPlayerName
          ? _self.defaultPlayerName
          : defaultPlayerName // ignore: cast_nullable_to_non_nullable
              as String?,
      numAllowed: freezed == numAllowed
          ? _self.numAllowed
          : numAllowed // ignore: cast_nullable_to_non_nullable
              as int?,
      numRequired: freezed == numRequired
          ? _self.numRequired
          : numRequired // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _self.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      colorScheme: freezed == colorScheme
          ? _self._colorScheme
          : colorScheme // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

// dart format on
