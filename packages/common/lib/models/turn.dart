import 'package:freezed_annotation/freezed_annotation.dart';

part 'turn.freezed.dart';
part 'turn.g.dart';

/// Model representing a turn in a game_match
@freezed
abstract class Turn with _$Turn {
  /// Default constructor
  const factory Turn({
    /// ID of the player making the turn
    required String playerId,
    /// The actual move data
    required String move,
    /// Timestamp when the turn was submitted
    required String timestamp,
    /// Turn number in the sequence
    required int turnNumber,
    /// Additional turn metadata
    @Default({})
    Map<String, dynamic> metadata,
  }) = _Turn;

  /// Create from JSON
  factory Turn.fromJson(Map<String, dynamic> json) => _$TurnFromJson(json);
}
