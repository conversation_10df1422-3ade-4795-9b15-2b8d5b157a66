// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_match.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GameMatch {
  /// Unique identifier for the game_match
  @GenerateIdIfNeededConverter()
  GameMatchId get id;

  /// Type of game this game_match is for
  @JsonKey(name: 'gameTypeId')
  GameTypeId get gameTypeId;

  /// ID of the player who created the game_match
  String get creatorId;

  /// Optional custom name for the game
  String? get gameName;

  /// Created timestamp
  @DefaultTimestampConverter()
  @JsonKey(name: 'created_at')
  int get createdAt;

  /// Last updated timestamp
  @DefaultTimestampConverter()
  @JsonKey(name: 'updated_at')
  int get updatedAt;

  /// Current status of the game_match
  @JsonKey(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
  MatchStatus get status;

  /// Flag indicating if the match is open for new players to join
  @JsonKey(name: 'is_open_for_joining')
  bool get isOpenForJoining;

  /// List of player slots in the match (from server)
  List<PlayerSlot> get playerSlots;

  /// List of players in the game_match
  List<Player> get players;

  /// List of turns that have occurred in the game_match
  List<Turn> get turns;

  /// Current turn number
  @JsonKey(name: 'currentTurn', defaultValue: 0)
  int get currentTurn;

  /// Create a copy of GameMatch
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<GameMatch> get copyWith =>
      _$GameMatchCopyWithImpl<GameMatch>(this as GameMatch, _$identity);

  /// Serializes this GameMatch to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GameMatch &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.gameTypeId, gameTypeId) ||
                other.gameTypeId == gameTypeId) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isOpenForJoining, isOpenForJoining) ||
                other.isOpenForJoining == isOpenForJoining) &&
            const DeepCollectionEquality()
                .equals(other.playerSlots, playerSlots) &&
            const DeepCollectionEquality().equals(other.players, players) &&
            const DeepCollectionEquality().equals(other.turns, turns) &&
            (identical(other.currentTurn, currentTurn) ||
                other.currentTurn == currentTurn));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      gameTypeId,
      creatorId,
      gameName,
      createdAt,
      updatedAt,
      status,
      isOpenForJoining,
      const DeepCollectionEquality().hash(playerSlots),
      const DeepCollectionEquality().hash(players),
      const DeepCollectionEquality().hash(turns),
      currentTurn);

  @override
  String toString() {
    return 'GameMatch(id: $id, gameTypeId: $gameTypeId, creatorId: $creatorId, gameName: $gameName, createdAt: $createdAt, updatedAt: $updatedAt, status: $status, isOpenForJoining: $isOpenForJoining, playerSlots: $playerSlots, players: $players, turns: $turns, currentTurn: $currentTurn)';
  }
}

/// @nodoc
abstract mixin class $GameMatchCopyWith<$Res> {
  factory $GameMatchCopyWith(GameMatch value, $Res Function(GameMatch) _then) =
      _$GameMatchCopyWithImpl;
  @useResult
  $Res call(
      {@GenerateIdIfNeededConverter() String id,
      @JsonKey(name: 'gameTypeId') String gameTypeId,
      String creatorId,
      String? gameName,
      @DefaultTimestampConverter() @JsonKey(name: 'created_at') int createdAt,
      @DefaultTimestampConverter() @JsonKey(name: 'updated_at') int updatedAt,
      @JsonKey(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
      MatchStatus status,
      @JsonKey(name: 'is_open_for_joining') bool isOpenForJoining,
      List<PlayerSlot> playerSlots,
      List<Player> players,
      List<Turn> turns,
      @JsonKey(name: 'currentTurn', defaultValue: 0) int currentTurn});
}

/// @nodoc
class _$GameMatchCopyWithImpl<$Res> implements $GameMatchCopyWith<$Res> {
  _$GameMatchCopyWithImpl(this._self, this._then);

  final GameMatch _self;
  final $Res Function(GameMatch) _then;

  /// Create a copy of GameMatch
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? gameTypeId = null,
    Object? creatorId = null,
    Object? gameName = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? status = null,
    Object? isOpenForJoining = null,
    Object? playerSlots = null,
    Object? players = null,
    Object? turns = null,
    Object? currentTurn = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id!
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      gameTypeId: null == gameTypeId
          ? _self.gameTypeId!
          : gameTypeId // ignore: cast_nullable_to_non_nullable
              as String,
      creatorId: null == creatorId
          ? _self.creatorId
          : creatorId // ignore: cast_nullable_to_non_nullable
              as String,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MatchStatus,
      isOpenForJoining: null == isOpenForJoining
          ? _self.isOpenForJoining
          : isOpenForJoining // ignore: cast_nullable_to_non_nullable
              as bool,
      playerSlots: null == playerSlots
          ? _self.playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      players: null == players
          ? _self.players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      turns: null == turns
          ? _self.turns
          : turns // ignore: cast_nullable_to_non_nullable
              as List<Turn>,
      currentTurn: null == currentTurn
          ? _self.currentTurn
          : currentTurn // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _GameMatch implements GameMatch {
  const _GameMatch(
      {@GenerateIdIfNeededConverter() required this.id,
      @JsonKey(name: 'gameTypeId') required this.gameTypeId,
      required this.creatorId,
      this.gameName,
      @DefaultTimestampConverter()
      @JsonKey(name: 'created_at')
      required this.createdAt,
      @DefaultTimestampConverter()
      @JsonKey(name: 'updated_at')
      required this.updatedAt,
      @JsonKey(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
      this.status = MatchStatus.open,
      @JsonKey(name: 'is_open_for_joining') this.isOpenForJoining = false,
      final List<PlayerSlot> playerSlots = const [],
      final List<Player> players = const [],
      final List<Turn> turns = const [],
      @JsonKey(name: 'currentTurn', defaultValue: 0) this.currentTurn = 0})
      : _playerSlots = playerSlots,
        _players = players,
        _turns = turns;
  factory _GameMatch.fromJson(Map<String, dynamic> json) =>
      _$GameMatchFromJson(json);

  /// Unique identifier for the game_match
  @override
  @GenerateIdIfNeededConverter()
  final String id;

  /// Type of game this game_match is for
  @override
  @JsonKey(name: 'gameTypeId')
  final String gameTypeId;

  /// ID of the player who created the game_match
  @override
  final String creatorId;

  /// Optional custom name for the game
  @override
  final String? gameName;

  /// Created timestamp
  @override
  @DefaultTimestampConverter()
  @JsonKey(name: 'created_at')
  final int createdAt;

  /// Last updated timestamp
  @override
  @DefaultTimestampConverter()
  @JsonKey(name: 'updated_at')
  final int updatedAt;

  /// Current status of the game_match
  @override
  @JsonKey(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
  final MatchStatus status;

  /// Flag indicating if the match is open for new players to join
  @override
  @JsonKey(name: 'is_open_for_joining')
  final bool isOpenForJoining;

  /// List of player slots in the match (from server)
  final List<PlayerSlot> _playerSlots;

  /// List of player slots in the match (from server)
  @override
  @JsonKey()
  List<PlayerSlot> get playerSlots {
    if (_playerSlots is EqualUnmodifiableListView) return _playerSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_playerSlots);
  }

  /// List of players in the game_match
  final List<Player> _players;

  /// List of players in the game_match
  @override
  @JsonKey()
  List<Player> get players {
    if (_players is EqualUnmodifiableListView) return _players;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_players);
  }

  /// List of turns that have occurred in the game_match
  final List<Turn> _turns;

  /// List of turns that have occurred in the game_match
  @override
  @JsonKey()
  List<Turn> get turns {
    if (_turns is EqualUnmodifiableListView) return _turns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_turns);
  }

  /// Current turn number
  @override
  @JsonKey(name: 'currentTurn', defaultValue: 0)
  final int currentTurn;

  /// Create a copy of GameMatch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GameMatchCopyWith<_GameMatch> get copyWith =>
      __$GameMatchCopyWithImpl<_GameMatch>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GameMatchToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GameMatch &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.gameTypeId, gameTypeId) ||
                other.gameTypeId == gameTypeId) &&
            (identical(other.creatorId, creatorId) ||
                other.creatorId == creatorId) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isOpenForJoining, isOpenForJoining) ||
                other.isOpenForJoining == isOpenForJoining) &&
            const DeepCollectionEquality()
                .equals(other._playerSlots, _playerSlots) &&
            const DeepCollectionEquality().equals(other._players, _players) &&
            const DeepCollectionEquality().equals(other._turns, _turns) &&
            (identical(other.currentTurn, currentTurn) ||
                other.currentTurn == currentTurn));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      gameTypeId,
      creatorId,
      gameName,
      createdAt,
      updatedAt,
      status,
      isOpenForJoining,
      const DeepCollectionEquality().hash(_playerSlots),
      const DeepCollectionEquality().hash(_players),
      const DeepCollectionEquality().hash(_turns),
      currentTurn);

  @override
  String toString() {
    return 'GameMatch(id: $id, gameTypeId: $gameTypeId, creatorId: $creatorId, gameName: $gameName, createdAt: $createdAt, updatedAt: $updatedAt, status: $status, isOpenForJoining: $isOpenForJoining, playerSlots: $playerSlots, players: $players, turns: $turns, currentTurn: $currentTurn)';
  }
}

/// @nodoc
abstract mixin class _$GameMatchCopyWith<$Res>
    implements $GameMatchCopyWith<$Res> {
  factory _$GameMatchCopyWith(
          _GameMatch value, $Res Function(_GameMatch) _then) =
      __$GameMatchCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@GenerateIdIfNeededConverter() String id,
      @JsonKey(name: 'gameTypeId') String gameTypeId,
      String creatorId,
      String? gameName,
      @DefaultTimestampConverter() @JsonKey(name: 'created_at') int createdAt,
      @DefaultTimestampConverter() @JsonKey(name: 'updated_at') int updatedAt,
      @JsonKey(name: 'status', fromJson: _statusFromJson, toJson: _statusToJson)
      MatchStatus status,
      @JsonKey(name: 'is_open_for_joining') bool isOpenForJoining,
      List<PlayerSlot> playerSlots,
      List<Player> players,
      List<Turn> turns,
      @JsonKey(name: 'currentTurn', defaultValue: 0) int currentTurn});
}

/// @nodoc
class __$GameMatchCopyWithImpl<$Res> implements _$GameMatchCopyWith<$Res> {
  __$GameMatchCopyWithImpl(this._self, this._then);

  final _GameMatch _self;
  final $Res Function(_GameMatch) _then;

  /// Create a copy of GameMatch
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? gameTypeId = null,
    Object? creatorId = null,
    Object? gameName = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? status = null,
    Object? isOpenForJoining = null,
    Object? playerSlots = null,
    Object? players = null,
    Object? turns = null,
    Object? currentTurn = null,
  }) {
    return _then(_GameMatch(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      gameTypeId: null == gameTypeId
          ? _self.gameTypeId
          : gameTypeId // ignore: cast_nullable_to_non_nullable
              as String,
      creatorId: null == creatorId
          ? _self.creatorId
          : creatorId // ignore: cast_nullable_to_non_nullable
              as String,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as int,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MatchStatus,
      isOpenForJoining: null == isOpenForJoining
          ? _self.isOpenForJoining
          : isOpenForJoining // ignore: cast_nullable_to_non_nullable
              as bool,
      playerSlots: null == playerSlots
          ? _self._playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      players: null == players
          ? _self._players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      turns: null == turns
          ? _self._turns
          : turns // ignore: cast_nullable_to_non_nullable
              as List<Turn>,
      currentTurn: null == currentTurn
          ? _self.currentTurn
          : currentTurn // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
