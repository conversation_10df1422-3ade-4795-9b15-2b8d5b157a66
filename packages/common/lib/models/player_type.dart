/// Enum representing the type of player in a match
enum PlayerType {
  /// A human player playing locally
  humanLocal,
  
  /// A human player playing over network
  humanNetwork,
  
  /// A bot (AI) player running locally
  botLocal,
  
  /// A bot (AI) player running over network
  botNetwork;

  bool get isHuman => this == PlayerType.humanLocal || this == PlayerType.humanNetwork;
  bool get isBot => this == PlayerType.botLocal || this == PlayerType.botNetwork;
  bool get isLocal => this == PlayerType.humanLocal || this == PlayerType.botLocal;
}
