// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'turn.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Turn _$TurnFromJson(Map<String, dynamic> json) => _Turn(
      playerId: json['playerId'] as String,
      move: json['move'] as String,
      timestamp: json['timestamp'] as String,
      turnNumber: (json['turnNumber'] as num).toInt(),
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$TurnToJson(_Turn instance) => <String, dynamic>{
      'playerId': instance.playerId,
      'move': instance.move,
      'timestamp': instance.timestamp,
      'turnNumber': instance.turnNumber,
      'metadata': instance.metadata,
    };
