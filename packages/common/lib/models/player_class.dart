import 'package:freezed_annotation/freezed_annotation.dart';

part 'player_class.freezed.dart';
part 'player_class.g.dart';

@freezed
abstract class PlayerClass with _$PlayerClass {
  const factory PlayerClass({
    required String id,
    required String name,
    String? defaultPlayerName,
    int? numAllowed,
    int? numRequired,
    String? icon,
    Map<String, dynamic>? colorScheme,
  }) = _PlayerClass;

  factory PlayerClass.fromJson(Map<String, dynamic> json) =>
      _$PlayerClassFromJson(json);
}