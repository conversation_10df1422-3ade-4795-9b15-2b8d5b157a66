import 'package:common/models/player_class.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('PlayerClass', () {
    group('constructor', () {
      test('should create a PlayerClass with required fields', () {
        // Act
        final playerClass = ModelFactories.createPlayerClass();

        // Assert
        expect(playerClass.id, equals('test-class-id'));
        expect(playerClass.name, equals('Test Class'));
        expect(playerClass.defaultPlayerName, isNull);
        expect(playerClass.numAllowed, isNull);
        expect(playerClass.numRequired, isNull);
        expect(playerClass.icon, isNull);
        expect(playerClass.colorScheme, isNull);
      });

      test('should create a PlayerClass with custom values', () {
        // Act
        final playerClass = ModelFactories.createPlayerClass(
          id: 'warrior-class',
          name: 'Warrior',
          defaultPlayerName: 'Brave Warrior',
          numAllowed: 2,
          numRequired: 1,
          icon: 'sword.png',
          colorScheme: {'primary': '#FF0000', 'secondary': '#800000'},
        );

        // Assert
        expect(playerClass.id, equals('warrior-class'));
        expect(playerClass.name, equals('Warrior'));
        expect(playerClass.defaultPlayerName, equals('Brave Warrior'));
        expect(playerClass.numAllowed, equals(2));
        expect(playerClass.numRequired, equals(1));
        expect(playerClass.icon, equals('sword.png'));
        expect(playerClass.colorScheme!['primary'], equals('#FF0000'));
        expect(playerClass.colorScheme!['secondary'], equals('#800000'));
      });

      test('should allow null optional fields', () {
        // Act
        final playerClass = PlayerClass(
          id: 'minimal-class',
          name: 'Minimal Class',
        );

        // Assert
        expect(playerClass.id, equals('minimal-class'));
        expect(playerClass.name, equals('Minimal Class'));
        expect(playerClass.defaultPlayerName, isNull);
        expect(playerClass.numAllowed, isNull);
        expect(playerClass.numRequired, isNull);
        expect(playerClass.icon, isNull);
        expect(playerClass.colorScheme, isNull);
      });

      test('should handle zero values for numeric fields', () {
        // Act
        final playerClass = ModelFactories.createPlayerClass(
          numAllowed: 0,
          numRequired: 0,
        );

        // Assert
        expect(playerClass.numAllowed, equals(0));
        expect(playerClass.numRequired, equals(0));
      });

      test('should handle negative values for numeric fields', () {
        // Act
        final playerClass = ModelFactories.createPlayerClass(
          numAllowed: -1,
          numRequired: -1,
        );

        // Assert
        expect(playerClass.numAllowed, equals(-1));
        expect(playerClass.numRequired, equals(-1));
      });

      test('should handle empty strings', () {
        // Act
        final playerClass = PlayerClass(
          id: '',
          name: '',
          defaultPlayerName: '',
          icon: '',
        );

        // Assert
        expect(playerClass.id, equals(''));
        expect(playerClass.name, equals(''));
        expect(playerClass.defaultPlayerName, equals(''));
        expect(playerClass.icon, equals(''));
      });

      test('should handle very long strings', () {
        // Arrange
        final longString = 'a' * 1000;

        // Act
        final playerClass = PlayerClass(
          id: longString,
          name: longString,
          defaultPlayerName: longString,
          icon: longString,
        );

        // Assert
        expect(playerClass.id, equals(longString));
        expect(playerClass.name, equals(longString));
        expect(playerClass.defaultPlayerName, equals(longString));
        expect(playerClass.icon, equals(longString));
      });

      test('should handle special characters in strings', () {
        // Arrange
        const specialString = '<EMAIL>-123_456!@#\$%^&*()';

        // Act
        final playerClass = PlayerClass(
          id: specialString,
          name: specialString,
          defaultPlayerName: specialString,
          icon: specialString,
        );

        // Assert
        expect(playerClass.id, equals(specialString));
        expect(playerClass.name, equals(specialString));
        expect(playerClass.defaultPlayerName, equals(specialString));
        expect(playerClass.icon, equals(specialString));
      });

      test('should handle unicode characters in strings', () {
        // Arrange
        const unicodeString = 'class-🎮-游戏-🚀';

        // Act
        final playerClass = PlayerClass(
          id: unicodeString,
          name: unicodeString,
          defaultPlayerName: unicodeString,
          icon: unicodeString,
        );

        // Assert
        expect(playerClass.id, equals(unicodeString));
        expect(playerClass.name, equals(unicodeString));
        expect(playerClass.defaultPlayerName, equals(unicodeString));
        expect(playerClass.icon, equals(unicodeString));
      });

      test('should handle complex color scheme', () {
        // Arrange
        final complexColorScheme = {
          'primary': '#FF0000',
          'secondary': '#00FF00',
          'accent': '#0000FF',
          'background': '#FFFFFF',
          'text': '#000000',
          'nested': {
            'light': '#CCCCCC',
            'dark': '#333333',
          },
          'array': ['#111111', '#222222', '#333333'],
          'number': 255,
          'boolean': true,
        };

        // Act
        final playerClass = ModelFactories.createPlayerClass(
          colorScheme: complexColorScheme,
        );

        // Assert
        expect(playerClass.colorScheme, equals(complexColorScheme));
        expect(playerClass.colorScheme!['nested']['light'], equals('#CCCCCC'));
        expect(playerClass.colorScheme!['array'][0], equals('#111111'));
        expect(playerClass.colorScheme!['number'], equals(255));
        expect(playerClass.colorScheme!['boolean'], equals(true));
      });

      test('should handle empty color scheme', () {
        // Act
        final playerClass = ModelFactories.createPlayerClass(
          colorScheme: {},
        );

        // Assert
        expect(playerClass.colorScheme, isEmpty);
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly with all fields', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass(
          id: 'json-class',
          name: 'JSON Class',
          defaultPlayerName: 'JSON Player',
          numAllowed: 3,
          numRequired: 1,
          icon: 'json.png',
          colorScheme: {'primary': '#FF0000'},
        );

        // Act
        final json = playerClass.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('json-class'));
        expect(json['name'], equals('JSON Class'));
        expect(json['defaultPlayerName'], equals('JSON Player'));
        expect(json['numAllowed'], equals(3));
        expect(json['numRequired'], equals(1));
        expect(json['icon'], equals('json.png'));
        expect(json['colorScheme'], isA<Map<String, dynamic>>());
        expect(json['colorScheme']['primary'], equals('#FF0000'));
      });

      test('should serialize to JSON correctly with minimal fields', () {
        // Arrange
        final playerClass = PlayerClass(
          id: 'minimal-class',
          name: 'Minimal Class',
        );

        // Act
        final json = playerClass.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('minimal-class'));
        expect(json['name'], equals('Minimal Class'));
        // Note: Freezed/json_serializable includes null fields in JSON by default
        expect(json['defaultPlayerName'], isNull);
        expect(json['numAllowed'], isNull);
        expect(json['numRequired'], isNull);
        expect(json['icon'], isNull);
        expect(json['colorScheme'], isNull);
      });

      test('should deserialize from JSON correctly with all fields', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerClassJson(
          id: 'json-class',
          name: 'JSON Class',
          defaultPlayerName: 'JSON Player',
          numAllowed: 3,
          numRequired: 1,
          icon: 'json.png',
          colorScheme: {'primary': '#FF0000'},
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.id, equals('json-class'));
        expect(playerClass.name, equals('JSON Class'));
        expect(playerClass.defaultPlayerName, equals('JSON Player'));
        expect(playerClass.numAllowed, equals(3));
        expect(playerClass.numRequired, equals(1));
        expect(playerClass.icon, equals('json.png'));
        expect(playerClass.colorScheme!['primary'], equals('#FF0000'));
      });

      test('should deserialize from JSON correctly with minimal fields', () {
        // Arrange
        final jsonData = {
          'id': 'minimal-class',
          'name': 'Minimal Class',
        };

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.id, equals('minimal-class'));
        expect(playerClass.name, equals('Minimal Class'));
        expect(playerClass.defaultPlayerName, isNull);
        expect(playerClass.numAllowed, isNull);
        expect(playerClass.numRequired, isNull);
        expect(playerClass.icon, isNull);
        expect(playerClass.colorScheme, isNull);
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalPlayerClass = ModelFactories.createPlayerClass(
          id: 'round-trip-class',
          name: 'Round Trip Class',
          defaultPlayerName: 'Round Trip Player',
          numAllowed: 5,
          numRequired: 2,
          icon: 'round-trip.png',
          colorScheme: {
            'primary': '#FF0000',
            'secondary': '#00FF00',
            'nested': {'value': 123},
          },
        );

        // Act
        final json = originalPlayerClass.toJson();
        final deserializedPlayerClass = PlayerClass.fromJson(json);

        // Assert
        expect(deserializedPlayerClass, equals(originalPlayerClass));
      });

      test('should handle null values in JSON', () {
        // Arrange
        final jsonData = {
          'id': 'null-test-class',
          'name': 'Null Test Class',
          'defaultPlayerName': null,
          'numAllowed': null,
          'numRequired': null,
          'icon': null,
          'colorScheme': null,
        };

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.id, equals('null-test-class'));
        expect(playerClass.name, equals('Null Test Class'));
        expect(playerClass.defaultPlayerName, isNull);
        expect(playerClass.numAllowed, isNull);
        expect(playerClass.numRequired, isNull);
        expect(playerClass.icon, isNull);
        expect(playerClass.colorScheme, isNull);
      });

      test('should handle zero values in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerClassJson(
          numAllowed: 0,
          numRequired: 0,
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.numAllowed, equals(0));
        expect(playerClass.numRequired, equals(0));
      });

      test('should handle negative values in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerClassJson(
          numAllowed: -1,
          numRequired: -5,
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.numAllowed, equals(-1));
        expect(playerClass.numRequired, equals(-5));
      });

      test('should handle empty strings in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerClassJson(
          id: '',
          name: '',
          defaultPlayerName: '',
          icon: '',
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.id, equals(''));
        expect(playerClass.name, equals(''));
        expect(playerClass.defaultPlayerName, equals(''));
        expect(playerClass.icon, equals(''));
      });

      test('should handle special characters in JSON', () {
        // Arrange
        const specialString = '<EMAIL>-123_456!@#\$%^&*()';
        final jsonData = ModelFactories.createPlayerClassJson(
          id: specialString,
          name: specialString,
          defaultPlayerName: specialString,
          icon: specialString,
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.id, equals(specialString));
        expect(playerClass.name, equals(specialString));
        expect(playerClass.defaultPlayerName, equals(specialString));
        expect(playerClass.icon, equals(specialString));
      });

      test('should handle unicode characters in JSON', () {
        // Arrange
        const unicodeString = 'class-🎮-游戏-🚀';
        final jsonData = ModelFactories.createPlayerClassJson(
          id: unicodeString,
          name: unicodeString,
          defaultPlayerName: unicodeString,
          icon: unicodeString,
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.id, equals(unicodeString));
        expect(playerClass.name, equals(unicodeString));
        expect(playerClass.defaultPlayerName, equals(unicodeString));
        expect(playerClass.icon, equals(unicodeString));
      });

      test('should handle complex color scheme in JSON', () {
        // Arrange
        final complexColorScheme = {
          'primary': '#FF0000',
          'nested': {
            'light': '#CCCCCC',
            'dark': '#333333',
          },
          'array': ['#111111', '#222222'],
          'number': 255,
          'boolean': true,
        };
        final jsonData = ModelFactories.createPlayerClassJson(
          colorScheme: complexColorScheme,
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.colorScheme, equals(complexColorScheme));
        expect(playerClass.colorScheme!['nested']['light'], equals('#CCCCCC'));
        expect(playerClass.colorScheme!['array'][0], equals('#111111'));
        expect(playerClass.colorScheme!['number'], equals(255));
        expect(playerClass.colorScheme!['boolean'], equals(true));
      });

      test('should handle empty color scheme in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerClassJson(
          colorScheme: {},
        );

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.colorScheme, isEmpty);
      });

      test('should handle extra fields in JSON gracefully', () {
        // Arrange
        final jsonData = {
          'id': 'extra-fields-class',
          'name': 'Extra Fields Class',
          'extraField': 'should be ignored',
          'anotherField': 123,
          'nestedExtra': {'ignored': true},
        };

        // Act
        final playerClass = PlayerClass.fromJson(jsonData);

        // Assert
        expect(playerClass.id, equals('extra-fields-class'));
        expect(playerClass.name, equals('Extra Fields Class'));
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        final playerClass1 = ModelFactories.createPlayerClass(
          id: 'same-id',
          name: 'Same Name',
          defaultPlayerName: 'Same Default',
          numAllowed: 2,
          numRequired: 1,
          icon: 'same.png',
          colorScheme: {'primary': '#FF0000'},
        );
        final playerClass2 = ModelFactories.createPlayerClass(
          id: 'same-id',
          name: 'Same Name',
          defaultPlayerName: 'Same Default',
          numAllowed: 2,
          numRequired: 1,
          icon: 'same.png',
          colorScheme: {'primary': '#FF0000'},
        );

        // Assert
        expect(playerClass1, equals(playerClass2));
        expect(playerClass1.hashCode, equals(playerClass2.hashCode));
      });

      test('should not be equal when IDs differ', () {
        // Arrange
        final playerClass1 = ModelFactories.createPlayerClass(id: 'id1');
        final playerClass2 = ModelFactories.createPlayerClass(id: 'id2');

        // Assert
        expect(playerClass1, isNot(equals(playerClass2)));
      });

      test('should not be equal when names differ', () {
        // Arrange
        final playerClass1 = ModelFactories.createPlayerClass(name: 'Name1');
        final playerClass2 = ModelFactories.createPlayerClass(name: 'Name2');

        // Assert
        expect(playerClass1, isNot(equals(playerClass2)));
      });

      test('should not be equal when optional fields differ', () {
        // Arrange
        final playerClass1 = ModelFactories.createPlayerClass(numAllowed: 1);
        final playerClass2 = ModelFactories.createPlayerClass(numAllowed: 2);

        // Assert
        expect(playerClass1, isNot(equals(playerClass2)));
      });

      test('should be equal to itself', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass();

        // Assert
        expect(playerClass, equals(playerClass));
      });

      test('should not be equal to null', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass();

        // Assert
        expect(playerClass, isNot(equals(null)));
      });

      test('should not be equal to different type', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass();
        const notPlayerClass = 'not a player class';

        // Assert
        expect(playerClass, isNot(equals(notPlayerClass)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalPlayerClass = ModelFactories.createPlayerClass(
          id: 'original-id',
          name: 'Original Name',
          defaultPlayerName: 'Original Default',
          numAllowed: 2,
          numRequired: 1,
          icon: 'original.png',
          colorScheme: {'primary': '#FF0000'},
        );

        // Act
        final copiedPlayerClass = originalPlayerClass.copyWith(
          name: 'New Name',
          numAllowed: 5,
          icon: 'new.png',
        );

        // Assert
        expect(copiedPlayerClass.id, equals('original-id'));
        expect(copiedPlayerClass.name, equals('New Name'));
        expect(copiedPlayerClass.defaultPlayerName, equals('Original Default'));
        expect(copiedPlayerClass.numAllowed, equals(5));
        expect(copiedPlayerClass.numRequired, equals(1));
        expect(copiedPlayerClass.icon, equals('new.png'));
        expect(copiedPlayerClass.colorScheme!['primary'], equals('#FF0000'));
      });

      test('should create identical copy when no parameters provided', () {
        // Arrange
        final originalPlayerClass = ModelFactories.createPlayerClass();

        // Act
        final copiedPlayerClass = originalPlayerClass.copyWith();

        // Assert
        expect(copiedPlayerClass, equals(originalPlayerClass));
      });

      test('should allow setting optional fields to null', () {
        // Arrange
        final originalPlayerClass = ModelFactories.createPlayerClass(
          defaultPlayerName: 'Original Default',
          numAllowed: 2,
          numRequired: 1,
          icon: 'original.png',
          colorScheme: {'primary': '#FF0000'},
        );

        // Act
        final copiedPlayerClass = originalPlayerClass.copyWith(
          defaultPlayerName: null,
          numAllowed: null,
          numRequired: null,
          icon: null,
          colorScheme: null,
        );

        // Assert
        expect(copiedPlayerClass.defaultPlayerName, isNull);
        expect(copiedPlayerClass.numAllowed, isNull);
        expect(copiedPlayerClass.numRequired, isNull);
        expect(copiedPlayerClass.icon, isNull);
        expect(copiedPlayerClass.colorScheme, isNull);
      });
    });

    group('edge cases', () {
      test('should handle null required fields gracefully in fromJson', () {
        // Arrange
        final jsonData = <String, dynamic>{'id': null, 'name': null};

        // Act & Assert
        expect(() => PlayerClass.fromJson(jsonData), throwsA(isA<TypeError>()));
      });

      test('should handle missing required fields in fromJson', () {
        // Arrange
        final jsonData = <String, dynamic>{};

        // Act & Assert
        expect(() => PlayerClass.fromJson(jsonData), throwsA(isA<TypeError>()));
      });

      test('should handle very large numeric values', () {
        // Arrange
        const largeNumber = 9223372036854775807; // Max int64

        // Act
        final playerClass = ModelFactories.createPlayerClass(
          numAllowed: largeNumber,
          numRequired: largeNumber,
        );

        // Assert
        expect(playerClass.numAllowed, equals(largeNumber));
        expect(playerClass.numRequired, equals(largeNumber));
      });

      test('should handle very small numeric values', () {
        // Arrange
        const smallNumber = -9223372036854775808; // Min int64

        // Act
        final playerClass = ModelFactories.createPlayerClass(
          numAllowed: smallNumber,
          numRequired: smallNumber,
        );

        // Assert
        expect(playerClass.numAllowed, equals(smallNumber));
        expect(playerClass.numRequired, equals(smallNumber));
      });
    });
  });
}
