import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('GameMatch', () {
    group('constructor', () {
      test('should create a GameMatch with required fields', () {
        // Act
        final match = ModelFactories.createGameMatch();

        // Assert
        expect(match.id, equals('test-match-id'));
        expect(match.gameTypeId, equals('test-game-type'));
        expect(match.creatorId, equals('test-creator-id'));
        expect(match.gameName, isNull);
        expect(match.createdAt, isA<int>());
        expect(match.updatedAt, isA<int>());
        expect(match.status, equals(MatchStatus.open));
        expect(match.isOpenForJoining, isTrue);
        expect(match.playerSlots, isEmpty);
        expect(match.players, isEmpty);
        expect(match.turns, isEmpty);
        expect(match.currentTurn, equals(0));
      });

      test('should create a GameMatch with custom values', () {
        // Arrange
        const customCreatedAt = 1640995200000;
        const customUpdatedAt = 1640995300000;

        // Act
        final match = ModelFactories.createGameMatch(
          id: 'custom-match',
          gameTypeId: 'custom-game',
          creatorId: 'custom-creator',
          gameName: 'Custom Game',
          createdAt: customCreatedAt,
          updatedAt: customUpdatedAt,
          status: MatchStatus.active,
          isOpenForJoining: false,
          currentTurn: 5,
        );

        // Assert
        expect(match.id, equals('custom-match'));
        expect(match.gameTypeId, equals('custom-game'));
        expect(match.creatorId, equals('custom-creator'));
        expect(match.gameName, equals('Custom Game'));
        expect(match.createdAt, equals(customCreatedAt));
        expect(match.updatedAt, equals(customUpdatedAt));
        expect(match.status, equals(MatchStatus.active));
        expect(match.isOpenForJoining, isFalse);
        expect(match.currentTurn, equals(5));
      });

      test('should use default values when not specified', () {
        // Act
        final match = GameMatch(
          id: 'test-id',
          gameTypeId: 'test-game',
          creatorId: 'test-creator',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        // Assert
        expect(match.status, equals(MatchStatus.open));
        expect(match.isOpenForJoining, isFalse);
        expect(match.currentTurn, equals(0));
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final match = ModelFactories.createGameMatch(
          id: 'json-match',
          gameTypeId: 'json-game',
          creatorId: 'json-creator',
          gameName: 'JSON Match',
          status: MatchStatus.active,
          isOpenForJoining: true,
          currentTurn: 3,
        );

        // Act
        final json = match.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('json-match'));
        expect(json['gameTypeId'], equals('json-game'));
        expect(json['creatorId'], equals('json-creator'));
        expect(json['gameName'], equals('JSON Match'));
        expect(json['created_at'], isA<int>());
        expect(json['updated_at'], isA<int>());
        expect(json['status'], equals('active'));
        expect(json['is_open_for_joining'], isTrue);
        expect(json['playerSlots'], isA<List>());
        expect(json['players'], isA<List>());
        expect(json['turns'], isA<List>());
        expect(json['currentTurn'], equals(3));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createGameMatchJson(
          id: 'json-match',
          gameTypeId: 'json-game',
          creatorId: 'json-creator',
          gameName: 'JSON Match',
          status: 'active',
          isOpenForJoining: true,
          currentTurn: 3,
        );

        // Act
        final match = GameMatch.fromJson(jsonData);

        // Assert
        expect(match.id, equals('json-match'));
        expect(match.gameTypeId, equals('json-game'));
        expect(match.creatorId, equals('json-creator'));
        expect(match.gameName, equals('JSON Match'));
        expect(match.status, equals(MatchStatus.active));
        expect(match.isOpenForJoining, isTrue);
        expect(match.currentTurn, equals(3));
      });

      test('should handle all MatchStatus values in JSON', () {
        final testCases = [
          ('open', MatchStatus.open),
          ('active', MatchStatus.active),
          ('closed', MatchStatus.closed),
        ];

        for (final (jsonStatus, enumStatus) in testCases) {
          // Arrange
          final jsonData = ModelFactories.createGameMatchJson(status: jsonStatus);

          // Act
          final match = GameMatch.fromJson(jsonData);

          // Assert
          expect(match.status, equals(enumStatus), reason: 'Failed for status: $jsonStatus');
        }
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalMatch = ModelFactories.createGameMatch(
          id: 'round-trip-test',
          gameName: 'Round Trip Test',
          status: MatchStatus.active,
        );

        // Act
        final json = originalMatch.toJson();
        final deserializedMatch = GameMatch.fromJson(json);

        // Assert
        expect(deserializedMatch.id, equals(originalMatch.id));
        expect(deserializedMatch.gameTypeId, equals(originalMatch.gameTypeId));
        expect(deserializedMatch.creatorId, equals(originalMatch.creatorId));
        expect(deserializedMatch.gameName, equals(originalMatch.gameName));
        expect(deserializedMatch.status, equals(originalMatch.status));
        expect(deserializedMatch.isOpenForJoining, equals(originalMatch.isOpenForJoining));
        expect(deserializedMatch.currentTurn, equals(originalMatch.currentTurn));
      });

      test('should handle null gameName in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createGameMatchJson(gameName: null);

        // Act
        final match = GameMatch.fromJson(jsonData);

        // Assert
        expect(match.gameName, isNull);
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        final match1 = ModelFactories.createGameMatch();
        final match2 = ModelFactories.createGameMatch();

        // Assert
        expect(match1, equals(match2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final match1 = ModelFactories.createGameMatch(id: 'id1');
        final match2 = ModelFactories.createGameMatch(id: 'id2');

        // Assert
        expect(match1, isNot(equals(match2)));
      });

      test('should not be equal when status differs', () {
        // Arrange
        final match1 = ModelFactories.createGameMatch(status: MatchStatus.open);
        final match2 = ModelFactories.createGameMatch(status: MatchStatus.active);

        // Assert
        expect(match1, isNot(equals(match2)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalMatch = ModelFactories.createGameMatch();

        // Act
        final copiedMatch = originalMatch.copyWith(
          gameName: 'New Name',
          status: MatchStatus.closed,
          currentTurn: 10,
        );

        // Assert
        expect(copiedMatch.id, equals(originalMatch.id));
        expect(copiedMatch.gameName, equals('New Name'));
        expect(copiedMatch.status, equals(MatchStatus.closed));
        expect(copiedMatch.currentTurn, equals(10));
        expect(copiedMatch.creatorId, equals(originalMatch.creatorId));
      });
    });

    group('complex scenarios', () {
      test('should handle complete match with players and turns', () {
        // Act
        final match = ModelFactories.createCompleteGameMatch(
          playerCount: 3,
          turnCount: 5,
        );

        // Assert
        expect(match.players.length, equals(3));
        expect(match.playerSlots.length, equals(3));
        expect(match.turns.length, equals(5));
        expect(match.currentTurn, equals(5));
        expect(match.status, equals(MatchStatus.active));
      });
    });
  });
}
