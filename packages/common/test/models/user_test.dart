import 'package:common/models/user.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('User', () {
    group('constructor', () {
      test('should create a User with required fields', () {
        // Act
        final user = ModelFactories.createUser();

        // Assert
        expect(user.id, equals('test-user-id'));
      });

      test('should create a User with custom values', () {
        // Act
        final user = ModelFactories.createUser(
          id: 'custom-user-id',
        );

        // Assert
        expect(user.id, equals('custom-user-id'));
      });

      test('should handle empty string ID', () {
        // Act
        final user = User(id: '');

        // Assert
        expect(user.id, equals(''));
      });

      test('should handle very long ID', () {
        // Arrange
        final longId = 'a' * 1000;

        // Act
        final user = User(id: longId);

        // Assert
        expect(user.id, equals(longId));
      });

      test('should handle special characters in ID', () {
        // Arrange
        const specialId = '<EMAIL>-123_456!@#\$%^&*()';

        // Act
        final user = User(id: specialId);

        // Assert
        expect(user.id, equals(specialId));
      });

      test('should handle unicode characters in ID', () {
        // Arrange
        const unicodeId = 'user-🎮-游戏-🚀';

        // Act
        final user = User(id: unicodeId);

        // Assert
        expect(user.id, equals(unicodeId));
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final user = ModelFactories.createUser(
          id: 'json-user-id',
        );

        // Act
        final json = user.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('json-user-id'));
        expect(json.keys.length, equals(1));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createUserJson(
          id: 'json-user-id',
        );

        // Act
        final user = User.fromJson(jsonData);

        // Assert
        expect(user.id, equals('json-user-id'));
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalUser = ModelFactories.createUser(
          id: 'round-trip-user',
        );

        // Act
        final json = originalUser.toJson();
        final deserializedUser = User.fromJson(json);

        // Assert
        expect(deserializedUser, equals(originalUser));
      });

      test('should handle empty string ID in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createUserJson(id: '');

        // Act
        final user = User.fromJson(jsonData);

        // Assert
        expect(user.id, equals(''));
      });

      test('should handle special characters in JSON', () {
        // Arrange
        const specialId = '<EMAIL>-123_456!@#\$%^&*()';
        final jsonData = ModelFactories.createUserJson(id: specialId);

        // Act
        final user = User.fromJson(jsonData);

        // Assert
        expect(user.id, equals(specialId));
      });

      test('should handle unicode characters in JSON', () {
        // Arrange
        const unicodeId = 'user-🎮-游戏-🚀';
        final jsonData = ModelFactories.createUserJson(id: unicodeId);

        // Act
        final user = User.fromJson(jsonData);

        // Assert
        expect(user.id, equals(unicodeId));
      });
    });

    group('equality', () {
      test('should be equal when IDs are the same', () {
        // Arrange
        final user1 = ModelFactories.createUser(id: 'same-id');
        final user2 = ModelFactories.createUser(id: 'same-id');

        // Assert
        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });

      test('should not be equal when IDs differ', () {
        // Arrange
        final user1 = ModelFactories.createUser(id: 'id1');
        final user2 = ModelFactories.createUser(id: 'id2');

        // Assert
        expect(user1, isNot(equals(user2)));
        expect(user1.hashCode, isNot(equals(user2.hashCode)));
      });

      test('should be equal to itself', () {
        // Arrange
        final user = ModelFactories.createUser();

        // Assert
        expect(user, equals(user));
        expect(user.hashCode, equals(user.hashCode));
      });

      test('should not be equal to null', () {
        // Arrange
        final user = ModelFactories.createUser();

        // Assert
        expect(user, isNot(equals(null)));
      });

      test('should not be equal to different type', () {
        // Arrange
        final user = ModelFactories.createUser();
        const notUser = 'not a user';

        // Assert
        expect(user, isNot(equals(notUser)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified ID', () {
        // Arrange
        final originalUser = ModelFactories.createUser(id: 'original-id');

        // Act
        final copiedUser = originalUser.copyWith(id: 'new-id');

        // Assert
        expect(copiedUser.id, equals('new-id'));
        expect(originalUser.id, equals('original-id'));
        expect(copiedUser, isNot(equals(originalUser)));
      });

      test('should create identical copy when no parameters provided', () {
        // Arrange
        final originalUser = ModelFactories.createUser();

        // Act
        final copiedUser = originalUser.copyWith();

        // Assert
        expect(copiedUser, equals(originalUser));
        expect(copiedUser.id, equals(originalUser.id));
      });
    });

    group('edge cases', () {
      test('should handle null ID gracefully in fromJson', () {
        // Arrange
        final jsonData = <String, dynamic>{'id': null};

        // Act & Assert
        expect(() => User.fromJson(jsonData), throwsA(isA<TypeError>()));
      });

      test('should handle missing ID field in fromJson', () {
        // Arrange
        final jsonData = <String, dynamic>{};

        // Act & Assert
        expect(() => User.fromJson(jsonData), throwsA(isA<TypeError>()));
      });

      test('should handle extra fields in JSON gracefully', () {
        // Arrange
        final jsonData = {
          'id': 'test-user',
          'extraField': 'should be ignored',
          'anotherField': 123,
        };

        // Act
        final user = User.fromJson(jsonData);

        // Assert
        expect(user.id, equals('test-user'));
      });
    });
  });
}
