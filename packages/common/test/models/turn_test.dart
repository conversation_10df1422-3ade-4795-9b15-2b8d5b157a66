import 'package:common/models/turn.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('Turn', () {
    group('constructor', () {
      test('should create a Turn with required fields', () {
        // Act
        final turn = ModelFactories.createTurn();

        // Assert
        expect(turn.playerId, equals('test-player-id'));
        expect(turn.move, equals('test-move-data'));
        expect(turn.timestamp, isA<String>());
        expect(turn.turnNumber, equals(1));
        expect(turn.metadata, isEmpty);
      });

      test('should create a Turn with custom values', () {
        // Act
        final turn = ModelFactories.createTurn(
          playerId: 'custom-player',
          move: 'custom-move',
          timestamp: '2023-01-01T00:00:00Z',
          turnNumber: 5,
          metadata: {'duration': 30},
        );

        // Assert
        expect(turn.playerId, equals('custom-player'));
        expect(turn.move, equals('custom-move'));
        expect(turn.timestamp, equals('2023-01-01T00:00:00Z'));
        expect(turn.turnNumber, equals(5));
        expect(turn.metadata['duration'], equals(30));
      });

      test('should use default metadata when not specified', () {
        // Act
        final turn = Turn(
          playerId: 'test-player',
          move: 'test-move',
          timestamp: '2023-01-01T00:00:00Z',
          turnNumber: 1,
        );

        // Assert
        expect(turn.metadata, isEmpty);
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final turn = ModelFactories.createTurn(
          playerId: 'json-player',
          move: 'json-move',
          timestamp: '2023-01-01T12:00:00Z',
          turnNumber: 3,
          metadata: {'score': 100},
        );

        // Act
        final json = turn.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['playerId'], equals('json-player'));
        expect(json['move'], equals('json-move'));
        expect(json['timestamp'], equals('2023-01-01T12:00:00Z'));
        expect(json['turnNumber'], equals(3));
        expect(json['metadata'], isA<Map<String, dynamic>>());
        expect(json['metadata']['score'], equals(100));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createTurnJson(
          playerId: 'json-player',
          move: 'json-move',
          timestamp: '2023-01-01T12:00:00Z',
          turnNumber: 3,
          metadata: {'score': 100},
        );

        // Act
        final turn = Turn.fromJson(jsonData);

        // Assert
        expect(turn.playerId, equals('json-player'));
        expect(turn.move, equals('json-move'));
        expect(turn.timestamp, equals('2023-01-01T12:00:00Z'));
        expect(turn.turnNumber, equals(3));
        expect(turn.metadata['score'], equals(100));
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalTurn = ModelFactories.createTurn(
          metadata: {'complex': {'nested': 'value'}, 'array': [1, 2, 3]},
        );

        // Act
        final json = originalTurn.toJson();
        final deserializedTurn = Turn.fromJson(json);

        // Assert
        expect(deserializedTurn, equals(originalTurn));
      });

      test('should handle empty metadata in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createTurnJson(metadata: {});

        // Act
        final turn = Turn.fromJson(jsonData);

        // Assert
        expect(turn.metadata, isEmpty);
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        const fixedTimestamp = '2023-01-01T00:00:00Z';
        final turn1 = ModelFactories.createTurn(timestamp: fixedTimestamp);
        final turn2 = ModelFactories.createTurn(timestamp: fixedTimestamp);

        // Assert
        expect(turn1, equals(turn2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final turn1 = ModelFactories.createTurn(turnNumber: 1);
        final turn2 = ModelFactories.createTurn(turnNumber: 2);

        // Assert
        expect(turn1, isNot(equals(turn2)));
      });

      test('should not be equal when move data differs', () {
        // Arrange
        final turn1 = ModelFactories.createTurn(move: 'move1');
        final turn2 = ModelFactories.createTurn(move: 'move2');

        // Assert
        expect(turn1, isNot(equals(turn2)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalTurn = ModelFactories.createTurn();

        // Act
        final copiedTurn = originalTurn.copyWith(
          move: 'new-move',
          turnNumber: 99,
          metadata: {'new': 'metadata'},
        );

        // Assert
        expect(copiedTurn.playerId, equals(originalTurn.playerId));
        expect(copiedTurn.move, equals('new-move'));
        expect(copiedTurn.timestamp, equals(originalTurn.timestamp));
        expect(copiedTurn.turnNumber, equals(99));
        expect(copiedTurn.metadata['new'], equals('metadata'));
      });
    });

    group('edge cases', () {
      test('should handle very long move data', () {
        // Arrange
        final longMove = 'a' * 10000;

        // Act
        final turn = ModelFactories.createTurn(move: longMove);

        // Assert
        expect(turn.move, equals(longMove));
      });

      test('should handle complex move data as JSON string', () {
        // Arrange
        const complexMove = '{"action":"move","coordinates":[1,2,3],"options":{"fast":true}}';

        // Act
        final turn = ModelFactories.createTurn(move: complexMove);

        // Assert
        expect(turn.move, equals(complexMove));
      });

      test('should handle zero and negative turn numbers', () {
        // Act
        final turn1 = ModelFactories.createTurn(turnNumber: 0);
        final turn2 = ModelFactories.createTurn(turnNumber: -1);

        // Assert
        expect(turn1.turnNumber, equals(0));
        expect(turn2.turnNumber, equals(-1));
      });

      test('should handle empty string values', () {
        // Act
        final turn = Turn(
          playerId: '',
          move: '',
          timestamp: '',
          turnNumber: 0,
          metadata: {},
        );

        // Assert
        expect(turn.playerId, equals(''));
        expect(turn.move, equals(''));
        expect(turn.timestamp, equals(''));
        expect(turn.turnNumber, equals(0));
        expect(turn.metadata, isEmpty);
      });

      test('should handle special characters in strings', () {
        // Arrange
        const specialString = '<EMAIL>-123_456!@#\$%^&*()';

        // Act
        final turn = Turn(
          playerId: specialString,
          move: specialString,
          timestamp: specialString,
          turnNumber: 1,
          metadata: {'special': specialString},
        );

        // Assert
        expect(turn.playerId, equals(specialString));
        expect(turn.move, equals(specialString));
        expect(turn.timestamp, equals(specialString));
        expect(turn.metadata['special'], equals(specialString));
      });

      test('should handle unicode characters in strings', () {
        // Arrange
        const unicodeString = 'turn-🎮-游戏-🚀';

        // Act
        final turn = Turn(
          playerId: unicodeString,
          move: unicodeString,
          timestamp: unicodeString,
          turnNumber: 1,
          metadata: {'unicode': unicodeString},
        );

        // Assert
        expect(turn.playerId, equals(unicodeString));
        expect(turn.move, equals(unicodeString));
        expect(turn.timestamp, equals(unicodeString));
        expect(turn.metadata['unicode'], equals(unicodeString));
      });

      test('should handle very large turn numbers', () {
        // Arrange
        const largeTurnNumber = 9223372036854775807; // Max int64

        // Act
        final turn = ModelFactories.createTurn(turnNumber: largeTurnNumber);

        // Assert
        expect(turn.turnNumber, equals(largeTurnNumber));
      });

      test('should handle very small turn numbers', () {
        // Arrange
        const smallTurnNumber = -9223372036854775808; // Min int64

        // Act
        final turn = ModelFactories.createTurn(turnNumber: smallTurnNumber);

        // Assert
        expect(turn.turnNumber, equals(smallTurnNumber));
      });

      test('should handle complex nested metadata', () {
        // Arrange
        final complexMetadata = {
          'performance': {
            'duration': 1500,
            'accuracy': 0.95,
            'efficiency': {
              'cpu': 0.8,
              'memory': 0.6,
            },
          },
          'gameState': {
            'score': 1000,
            'level': 5,
            'powerUps': ['speed', 'strength'],
            'inventory': [
              {'item': 'sword', 'quantity': 1},
              {'item': 'potion', 'quantity': 3},
            ],
          },
          'analytics': {
            'clickCount': 15,
            'keyPresses': 42,
            'mouseMovements': 127,
          },
          'flags': {
            'isFirstMove': false,
            'isSpecialMove': true,
            'isUndoable': true,
          },
        };

        // Act
        final turn = ModelFactories.createTurn(metadata: complexMetadata);

        // Assert
        expect(turn.metadata, equals(complexMetadata));
        expect(turn.metadata['performance']['efficiency']['cpu'], equals(0.8));
        expect(turn.metadata['gameState']['powerUps'][0], equals('speed'));
        expect(turn.metadata['gameState']['inventory'][1]['quantity'], equals(3));
        expect(turn.metadata['flags']['isSpecialMove'], equals(true));
      });

      test('should handle very large metadata', () {
        // Arrange
        final largeMetadata = <String, dynamic>{};
        for (int i = 0; i < 1000; i++) {
          largeMetadata['key$i'] = 'value$i';
        }

        // Act
        final turn = ModelFactories.createTurn(metadata: largeMetadata);

        // Assert
        expect(turn.metadata.length, equals(1000));
        expect(turn.metadata['key0'], equals('value0'));
        expect(turn.metadata['key999'], equals('value999'));
      });

      test('should handle metadata with null values', () {
        // Arrange
        final metadataWithNulls = {
          'setting1': null,
          'setting2': 'value',
          'setting3': null,
        };

        // Act
        final turn = ModelFactories.createTurn(metadata: metadataWithNulls);

        // Assert
        expect(turn.metadata['setting1'], isNull);
        expect(turn.metadata['setting2'], equals('value'));
        expect(turn.metadata['setting3'], isNull);
      });

      test('should handle metadata with mixed data types', () {
        // Arrange
        final mixedMetadata = {
          'string': 'text',
          'int': 42,
          'double': 3.14,
          'bool': true,
          'list': [1, 2, 3],
          'map': {'nested': 'value'},
          'null': null,
        };

        // Act
        final turn = ModelFactories.createTurn(metadata: mixedMetadata);

        // Assert
        expect(turn.metadata['string'], equals('text'));
        expect(turn.metadata['int'], equals(42));
        expect(turn.metadata['double'], equals(3.14));
        expect(turn.metadata['bool'], equals(true));
        expect(turn.metadata['list'], equals([1, 2, 3]));
        expect(turn.metadata['map']['nested'], equals('value'));
        expect(turn.metadata['null'], isNull);
      });

      test('should handle various timestamp formats', () {
        final timestampFormats = [
          '2023-01-01T00:00:00Z',
          '2023-01-01T00:00:00.000Z',
          '2023-01-01T00:00:00+00:00',
          '2023-01-01T00:00:00.000+00:00',
          '2023-01-01 00:00:00',
          '1672531200000', // Unix timestamp as string
          'invalid-timestamp',
          '',
        ];

        for (final timestamp in timestampFormats) {
          // Act
          final turn = ModelFactories.createTurn(timestamp: timestamp);

          // Assert
          expect(turn.timestamp, equals(timestamp));
        }
      });

      test('should handle malformed JSON gracefully in fromJson', () {
        // Arrange
        final malformedJson = {
          'playerId': 'test-player',
          'move': 'test-move',
          'timestamp': 'test-timestamp',
          'turnNumber': 'not-a-number', // This should cause an error
          'metadata': {},
        };

        // Act & Assert
        expect(() => Turn.fromJson(malformedJson), throwsA(isA<TypeError>()));
      });

      test('should handle missing required fields in fromJson', () {
        // Arrange
        final incompleteJson = {
          'playerId': 'test-player',
          // Missing 'move', 'timestamp', 'turnNumber'
        };

        // Act & Assert
        expect(() => Turn.fromJson(incompleteJson), throwsA(isA<TypeError>()));
      });

      test('should handle extra fields in JSON gracefully', () {
        // Arrange
        final jsonWithExtras = {
          'playerId': 'test-player',
          'move': 'test-move',
          'timestamp': '2023-01-01T00:00:00Z',
          'turnNumber': 1,
          'metadata': {'setting': 'value'},
          'extraField': 'should be ignored',
          'anotherExtra': 123,
        };

        // Act
        final turn = Turn.fromJson(jsonWithExtras);

        // Assert
        expect(turn.playerId, equals('test-player'));
        expect(turn.move, equals('test-move'));
        expect(turn.timestamp, equals('2023-01-01T00:00:00Z'));
        expect(turn.turnNumber, equals(1));
        expect(turn.metadata['setting'], equals('value'));
      });
    });
  });
}
