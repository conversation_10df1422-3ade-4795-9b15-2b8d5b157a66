import 'package:common/models/game.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('Game', () {
    group('constructor', () {
      test('should create a Game with required fields', () {
        // Act
        final game = ModelFactories.createGame();

        // Assert
        expect(game.id, equals('test-game-id'));
        expect(game.name, equals('Test Game'));
        expect(game.description, equals('A test game description'));
        expect(game.config, isA<Map<String, dynamic>>());
      });

      test('should create a Game with custom values', () {
        // Act
        final game = ModelFactories.createGame(
          id: 'custom-id',
          name: 'Custom Game',
          description: 'Custom description',
          config: {'customSetting': 'value'},
        );

        // Assert
        expect(game.id, equals('custom-id'));
        expect(game.name, equals('Custom Game'));
        expect(game.description, equals('Custom description'));
        expect(game.config['customSetting'], equals('value'));
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final game = ModelFactories.createGame();

        // Act
        final json = game.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('test-game-id'));
        expect(json['name'], equals('Test Game'));
        expect(json['description'], equals('A test game description'));
        expect(json['config'], isA<Map<String, dynamic>>());
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createGameJson();

        // Act
        final game = Game.fromJson(jsonData);

        // Assert
        expect(game.id, equals('test-game-id'));
        expect(game.name, equals('Test Game'));
        expect(game.description, equals('A test game description'));
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalGame = ModelFactories.createGame();

        // Act
        final json = originalGame.toJson();
        final deserializedGame = Game.fromJson(json);

        // Assert
        expect(deserializedGame, equals(originalGame));
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        final game1 = ModelFactories.createGame();
        final game2 = ModelFactories.createGame();

        // Assert
        expect(game1, equals(game2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final game1 = ModelFactories.createGame(id: 'id1');
        final game2 = ModelFactories.createGame(id: 'id2');

        // Assert
        expect(game1, isNot(equals(game2)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalGame = ModelFactories.createGame();

        // Act
        final copiedGame = originalGame.copyWith(
          name: 'New Game Name',
          description: 'New description',
        );

        // Assert
        expect(copiedGame.id, equals(originalGame.id));
        expect(copiedGame.name, equals('New Game Name'));
        expect(copiedGame.description, equals('New description'));
        expect(copiedGame.config, equals(originalGame.config));
      });

      test('should create identical copy when no parameters provided', () {
        // Arrange
        final originalGame = ModelFactories.createGame();

        // Act
        final copiedGame = originalGame.copyWith();

        // Assert
        expect(copiedGame, equals(originalGame));
      });
    });

    group('edge cases', () {
      test('should handle empty string values', () {
        // Act
        final game = Game(
          id: '',
          name: '',
          description: '',
          config: {},
        );

        // Assert
        expect(game.id, equals(''));
        expect(game.name, equals(''));
        expect(game.description, equals(''));
        expect(game.config, isEmpty);
      });

      test('should handle very long string values', () {
        // Arrange
        final longString = 'a' * 10000;

        // Act
        final game = Game(
          id: longString,
          name: longString,
          description: longString,
          config: {'longKey': longString},
        );

        // Assert
        expect(game.id, equals(longString));
        expect(game.name, equals(longString));
        expect(game.description, equals(longString));
        expect(game.config['longKey'], equals(longString));
      });

      test('should handle special characters in strings', () {
        // Arrange
        const specialString = '<EMAIL>-123_456!@#\$%^&*()';

        // Act
        final game = Game(
          id: specialString,
          name: specialString,
          description: specialString,
          config: {'special': specialString},
        );

        // Assert
        expect(game.id, equals(specialString));
        expect(game.name, equals(specialString));
        expect(game.description, equals(specialString));
        expect(game.config['special'], equals(specialString));
      });

      test('should handle unicode characters in strings', () {
        // Arrange
        const unicodeString = 'game-🎮-游戏-🚀';

        // Act
        final game = Game(
          id: unicodeString,
          name: unicodeString,
          description: unicodeString,
          config: {'unicode': unicodeString},
        );

        // Assert
        expect(game.id, equals(unicodeString));
        expect(game.name, equals(unicodeString));
        expect(game.description, equals(unicodeString));
        expect(game.config['unicode'], equals(unicodeString));
      });

      test('should handle null description', () {
        // Act
        final game = Game(
          id: 'test-id',
          name: 'Test Game',
          description: null,
          config: {},
        );

        // Assert
        expect(game.description, isNull);
      });

      test('should handle complex nested config', () {
        // Arrange
        final complexConfig = {
          'maxPlayers': 4,
          'turnTimeLimit': 300,
          'gameSettings': {
            'difficulty': 'hard',
            'enablePowerUps': true,
            'powerUpSettings': {
              'spawnRate': 0.1,
              'types': ['speed', 'strength', 'shield'],
            },
          },
          'rules': [
            {'type': 'movement', 'value': 'unlimited'},
            {'type': 'combat', 'value': 'enabled'},
          ],
          'metadata': {
            'version': '1.0.0',
            'author': 'Game Developer',
            'tags': ['strategy', 'multiplayer'],
          },
        };

        // Act
        final game = ModelFactories.createGame(config: complexConfig);

        // Assert
        expect(game.config, equals(complexConfig));
        expect(game.config['gameSettings']['powerUpSettings']['types'][0], equals('speed'));
        expect(game.config['rules'][0]['type'], equals('movement'));
        expect(game.config['metadata']['tags'], contains('strategy'));
      });

      test('should handle very large config', () {
        // Arrange
        final largeConfig = <String, dynamic>{};
        for (int i = 0; i < 1000; i++) {
          largeConfig['key$i'] = 'value$i';
        }

        // Act
        final game = ModelFactories.createGame(config: largeConfig);

        // Assert
        expect(game.config.length, equals(1000));
        expect(game.config['key0'], equals('value0'));
        expect(game.config['key999'], equals('value999'));
      });

      test('should handle config with null values', () {
        // Arrange
        final configWithNulls = {
          'setting1': null,
          'setting2': 'value',
          'setting3': null,
        };

        // Act
        final game = ModelFactories.createGame(config: configWithNulls);

        // Assert
        expect(game.config['setting1'], isNull);
        expect(game.config['setting2'], equals('value'));
        expect(game.config['setting3'], isNull);
      });

      test('should handle config with mixed data types', () {
        // Arrange
        final mixedConfig = {
          'string': 'text',
          'int': 42,
          'double': 3.14,
          'bool': true,
          'list': [1, 2, 3],
          'map': {'nested': 'value'},
          'null': null,
        };

        // Act
        final game = ModelFactories.createGame(config: mixedConfig);

        // Assert
        expect(game.config['string'], equals('text'));
        expect(game.config['int'], equals(42));
        expect(game.config['double'], equals(3.14));
        expect(game.config['bool'], equals(true));
        expect(game.config['list'], equals([1, 2, 3]));
        expect(game.config['map']['nested'], equals('value'));
        expect(game.config['null'], isNull);
      });

      test('should handle malformed JSON gracefully in fromJson', () {
        // Arrange
        final malformedJson = {
          'id': 'test-id',
          'name': 'Test Game',
          'description': 'Test description',
          'config': null, // This should cause an error
        };

        // Act & Assert
        expect(() => Game.fromJson(malformedJson), throwsA(isA<TypeError>()));
      });

      test('should handle missing required fields in fromJson', () {
        // Arrange
        final incompleteJson = {
          'name': 'Test Game',
          'description': 'Test description',
          // Missing 'id' and 'config'
        };

        // Act & Assert
        expect(() => Game.fromJson(incompleteJson), throwsA(isA<TypeError>()));
      });

      test('should handle extra fields in JSON gracefully', () {
        // Arrange
        final jsonWithExtras = {
          'id': 'test-id',
          'name': 'Test Game',
          'description': 'Test description',
          'config': {'setting': 'value'},
          'extraField': 'should be ignored',
          'anotherExtra': 123,
        };

        // Act
        final game = Game.fromJson(jsonWithExtras);

        // Assert
        expect(game.id, equals('test-id'));
        expect(game.name, equals('Test Game'));
        expect(game.description, equals('Test description'));
        expect(game.config['setting'], equals('value'));
      });
    });
  });
}
