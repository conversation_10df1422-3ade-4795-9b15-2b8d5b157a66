import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('PlayerSlot', () {
    group('constructor', () {
      test('should create a PlayerSlot with required fields', () {
        // Act
        final slot = ModelFactories.createPlayerSlot();

        // Assert
        expect(slot.id, equals('test-slot-id'));
        expect(slot.playerId, isNull);
        expect(slot.type, equals(PlayerType.humanLocal));
        expect(slot.playerClassId, equals('test-class-id'));
        expect(slot.name, equals('Test Slot'));
      });

      test('should create a PlayerSlot with custom values', () {
        // Act
        final slot = ModelFactories.createPlayerSlot(
          id: 'custom-slot',
          playerId: 'custom-player',
          type: PlayerType.botNetwork,
          playerClassId: 'custom-class',
          name: 'Custom Slot',
        );

        // Assert
        expect(slot.id, equals('custom-slot'));
        expect(slot.playerId, equals('custom-player'));
        expect(slot.type, equals(PlayerType.botNetwork));
        expect(slot.playerClassId, equals('custom-class'));
        expect(slot.name, equals('Custom Slot'));
      });

      test('should allow null optional fields', () {
        // Act
        final slot = PlayerSlot(
          id: 'test-id',
          type: PlayerType.humanLocal,
          playerClassId: 'class-id',
        );

        // Assert
        expect(slot.playerId, isNull);
        expect(slot.name, isNull);
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final slot = ModelFactories.createPlayerSlot(
          id: 'json-slot',
          playerId: 'json-player',
          type: PlayerType.humanNetwork,
          playerClassId: 'json-class',
          name: 'JSON Slot',
        );

        // Act
        final json = slot.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('json-slot'));
        expect(json['playerId'], equals('json-player'));
        expect(json['type'], equals('humanNetwork'));
        expect(json['playerClassId'], equals('json-class'));
        expect(json['name'], equals('JSON Slot'));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerSlotJson(
          id: 'json-slot',
          playerId: 'json-player',
          type: 'humanNetwork',
          playerClassId: 'json-class',
          name: 'JSON Slot',
        );

        // Act
        final slot = PlayerSlot.fromJson(jsonData);

        // Assert
        expect(slot.id, equals('json-slot'));
        expect(slot.playerId, equals('json-player'));
        expect(slot.type, equals(PlayerType.humanNetwork));
        expect(slot.playerClassId, equals('json-class'));
        expect(slot.name, equals('JSON Slot'));
      });

      test('should handle all PlayerType values in JSON', () {
        final testCases = [
          ('humanLocal', PlayerType.humanLocal),
          ('humanNetwork', PlayerType.humanNetwork),
          ('botLocal', PlayerType.botLocal),
          ('botNetwork', PlayerType.botNetwork),
        ];

        for (final (jsonType, enumType) in testCases) {
          // Arrange
          final jsonData = ModelFactories.createPlayerSlotJson(type: jsonType);

          // Act
          final slot = PlayerSlot.fromJson(jsonData);

          // Assert
          expect(slot.type, equals(enumType), reason: 'Failed for type: $jsonType');
        }
      });

      test('should handle unknown PlayerType in JSON with default', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerSlotJson(type: 'unknown');

        // Act
        final slot = PlayerSlot.fromJson(jsonData);

        // Assert
        expect(slot.type, equals(PlayerType.humanLocal));
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalSlot = ModelFactories.createPlayerSlot();

        // Act
        final json = originalSlot.toJson();
        final deserializedSlot = PlayerSlot.fromJson(json);

        // Assert
        expect(deserializedSlot, equals(originalSlot));
      });

      test('should handle null optional fields in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerSlotJson(
          playerId: null,
          name: null,
        );

        // Act
        final slot = PlayerSlot.fromJson(jsonData);

        // Assert
        expect(slot.playerId, isNull);
        expect(slot.name, isNull);
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        final slot1 = ModelFactories.createPlayerSlot();
        final slot2 = ModelFactories.createPlayerSlot();

        // Assert
        expect(slot1, equals(slot2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final slot1 = ModelFactories.createPlayerSlot(id: 'id1');
        final slot2 = ModelFactories.createPlayerSlot(id: 'id2');

        // Assert
        expect(slot1, isNot(equals(slot2)));
      });

      test('should not be equal when types differ', () {
        // Arrange
        final slot1 = ModelFactories.createPlayerSlot(type: PlayerType.humanLocal);
        final slot2 = ModelFactories.createPlayerSlot(type: PlayerType.botLocal);

        // Assert
        expect(slot1, isNot(equals(slot2)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalSlot = ModelFactories.createPlayerSlot();

        // Act
        final copiedSlot = originalSlot.copyWith(
          playerId: 'new-player',
          type: PlayerType.botLocal,
          name: 'New Name',
        );

        // Assert
        expect(copiedSlot.id, equals(originalSlot.id));
        expect(copiedSlot.playerId, equals('new-player'));
        expect(copiedSlot.type, equals(PlayerType.botLocal));
        expect(copiedSlot.playerClassId, equals(originalSlot.playerClassId));
        expect(copiedSlot.name, equals('New Name'));
      });
    });

    group('fromPlayerClass factory', () {
      test('should create PlayerSlot from PlayerClass with all fields', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass(
          id: 'warrior-class',
          name: 'Warrior',
          defaultPlayerName: 'Brave Warrior',
        );

        // Act
        final slot = PlayerSlot.fromPlayerClass(playerClass);

        // Assert
        expect(slot.id, equals('warrior-class'));
        expect(slot.playerId, isNull);
        expect(slot.type, equals(PlayerType.humanLocal));
        expect(slot.playerClassId, equals('warrior-class'));
        expect(slot.name, equals('Brave Warrior'));
      });

      test('should create PlayerSlot from PlayerClass with null defaultPlayerName', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass(
          id: 'mage-class',
          name: 'Mage',
          defaultPlayerName: null,
        );

        // Act
        final slot = PlayerSlot.fromPlayerClass(playerClass);

        // Assert
        expect(slot.id, equals('mage-class'));
        expect(slot.playerId, isNull);
        expect(slot.type, equals(PlayerType.humanLocal));
        expect(slot.playerClassId, equals('mage-class'));
        expect(slot.name, equals('Mage')); // Falls back to class name
      });

      test('should create PlayerSlot from PlayerClass with empty defaultPlayerName', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass(
          id: 'archer-class',
          name: 'Archer',
          defaultPlayerName: '',
        );

        // Act
        final slot = PlayerSlot.fromPlayerClass(playerClass);

        // Assert
        expect(slot.id, equals('archer-class'));
        expect(slot.name, equals('')); // Uses empty defaultPlayerName
      });

      test('should always set type to humanLocal', () {
        // Arrange
        final playerClass = ModelFactories.createPlayerClass();

        // Act
        final slot = PlayerSlot.fromPlayerClass(playerClass);

        // Assert
        expect(slot.type, equals(PlayerType.humanLocal));
      });

      test('should handle special characters in PlayerClass fields', () {
        // Arrange
        const specialString = '<EMAIL>-123_456!@#\$%^&*()';
        final playerClass = ModelFactories.createPlayerClass(
          id: specialString,
          name: specialString,
          defaultPlayerName: specialString,
        );

        // Act
        final slot = PlayerSlot.fromPlayerClass(playerClass);

        // Assert
        expect(slot.id, equals(specialString));
        expect(slot.playerClassId, equals(specialString));
        expect(slot.name, equals(specialString));
      });

      test('should handle unicode characters in PlayerClass fields', () {
        // Arrange
        const unicodeString = 'class-🎮-游戏-🚀';
        final playerClass = ModelFactories.createPlayerClass(
          id: unicodeString,
          name: unicodeString,
          defaultPlayerName: unicodeString,
        );

        // Act
        final slot = PlayerSlot.fromPlayerClass(playerClass);

        // Assert
        expect(slot.id, equals(unicodeString));
        expect(slot.playerClassId, equals(unicodeString));
        expect(slot.name, equals(unicodeString));
      });

      test('should handle very long strings in PlayerClass fields', () {
        // Arrange
        final longString = 'a' * 1000;
        final playerClass = ModelFactories.createPlayerClass(
          id: longString,
          name: longString,
          defaultPlayerName: longString,
        );

        // Act
        final slot = PlayerSlot.fromPlayerClass(playerClass);

        // Assert
        expect(slot.id, equals(longString));
        expect(slot.playerClassId, equals(longString));
        expect(slot.name, equals(longString));
      });
    });

    group('edge cases', () {
      test('should handle empty string values', () {
        // Act
        final slot = PlayerSlot(
          id: '',
          playerId: '',
          type: PlayerType.humanLocal,
          playerClassId: '',
          name: '',
        );

        // Assert
        expect(slot.id, equals(''));
        expect(slot.playerId, equals(''));
        expect(slot.playerClassId, equals(''));
        expect(slot.name, equals(''));
      });

      test('should handle very long string values', () {
        // Arrange
        final longString = 'a' * 10000;

        // Act
        final slot = PlayerSlot(
          id: longString,
          playerId: longString,
          type: PlayerType.humanLocal,
          playerClassId: longString,
          name: longString,
        );

        // Assert
        expect(slot.id, equals(longString));
        expect(slot.playerId, equals(longString));
        expect(slot.playerClassId, equals(longString));
        expect(slot.name, equals(longString));
      });

      test('should handle special characters in strings', () {
        // Arrange
        const specialString = '<EMAIL>-123_456!@#\$%^&*()';

        // Act
        final slot = PlayerSlot(
          id: specialString,
          playerId: specialString,
          type: PlayerType.humanLocal,
          playerClassId: specialString,
          name: specialString,
        );

        // Assert
        expect(slot.id, equals(specialString));
        expect(slot.playerId, equals(specialString));
        expect(slot.playerClassId, equals(specialString));
        expect(slot.name, equals(specialString));
      });

      test('should handle unicode characters in strings', () {
        // Arrange
        const unicodeString = 'slot-🎮-游戏-🚀';

        // Act
        final slot = PlayerSlot(
          id: unicodeString,
          playerId: unicodeString,
          type: PlayerType.humanLocal,
          playerClassId: unicodeString,
          name: unicodeString,
        );

        // Assert
        expect(slot.id, equals(unicodeString));
        expect(slot.playerId, equals(unicodeString));
        expect(slot.playerClassId, equals(unicodeString));
        expect(slot.name, equals(unicodeString));
      });

      test('should handle malformed JSON gracefully in fromJson', () {
        // Arrange
        final malformedJson = {
          'id': 'test-id',
          'type': 'invalidType', // This should default to humanLocal
          'playerClassId': 'test-class',
        };

        // Act
        final slot = PlayerSlot.fromJson(malformedJson);

        // Assert
        expect(slot.type, equals(PlayerType.humanLocal)); // Default fallback
      });

      test('should handle missing required fields in fromJson', () {
        // Arrange
        final incompleteJson = {
          'playerId': 'test-player',
          // Missing 'id', 'type', and 'playerClassId'
        };

        // Act & Assert
        expect(() => PlayerSlot.fromJson(incompleteJson), throwsA(isA<TypeError>()));
      });

      test('should handle extra fields in JSON gracefully', () {
        // Arrange
        final jsonWithExtras = {
          'id': 'test-slot',
          'playerId': 'test-player',
          'type': 'humanLocal',
          'playerClassId': 'test-class',
          'name': 'Test Slot',
          'extraField': 'should be ignored',
          'anotherExtra': 123,
        };

        // Act
        final slot = PlayerSlot.fromJson(jsonWithExtras);

        // Assert
        expect(slot.id, equals('test-slot'));
        expect(slot.playerId, equals('test-player'));
        expect(slot.type, equals(PlayerType.humanLocal));
        expect(slot.playerClassId, equals('test-class'));
        expect(slot.name, equals('Test Slot'));
      });
    });
  });
}
