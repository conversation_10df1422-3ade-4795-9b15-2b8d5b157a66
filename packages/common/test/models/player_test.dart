import 'package:common/models/player.dart';
import 'package:common/models/player_type.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('Player', () {
    group('constructor', () {
      test('should create a Player with required fields', () {
        // Act
        final player = ModelFactories.createPlayer();

        // Assert
        expect(player.id, equals('test-player-id'));
        expect(player.name, equals('Test Player'));
        expect(player.type, equals(PlayerType.humanNetwork));
        expect(player.metadata, isEmpty);
      });

      test('should create a Player with custom values', () {
        // Act
        final player = ModelFactories.createPlayer(
          id: 'custom-id',
          name: 'Custom Player',
          type: PlayerType.botLocal,
          metadata: {'skill': 'expert'},
        );

        // Assert
        expect(player.id, equals('custom-id'));
        expect(player.name, equals('Custom Player'));
        expect(player.type, equals(PlayerType.botLocal));
        expect(player.metadata['skill'], equals('expert'));
      });

      test('should use default values when not specified', () {
        // Act
        final player = Player(id: 'test-id');

        // Assert
        expect(player.id, equals('test-id'));
        expect(player.name, isNull);
        expect(player.type, equals(PlayerType.humanNetwork));
        expect(player.metadata, isEmpty);
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final player = ModelFactories.createPlayer(
          id: 'json-id',
          name: 'JSON Player',
          type: PlayerType.humanLocal,
          metadata: {'level': 5},
        );

        // Act
        final json = player.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('json-id'));
        expect(json['name'], equals('JSON Player'));
        expect(json['type'], equals('humanLocal'));
        expect(json['metadata'], isA<Map<String, dynamic>>());
        expect(json['metadata']['level'], equals(5));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerJson(
          id: 'json-id',
          name: 'JSON Player',
          type: 'humanLocal',
          metadata: {'level': 5},
        );

        // Act
        final player = Player.fromJson(jsonData);

        // Assert
        expect(player.id, equals('json-id'));
        expect(player.name, equals('JSON Player'));
        expect(player.type, equals(PlayerType.humanLocal));
        expect(player.metadata['level'], equals(5));
      });

      test('should handle all PlayerType values in JSON', () {
        final testCases = [
          PlayerType.humanLocal,
          PlayerType.humanNetwork,
          PlayerType.botLocal,
          PlayerType.botNetwork,
        ];

        for (final playerType in testCases) {
          // Arrange
          final player = ModelFactories.createPlayer(type: playerType);

          // Act
          final json = player.toJson();
          final deserializedPlayer = Player.fromJson(json);

          // Assert
          expect(deserializedPlayer.type, equals(playerType));
        }
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalPlayer = ModelFactories.createPlayer(
          metadata: {'complex': {'nested': 'value'}},
        );

        // Act
        final json = originalPlayer.toJson();
        final deserializedPlayer = Player.fromJson(json);

        // Assert
        expect(deserializedPlayer, equals(originalPlayer));
      });

      test('should handle null name in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerJson(name: null);

        // Act
        final player = Player.fromJson(jsonData);

        // Assert
        expect(player.name, isNull);
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        final player1 = ModelFactories.createPlayer();
        final player2 = ModelFactories.createPlayer();

        // Assert
        expect(player1, equals(player2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final player1 = ModelFactories.createPlayer(id: 'id1');
        final player2 = ModelFactories.createPlayer(id: 'id2');

        // Assert
        expect(player1, isNot(equals(player2)));
      });

      test('should not be equal when types differ', () {
        // Arrange
        final player1 = ModelFactories.createPlayer(type: PlayerType.humanLocal);
        final player2 = ModelFactories.createPlayer(type: PlayerType.botLocal);

        // Assert
        expect(player1, isNot(equals(player2)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalPlayer = ModelFactories.createPlayer();

        // Act
        final copiedPlayer = originalPlayer.copyWith(
          name: 'New Name',
          type: PlayerType.botLocal,
        );

        // Assert
        expect(copiedPlayer.id, equals(originalPlayer.id));
        expect(copiedPlayer.name, equals('New Name'));
        expect(copiedPlayer.type, equals(PlayerType.botLocal));
        expect(copiedPlayer.metadata, equals(originalPlayer.metadata));
      });
    });
  });
}
