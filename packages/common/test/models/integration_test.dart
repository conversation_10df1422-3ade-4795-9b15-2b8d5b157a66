import 'package:common/models/game.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player.dart';
import 'package:common/models/player_class.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/turn.dart';
import 'package:common/models/user.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('Model Integration Tests', () {
    group('complete game scenario', () {
      test('should handle full game lifecycle with all models', () {
        // Arrange - Create a complete game scenario
        final game = ModelFactories.createGame(
          id: 'chess-game',
          name: 'Chess',
          description: 'Classic chess game',
          config: {
            'maxPlayers': 2,
            'turnTimeLimit': 600,
            'boardSize': '8x8',
          },
        );

        final user1 = ModelFactories.createUser(id: 'user1');
        final user2 = ModelFactories.createUser(id: 'user2');

        final playerClass1 = ModelFactories.createPlayerClass(
          id: 'white-pieces',
          name: 'White Player',
          defaultPlayerName: 'White',
        );

        final playerClass2 = ModelFactories.createPlayerClass(
          id: 'black-pieces',
          name: 'Black Player',
          defaultPlayerName: 'Black',
        );

        final player1 = ModelFactories.createPlayer(
          id: user1.id,
          name: 'Alice',
          type: PlayerType.humanNetwork,
        );

        final player2 = ModelFactories.createPlayer(
          id: user2.id,
          name: 'Bob',
          type: PlayerType.humanNetwork,
        );

        final slot1 = PlayerSlot.fromPlayerClass(playerClass1).copyWith(
          playerId: player1.id,
        );

        final slot2 = PlayerSlot.fromPlayerClass(playerClass2).copyWith(
          playerId: player2.id,
        );

        final turn1 = ModelFactories.createTurn(
          playerId: player1.id,
          move: 'e2-e4',
          turnNumber: 1,
          metadata: {
            'piece': 'pawn',
            'from': 'e2',
            'to': 'e4',
            'duration': 5000,
          },
        );

        final turn2 = ModelFactories.createTurn(
          playerId: player2.id,
          move: 'e7-e5',
          turnNumber: 2,
          metadata: {
            'piece': 'pawn',
            'from': 'e7',
            'to': 'e5',
            'duration': 3000,
          },
        );

        final gameMatch = ModelFactories.createGameMatch(
          id: 'match-123',
          gameTypeId: game.id,
          creatorId: user1.id,
          gameName: 'Alice vs Bob Chess',
          status: MatchStatus.active,
          isOpenForJoining: false,
          playerSlots: [slot1, slot2],
          players: [player1, player2],
          turns: [turn1, turn2],
          currentTurn: 2,
        );

        // Act & Assert - Verify all relationships work correctly
        expect(gameMatch.gameTypeId, equals(game.id));
        expect(gameMatch.creatorId, equals(user1.id));
        expect(gameMatch.players.length, equals(2));
        expect(gameMatch.playerSlots.length, equals(2));
        expect(gameMatch.turns.length, equals(2));

        // Verify player relationships
        expect(gameMatch.players[0].id, equals(user1.id));
        expect(gameMatch.players[1].id, equals(user2.id));
        expect(gameMatch.playerSlots[0].playerId, equals(player1.id));
        expect(gameMatch.playerSlots[1].playerId, equals(player2.id));

        // Verify turn relationships
        expect(gameMatch.turns[0].playerId, equals(player1.id));
        expect(gameMatch.turns[1].playerId, equals(player2.id));
        expect(gameMatch.turns[0].turnNumber, equals(1));
        expect(gameMatch.turns[1].turnNumber, equals(2));

        // Verify game state
        expect(gameMatch.status, equals(MatchStatus.active));
        expect(gameMatch.currentTurn, equals(2));
        expect(gameMatch.isOpenForJoining, isFalse);
      });

      test('should handle JSON serialization of complete game scenario', () {
        // Arrange
        final completeMatch = ModelFactories.createCompleteGameMatch(
          playerCount: 4,
          turnCount: 10,
        );

        // Act - Test that we can serialize the match to JSON
        final json = completeMatch.toJson();

        // Assert - Verify the JSON structure contains the expected data
        expect(json, isA<Map<String, dynamic>>());
        expect(json['players'], isA<List>());
        expect(json['playerSlots'], isA<List>());
        expect(json['turns'], isA<List>());
        expect((json['players'] as List).length, equals(4));
        expect((json['playerSlots'] as List).length, equals(4));
        expect((json['turns'] as List).length, equals(10));

        // Verify basic match properties
        expect(json['currentTurn'], equals(10));
        expect(json['status'], equals('active'));
        expect(json['id'], isNotNull);
        expect(json['gameTypeId'], isNotNull);
        expect(json['creatorId'], isNotNull);

        // Verify that the lists contain the expected number of items
        // Note: The nested objects may not be fully serialized to JSON maps
        // depending on the implementation, but the structure should be correct
        expect(json.containsKey('players'), isTrue);
        expect(json.containsKey('playerSlots'), isTrue);
        expect(json.containsKey('turns'), isTrue);

        // Check for the boolean field - it's serialized as snake_case
        expect(json.keys, contains('is_open_for_joining'));
        expect(json.keys, contains('created_at'));
        expect(json.keys, contains('updated_at'));
      });
    });

    group('cross-model validation', () {
      test('should maintain referential integrity between models', () {
        // Arrange
        final playerId = 'player-123';
        final slotId = 'slot-456';
        final classId = 'class-789';

        final player = ModelFactories.createPlayer(id: playerId);
        final playerClass = ModelFactories.createPlayerClass(id: classId);
        final slot = ModelFactories.createPlayerSlot(
          id: slotId,
          playerId: playerId,
          playerClassId: classId,
        );

        // Act & Assert
        expect(slot.playerId, equals(player.id));
        expect(slot.playerClassId, equals(playerClass.id));
      });

      test('should handle player type consistency across models', () {
        // Test all player types work consistently
        for (final playerType in PlayerType.values) {
          // Arrange
          final player = ModelFactories.createPlayer(type: playerType);
          final slot = ModelFactories.createPlayerSlot(type: playerType);

          // Act & Assert
          expect(player.type, equals(playerType));
          expect(slot.type, equals(playerType));
          expect(player.type.isHuman, equals(slot.type.isHuman));
          expect(player.type.isBot, equals(slot.type.isBot));
          expect(player.type.isLocal, equals(slot.type.isLocal));
        }
      });

      test('should handle match status transitions correctly', () {
        // Test valid status transitions
        final statusTransitions = [
          (MatchStatus.open, MatchStatus.active),
          (MatchStatus.open, MatchStatus.closed),
          (MatchStatus.active, MatchStatus.closed),
        ];

        for (final (fromStatus, toStatus) in statusTransitions) {
          // Arrange
          final match = ModelFactories.createGameMatch(status: fromStatus);

          // Act
          final updatedMatch = match.copyWith(status: toStatus);

          // Assert
          expect(match.status, equals(fromStatus));
          expect(updatedMatch.status, equals(toStatus));
        }
      });
    });

    group('performance with large datasets', () {
      test('should handle large number of players efficiently', () {
        // Arrange
        const playerCount = 100;
        final players = List.generate(
          playerCount,
          (index) => ModelFactories.createPlayer(
            id: 'player-$index',
            name: 'Player $index',
          ),
        );

        final playerSlots = List.generate(
          playerCount,
          (index) => ModelFactories.createPlayerSlot(
            id: 'slot-$index',
            playerId: 'player-$index',
          ),
        );

        // Act
        final stopwatch = Stopwatch()..start();
        final match = ModelFactories.createGameMatch(
          players: players,
          playerSlots: playerSlots,
        );
        stopwatch.stop();

        // Assert
        expect(match.players.length, equals(playerCount));
        expect(match.playerSlots.length, equals(playerCount));
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast
      });

      test('should handle large number of turns efficiently', () {
        // Arrange
        const turnCount = 1000;
        final turns = List.generate(
          turnCount,
          (index) => ModelFactories.createTurn(
            turnNumber: index + 1,
            move: 'move-${index + 1}',
            metadata: {
              'index': index,
              'timestamp': DateTime.now().millisecondsSinceEpoch + index,
            },
          ),
        );

        // Act
        final stopwatch = Stopwatch()..start();
        final match = ModelFactories.createGameMatch(turns: turns);
        stopwatch.stop();

        // Assert
        expect(match.turns.length, equals(turnCount));
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast
      });

      test('should handle JSON serialization of large datasets efficiently', () {
        // Arrange
        final largeMatch = ModelFactories.createCompleteGameMatch(
          playerCount: 50,
          turnCount: 200,
        );

        // Act
        final stopwatch = Stopwatch()..start();
        final json = largeMatch.toJson();

        // Verify JSON structure without full deserialization to avoid type issues
        expect(json['players'], isA<List>());
        expect(json['turns'], isA<List>());
        expect((json['players'] as List).length, equals(50));
        expect((json['turns'] as List).length, equals(200));

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should complete within 1 second
      });
    });

    group('error handling and edge cases', () {
      test('should handle mixed valid and invalid data gracefully', () {
        // Arrange - Create a match with some valid and some edge case data
        final players = [
          ModelFactories.createPlayer(id: 'valid-player'),
          ModelFactories.createPlayer(id: '', name: ''), // Edge case: empty strings
          ModelFactories.createPlayer(id: 'unicode-🎮', name: 'Player-游戏'), // Unicode
        ];

        final turns = [
          ModelFactories.createTurn(turnNumber: 1),
          ModelFactories.createTurn(turnNumber: 0), // Edge case: zero turn
          ModelFactories.createTurn(turnNumber: -1), // Edge case: negative turn
        ];

        // Act
        final match = ModelFactories.createGameMatch(
          players: players,
          turns: turns,
        );

        // Assert
        expect(match.players.length, equals(3));
        expect(match.turns.length, equals(3));
        expect(match.players[1].id, equals(''));
        expect(match.players[2].id, equals('unicode-🎮'));
        expect(match.turns[1].turnNumber, equals(0));
        expect(match.turns[2].turnNumber, equals(-1));
      });

      test('should maintain data integrity during complex operations', () {
        // Arrange
        final originalMatch = ModelFactories.createCompleteGameMatch(
          playerCount: 3,
          turnCount: 5,
        );

        // Act - Perform multiple operations
        final modifiedMatch = originalMatch
            .copyWith(status: MatchStatus.closed)
            .copyWith(currentTurn: originalMatch.currentTurn + 1)
            .copyWith(gameName: 'Modified Game');

        // Assert - Original should be unchanged
        expect(originalMatch.status, equals(MatchStatus.active));
        expect(originalMatch.currentTurn, equals(5));
        expect(originalMatch.gameName, isNull);

        // Modified should have changes
        expect(modifiedMatch.status, equals(MatchStatus.closed));
        expect(modifiedMatch.currentTurn, equals(6));
        expect(modifiedMatch.gameName, equals('Modified Game'));

        // Other data should be preserved
        expect(modifiedMatch.players.length, equals(originalMatch.players.length));
        expect(modifiedMatch.turns.length, equals(originalMatch.turns.length));
        expect(modifiedMatch.id, equals(originalMatch.id));
      });
    });
  });
}
