import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:test/test.dart';

void main() {
  group('GenerateIdIfNeededConverter', () {
    late GenerateIdIfNeededConverter converter;

    setUp(() {
      converter = const GenerateIdIfNeededConverter();
    });

    group('fromJson', () {
      test('should return the provided string ID when given a valid string', () {
        // Arrange
        const testId = 'existing-id-123';

        // Act
        final result = converter.fromJson(testId);

        // Assert
        expect(result, equals(testId));
      });

      test('should generate a new UUID when given null', () {
        // Act
        final result = converter.fromJson(null);

        // Assert
        expect(result, isA<String>());
        expect(result.length, equals(36)); // UUID v4 length
        expect(result.contains('-'), isTrue); // UUID format check
      });

      test('should generate a new UUID when given non-string value', () {
        // Act
        final result1 = converter.from<PERSON>son(123);
        final result2 = converter.fromJson({'key': 'value'});

        // Assert
        expect(result1, isA<String>());
        expect(result1.length, equals(36));
        expect(result1.contains('-'), isTrue);

        expect(result2, isA<String>());
        expect(result2.length, equals(36));
        expect(result2.contains('-'), isTrue);
      });

      test('should generate different UUIDs on multiple calls', () {
        // Act
        final result1 = converter.fromJson(null);
        final result2 = converter.fromJson(null);

        // Assert
        expect(result1, isNot(equals(result2)));
      });
    });

    group('toJson', () {
      test('should return the same string value', () {
        // Arrange
        const testId = 'test-id-123';

        // Act
        final result = converter.toJson(testId);

        // Assert
        expect(result, equals(testId));
      });
    });
  });
}
