import 'package:common/models/converters/default_timestamp_converter.dart';
import 'package:test/test.dart';

void main() {
  group('DefaultTimestampConverter', () {
    late DefaultTimestampConverter converter;

    setUp(() {
      converter = const DefaultTimestampConverter();
    });

    group('fromJson', () {
      test('should return the provided timestamp when given a valid int', () {
        // Arrange
        const testTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC

        // Act
        final result = converter.fromJson(testTimestamp);

        // Assert
        expect(result, equals(testTimestamp));
      });

      test('should generate current timestamp when given null', () {
        // Arrange
        final beforeCall = DateTime.now().millisecondsSinceEpoch;

        // Act
        final result = converter.fromJson(null);

        // Assert
        final afterCall = DateTime.now().millisecondsSinceEpoch;
        expect(result, isA<int>());
        expect(result, greaterThanOrEqualTo(beforeCall));
        expect(result, lessThanOrEqualTo(afterCall));
      });

      test('should generate current timestamp when given non-int value', () {
        // Arrange
        final beforeCall = DateTime.now().millisecondsSinceEpoch;

        // Act
        final result1 = converter.fromJson('not-a-number');
        final result2 = converter.fromJson({'key': 'value'});
        final result3 = converter.fromJson([1, 2, 3]);

        // Assert
        final afterCall = DateTime.now().millisecondsSinceEpoch;
        
        expect(result1, isA<int>());
        expect(result1, greaterThanOrEqualTo(beforeCall));
        expect(result1, lessThanOrEqualTo(afterCall));

        expect(result2, isA<int>());
        expect(result2, greaterThanOrEqualTo(beforeCall));
        expect(result2, lessThanOrEqualTo(afterCall));

        expect(result3, isA<int>());
        expect(result3, greaterThanOrEqualTo(beforeCall));
        expect(result3, lessThanOrEqualTo(afterCall));
      });

      test('should handle zero timestamp', () {
        // Act
        final result = converter.fromJson(0);

        // Assert
        expect(result, equals(0));
      });

      test('should handle negative timestamp', () {
        // Arrange
        const negativeTimestamp = -1000;

        // Act
        final result = converter.fromJson(negativeTimestamp);

        // Assert
        expect(result, equals(negativeTimestamp));
      });

      test('should handle very large timestamp', () {
        // Arrange
        const largeTimestamp = 9999999999999;

        // Act
        final result = converter.fromJson(largeTimestamp);

        // Assert
        expect(result, equals(largeTimestamp));
      });
    });

    group('toJson', () {
      test('should return the same timestamp value', () {
        // Arrange
        const testTimestamp = 1640995200000;

        // Act
        final result = converter.toJson(testTimestamp);

        // Assert
        expect(result, equals(testTimestamp));
        expect(result, isA<int>());
      });

      test('should handle zero timestamp', () {
        // Act
        final result = converter.toJson(0);

        // Assert
        expect(result, equals(0));
      });

      test('should handle negative timestamp', () {
        // Arrange
        const negativeTimestamp = -1000;

        // Act
        final result = converter.toJson(negativeTimestamp);

        // Assert
        expect(result, equals(negativeTimestamp));
      });
    });

    group('round-trip conversion', () {
      test('should maintain timestamp values through round-trip conversion', () {
        // Arrange
        const originalTimestamp = 1640995200000;

        // Act
        final fromJsonResult = converter.fromJson(originalTimestamp);
        final toJsonResult = converter.toJson(fromJsonResult);

        // Assert
        expect(fromJsonResult, equals(originalTimestamp));
        expect(toJsonResult, equals(originalTimestamp));
      });

      test('should generate valid timestamp for null input and convert back', () {
        // Act
        final fromJsonResult = converter.fromJson(null);
        final toJsonResult = converter.toJson(fromJsonResult);

        // Assert
        expect(fromJsonResult, isA<int>());
        expect(toJsonResult, equals(fromJsonResult));
      });
    });

    group('timestamp validation', () {
      test('generated timestamps should be reasonable', () {
        // Arrange
        final year2020 = DateTime(2020).millisecondsSinceEpoch;
        final year2030 = DateTime(2030).millisecondsSinceEpoch;

        // Act
        final result = converter.fromJson(null);

        // Assert
        expect(result, greaterThan(year2020));
        expect(result, lessThan(year2030));
      });

      test('multiple calls should generate different timestamps', () {
        // Act
        final result1 = converter.fromJson(null);
        // Small delay to ensure different timestamps
        final result2 = converter.fromJson(null);

        // Assert
        // Note: These might be the same if called very quickly,
        // but they should at least be close in time
        expect(result1, isA<int>());
        expect(result2, isA<int>());
        expect((result2 - result1).abs(), lessThan(1000)); // Within 1 second
      });
    });
  });
}
