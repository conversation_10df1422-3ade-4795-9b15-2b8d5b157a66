check_flutterfire_cli() {
  if ! command -v flutterfire &> /dev/null; then
    return 1
  else
    return 0
  fi
}

# Function to install FlutterFire CLI
# install_flutterfire_cli() {
#   echo "FlutterFire CLI is not installed. Installing now..."
#   dart pub global activate flutterfire_cli
#   echo "FlutterFire CLI has been installed successfully."
# }

# # Main script execution
# if check_flutterfire_cli; then
#   echo "FlutterFire CLI is already installed."
# else
#   install_flutterfire_cli
# fi

# 📦 Install the dart_frog cli from pub.dev
dart pub global activate dart_frog_cli

flutter clean
dart pub get

cd packages/dauntless_server
dart pub get
dart run --enable-experiment=class-modifiers build_runner build --delete-conflicting-outputs
cd ../common
dart pub get
dart run --enable-experiment=class-modifiers build_runner build --delete-conflicting-outputs
cd ../.. # Go back to the root directory

# Use the required flags for freezed 3.0.4 with Dart 3.7.2
dart run --enable-experiment=class-modifiers build_runner build --delete-conflicting-outputs