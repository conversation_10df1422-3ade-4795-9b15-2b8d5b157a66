{"applyElevationOverlayColor": true, "brightness": "dark", "buttonTheme": {"alignedDropdown": false, "colorScheme": {"background": "#ff000000", "brightness": "dark", "error": "#ffffb4ab", "errorContainer": "#ff93000a", "inversePrimary": "#ff006a60", "inverseSurface": "#ffdde4e1", "onBackground": "#ffdde4e1", "onError": "#ff690005", "onErrorContainer": "#ffffdad6", "onInverseSurface": "#ff2b3230", "onPrimary": "#ff003731", "onPrimaryContainer": "#ff9ef2e4", "onSecondary": "#ff1c3531", "onSecondaryContainer": "#ffcce8e2", "onSurface": "#ffdde4e1", "onSurfaceVariant": "#ffbec9c6", "onTertiary": "#ff153349", "onTertiaryContainer": "#ffcce5ff", "outline": "#ff899390", "outlineVariant": "#ff3f4947", "primary": "#ff82d5c8", "primaryContainer": "#ff005048", "scrim": "#ff000000", "secondary": "#ffb1ccc6", "secondaryContainer": "#ff334b47", "shadow": "#ff000000", "surface": "#ff000000", "surfaceContainerHighest": "#ff303635", "surfaceTint": "#ff82d5c8", "tertiary": "#ffadcae6", "tertiaryContainer": "#ff2d4961"}, "height": 36, "layoutBehavior": "padded", "minWidth": 88, "padding": {"bottom": 0, "left": 16, "right": 16, "top": 0}, "shape": {"borderRadius": {"bottomLeft": {"type": "elliptical", "x": 2, "y": 2}, "bottomRight": {"type": "elliptical", "x": 2, "y": 2}, "topLeft": {"type": "elliptical", "x": 2, "y": 2}, "topRight": {"type": "elliptical", "x": 2, "y": 2}, "type": "only"}, "side": {"color": "#ff000000", "strokeAlign": -1, "style": "none", "width": 0}, "type": "rounded"}, "textTheme": "normal"}, "canvasColor": "#ff0e1513", "cardColor": "#ff0e1513", "colorScheme": {"background": "#ff0e1513", "brightness": "dark", "error": "#ffffb4ab", "errorContainer": "#ff93000a", "inversePrimary": "#ff006a60", "inverseSurface": "#ffdde4e1", "onBackground": "#ffdde4e1", "onError": "#ff690005", "onErrorContainer": "#ffffdad6", "onInverseSurface": "#ff2b3230", "onPrimary": "#ff003731", "onPrimaryContainer": "#ff9ef2e4", "onSecondary": "#ff1c3531", "onSecondaryContainer": "#ffcce8e2", "onSurface": "#ffdde4e1", "onSurfaceVariant": "#ffbec9c6", "onTertiary": "#ff153349", "onTertiaryContainer": "#ffcce5ff", "outline": "#ff899390", "outlineVariant": "#ff3f4947", "primary": "#ff82d5c8", "primaryContainer": "#ff005048", "scrim": "#ff000000", "secondary": "#ffb1ccc6", "secondaryContainer": "#ff334b47", "shadow": "#ff000000", "surface": "#ff0e1513", "surfaceContainerHighest": "#ff303635", "surfaceTint": "#ff82d5c8", "tertiary": "#ffadcae6", "tertiaryContainer": "#ff2d4961"}, "disabledColor": "#62ffffff", "dividerColor": "#ff82d5c8", "focusColor": "#1fffffff", "highlightColor": "#40cccccc", "hintColor": "#99ffffff", "hoverColor": "#0affffff", "iconTheme": {"color": "#ffffffff"}, "indicatorColor": "#ffdde4e1", "inputDecorationTheme": {"alignLabelWithHint": false, "filled": false, "floatingLabelAlignment": "start", "floatingLabelBehavior": "auto", "isCollapsed": false, "isDense": false}, "materialTapTargetSize": "shrinkWrap", "platform": "macOS", "primaryColor": "#ff0e1513", "primaryColorDark": "#ff000000", "primaryColorLight": "#ff9e9e9e", "primaryIconTheme": {"color": "#ffffffff"}, "primaryTextTheme": {"bodyLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 14, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 12, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 96, "fontWeight": "w300", "inherit": false, "letterSpacing": -1.5, "textBaseline": "alphabetic"}, "displayMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 60, "fontWeight": "w300", "inherit": false, "letterSpacing": -0.5, "textBaseline": "alphabetic"}, "displaySmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 48, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 40, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 34, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 24, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 1.25, "textBaseline": "alphabetic"}, "labelMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 11, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "labelSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 10, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "titleLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 20, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "scaffoldBackgroundColor": "#ff000000", "secondaryHeaderColor": "#ff616161", "shadowColor": "#ff000000", "splashColor": "#40cccccc", "splashFactory": "ripple", "textTheme": {"bodyLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 14, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 12, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 96, "fontWeight": "w300", "inherit": false, "letterSpacing": -1.5, "textBaseline": "alphabetic"}, "displayMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 60, "fontWeight": "w300", "inherit": false, "letterSpacing": -0.5, "textBaseline": "alphabetic"}, "displaySmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 48, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 40, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 34, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 24, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 1.25, "textBaseline": "alphabetic"}, "labelMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 11, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "labelSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 10, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "titleLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 20, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "typography": {"black": {"bodyLarge": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "bodyMedium": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "bodySmall": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "displayLarge": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "displayMedium": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "displaySmall": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "headlineLarge": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "headlineMedium": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "headlineSmall": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "labelLarge": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "labelMedium": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "labelSmall": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "titleLarge": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "titleMedium": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "titleSmall": {"color": "#ff0e1513", "decoration": "none", "decorationColor": "#ff0e1513", "fontFamily": ".AppleSystemUIFont", "inherit": true}}, "dense": {"bodyLarge": {"fontSize": 16, "fontWeight": "w400", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "ideographic"}, "bodyMedium": {"fontSize": 14, "fontWeight": "w400", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.25, "textBaseline": "ideographic"}, "bodySmall": {"fontSize": 12, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.4, "textBaseline": "ideographic"}, "displayLarge": {"fontSize": 57, "fontWeight": "w400", "height": 1.12, "inherit": false, "leadingDistribution": "even", "letterSpacing": -0.25, "textBaseline": "ideographic"}, "displayMedium": {"fontSize": 45, "fontWeight": "w400", "height": 1.16, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "displaySmall": {"fontSize": 36, "fontWeight": "w400", "height": 1.22, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "headlineLarge": {"fontSize": 32, "fontWeight": "w400", "height": 1.25, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "headlineMedium": {"fontSize": 28, "fontWeight": "w400", "height": 1.29, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "headlineSmall": {"fontSize": 24, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "labelLarge": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "ideographic"}, "labelMedium": {"fontSize": 12, "fontWeight": "w500", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "ideographic"}, "labelSmall": {"fontSize": 11, "fontWeight": "w500", "height": 1.45, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "ideographic"}, "titleLarge": {"fontSize": 22, "fontWeight": "w400", "height": 1.27, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "titleMedium": {"fontSize": 16, "fontWeight": "w500", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.15, "textBaseline": "ideographic"}, "titleSmall": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "ideographic"}}, "englishLike": {"bodyLarge": {"fontSize": 16, "fontWeight": "w400", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"fontSize": 14, "fontWeight": "w400", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"fontSize": 12, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"fontSize": 57, "fontWeight": "w400", "height": 1.12, "inherit": false, "leadingDistribution": "even", "letterSpacing": -0.25, "textBaseline": "alphabetic"}, "displayMedium": {"fontSize": 45, "fontWeight": "w400", "height": 1.16, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "displaySmall": {"fontSize": 36, "fontWeight": "w400", "height": 1.22, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"fontSize": 32, "fontWeight": "w400", "height": 1.25, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineMedium": {"fontSize": 28, "fontWeight": "w400", "height": 1.29, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineSmall": {"fontSize": 24, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}, "labelMedium": {"fontSize": 12, "fontWeight": "w500", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "labelSmall": {"fontSize": 11, "fontWeight": "w500", "height": 1.45, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "titleLarge": {"fontSize": 22, "fontWeight": "w400", "height": 1.27, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "titleMedium": {"fontSize": 16, "fontWeight": "w500", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "tall": {"bodyLarge": {"fontSize": 16, "fontWeight": "w400", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"fontSize": 14, "fontWeight": "w400", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"fontSize": 12, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"fontSize": 57, "fontWeight": "w400", "height": 1.12, "inherit": false, "leadingDistribution": "even", "letterSpacing": -0.25, "textBaseline": "alphabetic"}, "displayMedium": {"fontSize": 45, "fontWeight": "w400", "height": 1.16, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "displaySmall": {"fontSize": 36, "fontWeight": "w400", "height": 1.22, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"fontSize": 32, "fontWeight": "w400", "height": 1.25, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineMedium": {"fontSize": 28, "fontWeight": "w400", "height": 1.29, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineSmall": {"fontSize": 24, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}, "labelMedium": {"fontSize": 12, "fontWeight": "w500", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "labelSmall": {"fontSize": 11, "fontWeight": "w500", "height": 1.45, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "titleLarge": {"fontSize": 22, "fontWeight": "w400", "height": 1.27, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "titleMedium": {"fontSize": 16, "fontWeight": "w500", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "white": {"bodyLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "bodyMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "bodySmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "displayLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "displayMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "displaySmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "headlineLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "headlineMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "headlineSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "labelLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "labelMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "labelSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "titleLarge": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "titleMedium": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}, "titleSmall": {"color": "#ffdde4e1", "decoration": "none", "decorationColor": "#ffdde4e1", "fontFamily": ".AppleSystemUIFont", "inherit": true}}}, "unselectedWidgetColor": "#b3ffffff", "useMaterial3": true, "visualDensity": "compact"}